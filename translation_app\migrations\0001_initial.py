# Generated by Django 4.2.7 on 2025-05-01 01:57

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ComprehensiveTranslation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('base_word', models.CharField(max_length=255)),
                ('translation', models.CharField(max_length=255)),
                ('part_of_speech', models.CharField(blank=True, max_length=50, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('cultural_notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_comprehensive_translation', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Language',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('code', models.CharField(max_length=10, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
            ],
            options={
                'verbose_name_plural': 'Languages',
            },
        ),
        migrations.CreateModel(
            name='Word',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.CharField(max_length=255)),
                ('pronunciation', models.CharField(blank=True, max_length=255, null=True)),
                ('part_of_speech', models.CharField(blank=True, max_length=50, null=True)),
                ('etymology', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('language', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='words', to='translation_app.language')),
            ],
        ),
        migrations.CreateModel(
            name='TranslationFeedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('original_text', models.TextField()),
                ('translated_text', models.TextField()),
                ('suggested_translation', models.TextField(blank=True, null=True)),
                ('rating', models.IntegerField(blank=True, choices=[(1, 'Poor'), (2, 'Fair'), (3, 'Good'), (4, 'Very Good'), (5, 'Excellent')], null=True)),
                ('comments', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('source_language', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feedback_source', to='translation_app.language')),
                ('target_language', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feedback_target', to='translation_app.language')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='TranslationExample',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('source_text', models.TextField()),
                ('target_text', models.TextField()),
                ('notes', models.TextField(blank=True, null=True)),
                ('comprehensive_translation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='examples', to='translation_app.comprehensivetranslation')),
            ],
        ),
        migrations.CreateModel(
            name='Translation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notes', models.TextField(blank=True, null=True)),
                ('example_sentence', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('source_word', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='translation_app.word')),
                ('target_word', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translated_from', to='translation_app.word')),
            ],
        ),
        migrations.CreateModel(
            name='RelatedWordForm',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('form', models.CharField(max_length=255)),
                ('translation', models.CharField(max_length=255)),
                ('notes', models.TextField(blank=True, null=True)),
                ('comprehensive_translation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='related_forms', to='translation_app.comprehensivetranslation')),
            ],
        ),
        migrations.AddField(
            model_name='comprehensivetranslation',
            name='source_language',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='source_comprehensive', to='translation_app.language'),
        ),
        migrations.AddField(
            model_name='comprehensivetranslation',
            name='target_language',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='target_comprehensive', to='translation_app.language'),
        ),
        migrations.AddIndex(
            model_name='word',
            index=models.Index(fields=['text'], name='translation_text_7524c3_idx'),
        ),
        migrations.AddIndex(
            model_name='word',
            index=models.Index(fields=['language', 'text'], name='translation_languag_8e85eb_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='word',
            unique_together={('language', 'text')},
        ),
        migrations.AddIndex(
            model_name='translation',
            index=models.Index(fields=['source_word'], name='translation_source__6aa41d_idx'),
        ),
        migrations.AddIndex(
            model_name='translation',
            index=models.Index(fields=['target_word'], name='translation_target__74c7d9_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='translation',
            unique_together={('source_word', 'target_word')},
        ),
        migrations.AlterUniqueTogether(
            name='relatedwordform',
            unique_together={('comprehensive_translation', 'form')},
        ),
        migrations.AddIndex(
            model_name='comprehensivetranslation',
            index=models.Index(fields=['base_word'], name='translation_base_wo_b904bb_idx'),
        ),
        migrations.AddIndex(
            model_name='comprehensivetranslation',
            index=models.Index(fields=['source_language', 'target_language'], name='translation_source__ef881f_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='comprehensivetranslation',
            unique_together={('base_word', 'source_language', 'target_language')},
        ),
    ]
