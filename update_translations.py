#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to update translations from the database to static files and attention mechanism.
This script should be run manually or via cron job to process approved translations.
"""

import os
import sys
import django
import logging
import argparse
from datetime import datetime

# Set up Django environment
# Try different possible settings modules
possible_settings_modules = [
    'config.settings',
    'teduray.settings',
    'translation_project.settings',
    'settings',
]

for settings_module in possible_settings_modules:
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', settings_module)
        django.setup()
        print(f"Successfully loaded settings module: {settings_module}")
        break
    except ImportError:
        print(f"Failed to load settings module: {settings_module}")
        if settings_module == possible_settings_modules[-1]:
            raise Exception("Could not find a valid settings module. Please specify the correct settings module.")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def update_translations(options):
    """Update translations based on the provided options"""
    from django.core.management import call_command

    logger.info("Starting translation system update...")

    # Build command arguments for optimize_translation_system
    cmd_args = []

    # Process feedback if requested
    if options.process_feedback:
        logger.info("Will process pending feedback...")
        cmd_args.append('--process_feedback')

        # Also try running the dedicated process_feedback command if it exists
        try:
            logger.info("Running dedicated process_feedback command...")
            call_command('process_feedback')
            logger.info("process_feedback command completed successfully")
        except Exception as e:
            logger.warning(f"Error running process_feedback command: {str(e)}")
            logger.info("Will continue with optimize_translation_system command")

    # Update attention data if requested
    if options.update_attention:
        logger.info("Will update attention data...")
        cmd_args.append('--attention_focus')

    # Update metrics if requested
    if options.update_metrics:
        logger.info("Will update translation metrics...")
        cmd_args.append('--update_metrics')

    # Reload translations if requested
    if options.reload_translations:
        logger.info("Will reload translations...")
        cmd_args.append('--load_translations')

    # Set limit for processing
    if options.limit:
        cmd_args.extend(['--limit', str(options.limit)])
        logger.info(f"Processing limit: {options.limit} translations")

    # Run the optimize_translation_system command with all the specified options
    logger.info(f"Running optimize_translation_system with args: {cmd_args}")
    try:
        call_command('optimize_translation_system', *cmd_args)
    except Exception as e:
        # If the command doesn't exist, try the old command name
        logger.warning(f"Error running optimize_translation_system: {str(e)}")
        logger.info("Trying with optimize_system command instead...")

        # Convert arguments to the format expected by optimize_system
        optimize_kwargs = {}
        if '--process_feedback' in cmd_args:
            optimize_kwargs['process_feedback'] = True
        if '--attention_focus' in cmd_args:
            optimize_kwargs['attention_focus'] = True
        if '--update_metrics' in cmd_args:
            optimize_kwargs['update_metrics'] = True
        if '--load_translations' in cmd_args:
            optimize_kwargs['load_translations'] = True

        # Extract limit if present
        try:
            limit_index = cmd_args.index('--limit')
            if limit_index + 1 < len(cmd_args):
                optimize_kwargs['limit'] = int(cmd_args[limit_index + 1])
        except (ValueError, IndexError):
            pass

        call_command('optimize_system', **optimize_kwargs)

    # Update static files if requested separately
    if options.update_static and not options.reload_translations:
        logger.info("Updating static translation files...")
        try:
            call_command('update_static_translations')
        except Exception as e:
            # If the command doesn't exist, try alternative commands
            logger.warning(f"Error running update_static_translations: {str(e)}")
            logger.info("Trying with export_translations command instead...")
            try:
                call_command('export_translations')
            except Exception as e2:
                logger.warning(f"Error running export_translations: {str(e2)}")
                logger.info("Trying with update_latest_translations command instead...")
                try:
                    call_command('update_latest_translations', hours=24, limit=100)
                except Exception as e3:
                    logger.error(f"All static file update commands failed: {str(e3)}")

        logger.info("Static translation files updated.")

    logger.info("Translation system update completed successfully!")

def main():
    """Main function to parse arguments and run the update"""
    parser = argparse.ArgumentParser(description='Update the translation system')

    parser.add_argument(
        '--process-feedback',
        action='store_true',
        help='Process pending translation feedback'
    )

    parser.add_argument(
        '--update-attention',
        action='store_true',
        help='Update attention data for translations'
    )

    parser.add_argument(
        '--update-metrics',
        action='store_true',
        help='Update translation metrics'
    )

    parser.add_argument(
        '--reload-translations',
        action='store_true',
        help='Reload translations from the database'
    )

    parser.add_argument(
        '--update-static',
        action='store_true',
        help='Update static translation files'
    )

    parser.add_argument(
        '--limit',
        type=int,
        default=100,
        help='Limit the number of translations to process (default: 100)'
    )

    parser.add_argument(
        '--all',
        action='store_true',
        help='Perform all update operations'
    )

    args = parser.parse_args()

    # If --all is specified, set all options to True
    if args.all:
        args.process_feedback = True
        args.update_attention = True
        args.update_metrics = True
        args.reload_translations = True
        args.update_static = True

    # If no options are specified, show help
    if not any([
        args.process_feedback,
        args.update_attention,
        args.update_metrics,
        args.reload_translations,
        args.update_static
    ]):
        parser.print_help()
        sys.exit(1)

    # Run the update
    update_translations(args)

if __name__ == '__main__':
    main()
