"""
URL Configuration for translation project.
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.shortcuts import redirect
from django.contrib.admin.views.decorators import staff_member_required
from translation_app import views

# Customize admin site
admin.site.site_header = settings.ADMIN_SITE_HEADER
admin.site.site_title = settings.ADMIN_SITE_TITLE
admin.site.index_title = settings.ADMIN_INDEX_TITLE
admin.site.site_url = settings.ADMIN_SITE_URL

urlpatterns = [
    path('admin/', admin.site.urls),
    # Add direct API endpoints to ensure they're accessible
    path('api/translate/', views.translate_text, name='translate_text_direct'),
    path('api/feedback/', views.submit_feedback, name='submit_feedback_direct'),
    # Add translation management URL directly
    path('translation-management/',
         staff_member_required(views.translation_management_view),
         name='translation_management'),
    # Include all other app URLs
    path('', include('translation_app.urls')),
]
