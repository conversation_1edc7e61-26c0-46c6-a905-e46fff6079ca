from django.db import models
from django.utils import timezone
from django.contrib.auth.models import User
import json

class Language(models.Model):
    """Model representing a language in the translation system"""
    name = models.CharField(max_length=50, unique=True)
    code = models.CharField(max_length=10, unique=True)
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Languages"

class Word(models.Model):
    """Model representing a word in a specific language"""
    language = models.ForeignKey(Language, on_delete=models.CASCADE, related_name='words')
    text = models.Char<PERSON>ield(max_length=255)
    pronunciation = models.CharField(max_length=255, blank=True, null=True)
    part_of_speech = models.CharField(max_length=50, blank=True, null=True)
    etymology = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return f"{self.text} ({self.language.name})"

    class Meta:
        unique_together = ['language', 'text']
        indexes = [
            models.Index(fields=['text']),
            models.Index(fields=['language', 'text']),
        ]

class Translation(models.Model):
    """Model representing translations between words"""
    source_word = models.ForeignKey(Word, on_delete=models.CASCADE, related_name='translations')
    target_word = models.ForeignKey(Word, on_delete=models.CASCADE, related_name='translated_from')
    notes = models.TextField(blank=True, null=True)
    example_sentence = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return f"{self.source_word.text} ({self.source_word.language.name}) → {self.target_word.text} ({self.target_word.language.name})"

    class Meta:
        unique_together = ['source_word', 'target_word']
        indexes = [
            models.Index(fields=['source_word']),
            models.Index(fields=['target_word']),
        ]

class ComprehensiveTranslation(models.Model):
    """
    Model for storing comprehensive translations with related forms, examples, and cultural context.
    This enables Augment-like rich translations with contextual information.
    """
    base_word = models.CharField(max_length=255)
    translation = models.CharField(max_length=255)
    source_language = models.ForeignKey(Language, on_delete=models.CASCADE, related_name='source_comprehensive')
    target_language = models.ForeignKey(Language, on_delete=models.CASCADE, related_name='target_comprehensive')
    part_of_speech = models.CharField(max_length=50, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    cultural_notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_comprehensive_translation')

    class Meta:
        unique_together = ['base_word', 'source_language', 'target_language']
        indexes = [
            models.Index(fields=['base_word']),
            models.Index(fields=['source_language', 'target_language']),
        ]

    def __str__(self):
        return f"{self.base_word} ({self.source_language.code}) → {self.translation} ({self.target_language.code})"

class RelatedWordForm(models.Model):
    """
    Model for storing related word forms for comprehensive translations.
    """
    comprehensive_translation = models.ForeignKey(ComprehensiveTranslation, on_delete=models.CASCADE, related_name='related_forms')
    form = models.CharField(max_length=255)
    translation = models.CharField(max_length=255)
    notes = models.TextField(blank=True, null=True)

    class Meta:
        unique_together = ['comprehensive_translation', 'form']

    def __str__(self):
        return f"{self.form} → {self.translation}"

class TranslationExample(models.Model):
    """
    Model for storing example sentences for comprehensive translations.
    """
    comprehensive_translation = models.ForeignKey(ComprehensiveTranslation, on_delete=models.CASCADE, related_name='examples')
    source_text = models.TextField()
    target_text = models.TextField()
    notes = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"{self.source_text[:30]}... → {self.target_text[:30]}..."

class TranslationVersion(models.Model):
    """
    Model to track versions of translations for version control and history.
    """
    comprehensive_translation = models.ForeignKey(ComprehensiveTranslation, on_delete=models.CASCADE, related_name='versions')
    translation = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.CharField(max_length=255, default='system')  # 'system', 'user', 'admin', 'feedback'
    confidence_score = models.FloatField(default=0.95)
    is_active = models.BooleanField(default=True)
    notes = models.TextField(blank=True, null=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Version of {self.comprehensive_translation.base_word[:30]} created at {self.created_at}"

class TranslationSuggestion(models.Model):
    """
    Model for storing suggested changes to translations that require review.
    """
    STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ]

    original_text = models.TextField()
    system_translation = models.TextField()
    suggested_translation = models.TextField()
    source_language = models.ForeignKey(Language, on_delete=models.CASCADE, related_name='suggestion_source')
    target_language = models.ForeignKey(Language, on_delete=models.CASCADE, related_name='suggestion_target')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    confidence_score = models.FloatField(default=0.8)
    notes = models.TextField(blank=True, null=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    reviewed_at = models.DateTimeField(null=True, blank=True)
    reviewed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='reviewed_suggestions')

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Suggestion for: {self.original_text[:30]}... ({self.status})"

class TranslationFeedback(models.Model):
    """
    Model for storing user feedback on translations.
    """
    original_text = models.TextField()
    translated_text = models.TextField()
    suggested_translation = models.TextField(blank=True, null=True)
    source_language = models.ForeignKey(Language, on_delete=models.CASCADE, related_name='feedback_source')
    target_language = models.ForeignKey(Language, on_delete=models.CASCADE, related_name='feedback_target')
    rating = models.IntegerField(choices=[
        (1, 'Poor'),
        (2, 'Fair'),
        (3, 'Good'),
        (4, 'Very Good'),
        (5, 'Excellent'),
    ], null=True, blank=True)
    comments = models.TextField(blank=True, null=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    processed = models.BooleanField(default=False)

    def __str__(self):
        return f"Feedback on: {self.original_text[:30]}..."

class TranslationMetrics(models.Model):
    """
    Model for tracking translation metrics including BLEU scores and other statistics.
    """
    date = models.DateField(default=timezone.now)
    source_language = models.ForeignKey(Language, on_delete=models.CASCADE, related_name='source_metrics')
    target_language = models.ForeignKey(Language, on_delete=models.CASCADE, related_name='target_metrics')

    # Translation volume metrics
    words_translated = models.IntegerField(default=0)
    characters_translated = models.IntegerField(default=0)
    sentences_translated = models.IntegerField(default=0)

    # Quality metrics
    bleu_score = models.FloatField(default=0.0)  # BLEU score (0.0-1.0)
    meteor_score = models.FloatField(default=0.0, null=True, blank=True)  # METEOR score if available

    # User feedback metrics
    average_rating = models.FloatField(default=0.0)
    feedback_count = models.IntegerField(default=0)

    # System performance metrics
    average_confidence = models.FloatField(default=0.0)

    # Additional metrics stored as JSON
    additional_metrics = models.TextField(blank=True, null=True)

    class Meta:
        unique_together = ['date', 'source_language', 'target_language']
        ordering = ['-date']
        verbose_name = 'Translation Metrics'
        verbose_name_plural = 'Translation Metrics'

    def __str__(self):
        return f"Metrics for {self.source_language.code} → {self.target_language.code} on {self.date}"

    def get_additional_metrics(self):
        """Get additional metrics as a dictionary"""
        if self.additional_metrics:
            return json.loads(self.additional_metrics)
        return {}

    def set_additional_metrics(self, metrics_dict):
        """Set additional metrics from a dictionary"""
        self.additional_metrics = json.dumps(metrics_dict)

class AttentionData(models.Model):
    """
    Model for storing attention mechanism data for translations.
    This helps track which parts of source text correspond to which parts of target text.
    """
    source_text = models.TextField()
    target_text = models.TextField()
    source_language = models.ForeignKey(Language, on_delete=models.CASCADE, related_name='source_attention')
    target_language = models.ForeignKey(Language, on_delete=models.CASCADE, related_name='target_attention')

    # Attention weights stored as JSON
    # Format: [{"source_idx": 0, "target_idx": 1, "weight": 0.8}, ...]
    attention_weights = models.TextField()

    # Previous attention weights for before/after comparison
    previous_attention_weights = models.TextField(null=True, blank=True)

    # Attention improvement metrics
    word_level_improvement = models.FloatField(default=0.0)  # Word-level improvement percentage
    sentence_level_improvement = models.FloatField(default=0.0)  # Sentence-level improvement percentage
    phrase_level_improvement = models.FloatField(default=0.0)  # Phrase-level improvement percentage
    attention_improvement = models.FloatField(default=0.0)  # Overall improvement percentage

    # Performance metrics
    processing_time = models.FloatField(default=0.0)  # Time taken to process in seconds
    confidence_score = models.FloatField(default=0.7)  # Confidence score (0.0-1.0)
    bleu_score = models.FloatField(default=0.7)  # BLEU score (0.0-1.0)

    # Sentence type classification
    SENTENCE_TYPE_CHOICES = [
        ('declarative', 'Declarative'),
        ('interrogative', 'Interrogative'),
        ('imperative', 'Imperative'),
        ('exclamatory', 'Exclamatory'),
        ('other', 'Other')
    ]
    sentence_type = models.CharField(max_length=20, choices=SENTENCE_TYPE_CHOICES, default='declarative')

    # Relationship to comprehensive translation
    comprehensive_translation = models.ForeignKey(ComprehensiveTranslation, on_delete=models.CASCADE,
                                                related_name='attention_data', null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class AITranslationReview(models.Model):
    """
    Model for storing AI-generated translations that need review before being added to the main database.
    """
    STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('modified', 'Modified and Approved')
    ]

    source_text = models.TextField()
    ai_translation = models.TextField()
    modified_translation = models.TextField(blank=True, null=True)
    source_language = models.ForeignKey(Language, on_delete=models.CASCADE, related_name='ai_review_source')
    target_language = models.ForeignKey(Language, on_delete=models.CASCADE, related_name='ai_review_target')
    confidence = models.FloatField(default=0.0)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    reviewed_at = models.DateTimeField(null=True, blank=True)
    reviewed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    notes = models.TextField(blank=True, null=True)
    ai_model = models.CharField(max_length=100, default='huggingface')
    translation_id = models.IntegerField(null=True, blank=True)  # Optional reference to a translation

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['source_language', 'target_language']),
        ]

    def __str__(self):
        return f"{self.source_text[:30]} → {self.ai_translation[:30]} ({self.status})"

    def approve(self, user=None):
        """Approve the AI translation and add it to the database"""
        from django.utils import timezone

        self.status = 'approved'
        self.reviewed_at = timezone.now()
        self.reviewed_by = user
        self.save()

        # Add to comprehensive translations if it doesn't exist
        try:
            translation, created = ComprehensiveTranslation.objects.get_or_create(
                base_word=self.source_text,
                source_language=self.source_language,
                target_language=self.target_language,
                defaults={
                    'translation': self.ai_translation,
                    'part_of_speech': 'phrase' if ' ' in self.source_text else 'word',
                    'notes': f'AI-generated translation (approved on {timezone.now().strftime("%Y-%m-%d")})'
                }
            )

            if not created:
                # Create a version record of the current translation
                TranslationVersion.objects.create(
                    comprehensive_translation=translation,
                    translation=translation.translation,
                    created_by='system',
                    confidence_score=0.95,
                    is_active=False,
                    notes='Archived before AI translation approval'
                )

                # Update with the AI translation
                translation.translation = self.ai_translation
                translation.notes = (translation.notes or '') + f'\nAI-generated translation approved on {timezone.now().strftime("%Y-%m-%d")}'
                translation.save()

            # Create a new version for the AI translation
            TranslationVersion.objects.create(
                comprehensive_translation=translation,
                translation=self.ai_translation,
                created_by='ai',
                confidence_score=self.confidence,
                is_active=True,
                notes=f'AI-generated translation approved by {user.username if user else "system"}'
            )

            return True
        except Exception as e:
            import logging
            logging.error(f"Error approving AI translation: {str(e)}")
            return False

    def approve_with_modifications(self, modified_translation, user=None):
        """Approve with modifications and add to the database"""
        from django.utils import timezone

        self.status = 'modified'
        self.modified_translation = modified_translation
        self.reviewed_at = timezone.now()
        self.reviewed_by = user
        self.save()

        # Add to comprehensive translations if it doesn't exist
        try:
            translation, created = ComprehensiveTranslation.objects.get_or_create(
                base_word=self.source_text,
                source_language=self.source_language,
                target_language=self.target_language,
                defaults={
                    'translation': modified_translation,
                    'part_of_speech': 'phrase' if ' ' in self.source_text else 'word',
                    'notes': f'AI-generated translation (modified and approved on {timezone.now().strftime("%Y-%m-%d")})'
                }
            )

            if not created:
                # Create a version record of the current translation
                TranslationVersion.objects.create(
                    comprehensive_translation=translation,
                    translation=translation.translation,
                    created_by='system',
                    confidence_score=0.95,
                    is_active=False,
                    notes='Archived before modified AI translation approval'
                )

                # Update with the modified translation
                translation.translation = modified_translation
                translation.notes = (translation.notes or '') + f'\nModified AI translation approved on {timezone.now().strftime("%Y-%m-%d")}'
                translation.save()

            # Create a new version for the modified translation
            TranslationVersion.objects.create(
                comprehensive_translation=translation,
                translation=modified_translation,
                created_by='ai_modified',
                confidence_score=0.9,
                is_active=True,
                notes=f'Modified AI translation approved by {user.username if user else "system"}'
            )

            return True
        except Exception as e:
            import logging
            logging.error(f"Error approving modified AI translation: {str(e)}")
            return False

    # Metadata for attention weights

    def get_attention_weights(self):
        """Get attention weights as a list of dictionaries"""
        return json.loads(self.attention_weights)

    def set_attention_weights(self, weights_list):
        """Set attention weights from a list of dictionaries"""
        self.attention_weights = json.dumps(weights_list)

    def get_previous_attention_weights(self):
        """Get previous attention weights as a list of dictionaries"""
        if self.previous_attention_weights:
            return json.loads(self.previous_attention_weights)
        return []

    def set_previous_attention_weights(self, weights_list):
        """Set previous attention weights from a list of dictionaries"""
        self.previous_attention_weights = json.dumps(weights_list)

    def calculate_improvements(self, previous_weights=None, current_weights=None):
        """
        Calculate improvement metrics between previous and current attention weights

        Args:
            previous_weights: Optional previous weights (if not stored in model)
            current_weights: Optional current weights (if not stored in model)

        Returns:
            dict: Dictionary with improvement metrics
        """
        prev_weights = previous_weights or self.get_previous_attention_weights()
        curr_weights = current_weights or self.get_attention_weights()

        if not prev_weights or not curr_weights:
            return {
                'attention_improvement': 0.0,
                'word_level_improvement': 0.0,
                'sentence_level_improvement': 0.0,
                'phrase_level_improvement': 0.0
            }

        # Calculate word-level improvement (average weight increase per word)
        prev_avg = sum(w.get('weight', 0) for w in prev_weights) / max(1, len(prev_weights))
        curr_avg = sum(w.get('weight', 0) for w in curr_weights) / max(1, len(curr_weights))
        word_improvement = ((curr_avg - prev_avg) / max(0.01, prev_avg)) * 100

        # Calculate sentence-level improvement (overall coherence)
        # For sentence level, we look at the max weight for each target token
        prev_max_weights = {}
        curr_max_weights = {}

        for w in prev_weights:
            target_idx = w.get('target_idx')
            weight = w.get('weight', 0)
            prev_max_weights[target_idx] = max(prev_max_weights.get(target_idx, 0), weight)

        for w in curr_weights:
            target_idx = w.get('target_idx')
            weight = w.get('weight', 0)
            curr_max_weights[target_idx] = max(curr_max_weights.get(target_idx, 0), weight)

        prev_sentence_avg = sum(prev_max_weights.values()) / max(1, len(prev_max_weights))
        curr_sentence_avg = sum(curr_max_weights.values()) / max(1, len(curr_max_weights))
        sentence_improvement = ((curr_sentence_avg - prev_sentence_avg) / max(0.01, prev_sentence_avg)) * 100

        # Calculate phrase-level improvement (consecutive tokens)
        # For phrase level, we look at pairs of consecutive tokens
        prev_phrase_scores = []
        curr_phrase_scores = []

        # Group weights by target token
        prev_by_target = {}
        curr_by_target = {}

        for w in prev_weights:
            target_idx = w.get('target_idx')
            if target_idx not in prev_by_target:
                prev_by_target[target_idx] = []
            prev_by_target[target_idx].append(w)

        for w in curr_weights:
            target_idx = w.get('target_idx')
            if target_idx not in curr_by_target:
                curr_by_target[target_idx] = []
            curr_by_target[target_idx].append(w)

        # Calculate phrase coherence for consecutive tokens
        for i in range(max(prev_by_target.keys(), default=-1)):
            if i in prev_by_target and i+1 in prev_by_target:
                # Find max weight for each token in the pair
                max_i = max(w.get('weight', 0) for w in prev_by_target[i])
                max_i1 = max(w.get('weight', 0) for w in prev_by_target[i+1])
                prev_phrase_scores.append((max_i + max_i1) / 2)

        for i in range(max(curr_by_target.keys(), default=-1)):
            if i in curr_by_target and i+1 in curr_by_target:
                # Find max weight for each token in the pair
                max_i = max(w.get('weight', 0) for w in curr_by_target[i])
                max_i1 = max(w.get('weight', 0) for w in curr_by_target[i+1])
                curr_phrase_scores.append((max_i + max_i1) / 2)

        prev_phrase_avg = sum(prev_phrase_scores) / max(1, len(prev_phrase_scores))
        curr_phrase_avg = sum(curr_phrase_scores) / max(1, len(curr_phrase_scores))
        phrase_improvement = ((curr_phrase_avg - prev_phrase_avg) / max(0.01, prev_phrase_avg)) * 100

        # Calculate overall improvement (weighted average)
        overall_improvement = (
            word_improvement * 0.3 +
            sentence_improvement * 0.4 +
            phrase_improvement * 0.3
        )

        # Update model fields
        self.word_level_improvement = word_improvement
        self.sentence_level_improvement = sentence_improvement
        self.phrase_level_improvement = phrase_improvement
        self.attention_improvement = overall_improvement

        return {
            'attention_improvement': overall_improvement,
            'word_level_improvement': word_improvement,
            'sentence_level_improvement': sentence_improvement,
            'phrase_level_improvement': phrase_improvement
        }

    def detect_sentence_type(self):
        """
        Detect the type of sentence based on the source text

        Returns:
            str: Sentence type (declarative, interrogative, imperative, exclamatory, other)
        """
        text = self.source_text.strip()

        # Check for interrogative sentences (questions)
        if text.endswith('?') or text.lower().startswith(('what', 'who', 'where', 'when', 'why', 'how', 'is', 'are', 'do', 'does', 'did', 'can', 'could', 'will', 'would')):
            return 'interrogative'

        # Check for exclamatory sentences
        if text.endswith('!'):
            return 'exclamatory'

        # Check for imperative sentences (commands)
        if text.lower().split()[0] in ('go', 'do', 'make', 'try', 'let', 'please', 'help', 'show', 'tell', 'give', 'find', 'use', 'look', 'come', 'stop'):
            return 'imperative'

        # Default to declarative
        return 'declarative'

class TranslationRating(models.Model):
    """
    Model for storing user ratings on translations.
    """
    comprehensive_translation = models.ForeignKey(ComprehensiveTranslation, on_delete=models.CASCADE, related_name='ratings')
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    rating = models.IntegerField(choices=[(1, '1 - Poor'), (2, '2 - Fair'), (3, '3 - Good'), (4, '4 - Very Good'), (5, '5 - Excellent')])
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Rating {self.rating}/5 for {self.comprehensive_translation}"

class BleuScoreHistory(models.Model):
    """
    Model for tracking BLEU score history over time for specific translation pairs.
    """
    date = models.DateField(default=timezone.now)
    source_language = models.ForeignKey(Language, on_delete=models.CASCADE, related_name='source_bleu')
    target_language = models.ForeignKey(Language, on_delete=models.CASCADE, related_name='target_bleu')

    # BLEU scores
    bleu_score = models.FloatField(default=0.0)  # Overall BLEU score
    bleu_1gram = models.FloatField(default=0.0)  # 1-gram precision
    bleu_2gram = models.FloatField(default=0.0)  # 2-gram precision
    bleu_3gram = models.FloatField(default=0.0)  # 3-gram precision
    bleu_4gram = models.FloatField(default=0.0)  # 4-gram precision

    # F1 score
    f1_score = models.FloatField(default=0.0)  # F1 measure

    # Reference data
    reference_count = models.IntegerField(default=0)  # Number of reference translations used
    test_set_size = models.IntegerField(default=0)  # Number of sentences in test set

    # Notes
    notes = models.TextField(blank=True, null=True)

    class Meta:
        unique_together = ['date', 'source_language', 'target_language']
        ordering = ['-date']
        verbose_name = 'BLEU Score History'
        verbose_name_plural = 'BLEU Score History'

    def __str__(self):
        return f"BLEU score for {self.source_language.code} → {self.target_language.code} on {self.date}: {self.bleu_score:.4f}"
