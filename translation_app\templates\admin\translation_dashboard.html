{% extends "admin/base_site.html" %}
{% load i18n %}

{% block title %}{% trans "Translation Dashboard" %} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; {% trans 'Translation Dashboard' %}
</div>
{% endblock %}

{% block content %}
<div id="content-main">
    <style>
        .dashboard-container {
            margin-bottom: 20px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        
        .dashboard-container h1 {
            margin-top: 0;
            color: #343a40;
            font-size: 1.8rem;
            margin-bottom: 20px;
        }
        
        .dashboard-container h2 {
            margin-top: 0;
            color: #343a40;
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        
        .button-row {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .button-container {
            flex: 1 1 calc(50% - 15px);
            min-width: 250px;
            background-color: white;
            border-radius: 5px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .button-container h3 {
            margin-top: 0;
            color: #343a40;
            font-size: 1.2rem;
            margin-bottom: 10px;
        }
        
        .button-container p {
            color: #6c757d;
            margin-bottom: 15px;
            font-size: 0.9rem;
        }
        
        .button {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: 500;
            text-decoration: none;
            color: white;
            cursor: pointer;
            border: none;
        }
        
        .button-success {
            background-color: #28a745;
        }
        
        .button-primary {
            background-color: #007bff;
        }
        
        .button-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .button-info {
            background-color: #17a2b8;
        }
        
        @media (max-width: 768px) {
            .button-container {
                flex: 1 1 100%;
            }
        }
    </style>

    <div class="dashboard-container">
        <h1>{% trans "Translation System Management" %}</h1>
        
        <div class="button-row">
            <div class="button-container">
                <h3>{% trans "Update Attention Mechanism" %}</h3>
                <p>{% trans "Enhance translations using the attention mechanism for better quality with longer sentences and phrases." %}</p>
                <form method="post" action="{% url 'admin:update_attention' %}">
                    {% csrf_token %}
                    <button type="submit" class="button button-success">{% trans "Update Attention" %}</button>
                </form>
            </div>
            
            <div class="button-container">
                <h3>{% trans "Update Static Files" %}</h3>
                <p>{% trans "Update static translation files used by the frontend for faster access without database queries." %}</p>
                <form method="post" action="{% url 'admin:update_static' %}">
                    {% csrf_token %}
                    <button type="submit" class="button button-primary">{% trans "Update Static Files" %}</button>
                </form>
            </div>
        </div>
        
        <div class="button-row">
            <div class="button-container">
                <h3>{% trans "Process Translation Feedback" %}</h3>
                <p>{% trans "Process pending translation feedback and generate suggestions from user input to improve the system." %}</p>
                <form method="post" action="{% url 'admin:update_feedback' %}">
                    {% csrf_token %}
                    <button type="submit" class="button button-warning">{% trans "Process Feedback" %}</button>
                </form>
            </div>
            
            <div class="button-container">
                <h3>{% trans "Update Translation System" %}</h3>
                <p>{% trans "Update the entire translation system with all processed translations, reload the service and update metrics." %}</p>
                <form method="post" action="{% url 'admin:update_translations' %}">
                    {% csrf_token %}
                    <button type="submit" class="button button-info">{% trans "Update System" %}</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
