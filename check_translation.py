import os
import django
import json

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from translation_app.models import Language, ComprehensiveTranslation

# Check if the translation exists in the database
def check_translation_in_db(word, source_lang_code, target_lang_code):
    try:
        source_lang = Language.objects.get(code=source_lang_code)
        target_lang = Language.objects.get(code=target_lang_code)
        
        translation = ComprehensiveTranslation.objects.filter(
            base_word=word,
            source_language=source_lang,
            target_language=target_lang
        ).first()
        
        if translation:
            print(f"Found in database: {word} → {translation.translation}")
            print(f"Part of speech: {translation.part_of_speech}")
            print(f"Notes: {translation.notes}")
            print(f"Updated at: {translation.updated_at}")
            return True
        else:
            print(f"Not found in database: {word}")
            return False
    except Exception as e:
        print(f"Error checking database: {str(e)}")
        return False

# Check if the translation exists in the static file
def check_translation_in_static_file(word, source_lang_code, target_lang_code):
    try:
        # Try different possible paths for the static file
        static_file_paths = [
            'static/translation_app/js/all_translations.js',
            'translation_app/static/translation_app/js/all_translations.js'
        ]
        
        static_file_path = None
        for path in static_file_paths:
            if os.path.exists(path):
                static_file_path = path
                break
        
        if not static_file_path:
            print("Static file not found")
            return False
        
        # Read the static file
        with open(static_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract the JSON part
        start_index = content.find('const ALL_TRANSLATIONS = ')
        if start_index == -1:
            print("ALL_TRANSLATIONS variable not found in static file")
            return False
        
        json_start = content.find('{', start_index)
        json_end = content.rfind('}', json_start) + 1
        
        if json_start == -1 or json_end == -1:
            print("JSON object boundaries not found in static file")
            return False
        
        # Extract and parse the JSON
        json_str = content[json_start:json_end]
        translations_dict = json.loads(json_str)
        
        # Determine the direction
        if source_lang_code == 'tgl' and target_lang_code == 'ted':
            direction = 'tgl_to_ted'
        elif source_lang_code == 'ted' and target_lang_code == 'tgl':
            direction = 'ted_to_tgl'
        else:
            print(f"Unsupported language pair: {source_lang_code} to {target_lang_code}")
            return False
        
        # Check if the word exists in the translations dictionary
        if direction in translations_dict and word.lower() in translations_dict[direction]:
            translation_info = translations_dict[direction][word.lower()]
            print(f"Found in static file: {word} → {translation_info['translation']}")
            print(f"Confidence: {translation_info.get('confidence', 'N/A')}")
            print(f"Source: {translation_info.get('source', 'N/A')}")
            return True
        else:
            print(f"Not found in static file: {word}")
            return False
    except Exception as e:
        print(f"Error checking static file: {str(e)}")
        return False

# Check the fast lookup mechanism
def check_fast_lookup(word, source_lang_code, target_lang_code):
    try:
        from translation_app.fast_lookup import KNOWN_WORD_PAIRS
        
        # Determine the direction
        if source_lang_code == 'tgl' and target_lang_code == 'ted':
            direction = 'tgl_to_ted'
        elif source_lang_code == 'ted' and target_lang_code == 'tgl':
            direction = 'ted_to_tgl'
        else:
            print(f"Unsupported language pair: {source_lang_code} to {target_lang_code}")
            return False
        
        # Check if the word exists in the known word pairs
        if direction in KNOWN_WORD_PAIRS and word.lower() in KNOWN_WORD_PAIRS[direction]:
            translation = KNOWN_WORD_PAIRS[direction][word.lower()]
            print(f"Found in fast lookup: {word} → {translation}")
            return True
        else:
            print(f"Not found in fast lookup: {word}")
            return False
    except Exception as e:
        print(f"Error checking fast lookup: {str(e)}")
        return False

# Check the translation service
def check_translation_service(word, source_lang_code, target_lang_code):
    try:
        from translation_app.services import get_translation_service
        
        # Get the translation service
        translation_service = get_translation_service()
        
        # Try to translate the word
        result = translation_service.translate_text(word, source_lang_code, target_lang_code)
        
        print(f"Translation service result:")
        print(f"Translation: {result.get('translation', 'N/A')}")
        print(f"Confidence: {result.get('confidence', 'N/A')}")
        print(f"Source: {result.get('source', 'N/A')}")
        
        return True
    except Exception as e:
        print(f"Error using translation service: {str(e)}")
        return False

# Check the word
word = "nagsisisi"
source_lang = "tgl"
target_lang = "ted"

print(f"Checking translation for: {word} ({source_lang} → {target_lang})")
print("\n1. Database check:")
check_translation_in_db(word, source_lang, target_lang)

print("\n2. Static file check:")
check_translation_in_static_file(word, source_lang, target_lang)

print("\n3. Fast lookup check:")
check_fast_lookup(word, source_lang, target_lang)

print("\n4. Translation service check:")
check_translation_service(word, source_lang, target_lang)
