"""
BLEU Score Module for Translation System

This module provides utilities for calculating BLEU scores
and tracking translation quality over time.
"""

import re
import math
import datetime
from collections import Counter
from typing import List, Dict, Tuple, Any, Union
from django.utils import timezone
from django.db.models import Avg

from .models import BleuScoreHistory, TranslationMetrics, Language

class BleuScoreCalculator:
    """
    Class for calculating BLEU scores and tracking translation quality.
    """
    
    def __init__(self):
        """Initialize the BLEU score calculator"""
        self.reference_translations = {}
        
    def calculate_bleu(self, candidate: str, references: List[str], 
                      max_ngram: int = 4) -> Dict[str, float]:
        """
        Calculate BLEU score for a candidate translation against reference translations.
        
        Args:
            candidate: Candidate translation
            references: List of reference translations
            max_ngram: Maximum n-gram size to consider (default: 4)
            
        Returns:
            dict: BLEU scores including overall score and n-gram precisions
        """
        # Tokenize
        candidate_tokens = self._tokenize(candidate)
        reference_tokens = [self._tokenize(ref) for ref in references]
        
        # Calculate n-gram precisions
        precisions = []
        for n in range(1, max_ngram + 1):
            precision = self._calculate_ngram_precision(candidate_tokens, reference_tokens, n)
            precisions.append(precision)
        
        # Calculate brevity penalty
        bp = self._calculate_brevity_penalty(candidate_tokens, reference_tokens)
        
        # Calculate final BLEU score
        if all(p > 0 for p in precisions):
            bleu = bp * math.exp(sum(math.log(p) for p in precisions) / max_ngram)
        else:
            bleu = 0.0
        
        # Return scores
        return {
            'bleu': bleu,
            'precisions': precisions,
            'brevity_penalty': bp,
            'candidate_length': len(candidate_tokens),
            'reference_lengths': [len(ref) for ref in reference_tokens]
        }
    
    def _tokenize(self, text: str) -> List[str]:
        """
        Tokenize text for BLEU calculation.
        
        Args:
            text: Text to tokenize
            
        Returns:
            list: Tokenized text
        """
        # Simple tokenization by whitespace and punctuation
        text = text.lower()
        text = re.sub(r'([.,!?;:])', r' \1 ', text)
        return text.split()
    
    def _calculate_ngram_precision(self, candidate_tokens: List[str], 
                                 reference_tokens: List[List[str]], 
                                 n: int) -> float:
        """
        Calculate n-gram precision for BLEU score.
        
        Args:
            candidate_tokens: Tokenized candidate translation
            reference_tokens: List of tokenized reference translations
            n: n-gram size
            
        Returns:
            float: n-gram precision
        """
        # Extract n-grams from candidate
        candidate_ngrams = self._get_ngrams(candidate_tokens, n)
        
        if not candidate_ngrams:
            return 0.0
        
        # Count n-grams in candidate
        candidate_counts = Counter(candidate_ngrams)
        
        # Calculate maximum reference counts for each n-gram
        max_reference_counts = Counter()
        for reference in reference_tokens:
            reference_ngrams = self._get_ngrams(reference, n)
            reference_counts = Counter(reference_ngrams)
            
            for ngram, count in reference_counts.items():
                max_reference_counts[ngram] = max(max_reference_counts[ngram], count)
        
        # Calculate clipped counts
        clipped_counts = {ngram: min(count, max_reference_counts[ngram]) 
                         for ngram, count in candidate_counts.items()}
        
        # Calculate precision
        numerator = sum(clipped_counts.values())
        denominator = sum(candidate_counts.values())
        
        return numerator / denominator if denominator > 0 else 0.0
    
    def _get_ngrams(self, tokens: List[str], n: int) -> List[Tuple[str, ...]]:
        """
        Extract n-grams from a list of tokens.
        
        Args:
            tokens: List of tokens
            n: n-gram size
            
        Returns:
            list: List of n-grams as tuples
        """
        return [tuple(tokens[i:i+n]) for i in range(len(tokens) - n + 1)]
    
    def _calculate_brevity_penalty(self, candidate_tokens: List[str], 
                                 reference_tokens: List[List[str]]) -> float:
        """
        Calculate brevity penalty for BLEU score.
        
        Args:
            candidate_tokens: Tokenized candidate translation
            reference_tokens: List of tokenized reference translations
            
        Returns:
            float: Brevity penalty
        """
        c_len = len(candidate_tokens)
        
        # Find closest reference length
        r_lens = [len(ref) for ref in reference_tokens]
        closest_r_len = min(r_lens, key=lambda x: abs(x - c_len))
        
        # Calculate brevity penalty
        if c_len >= closest_r_len:
            return 1.0
        else:
            return math.exp(1 - closest_r_len / c_len)
    
    def record_bleu_score(self, source_lang_code: str, target_lang_code: str, 
                         bleu_data: Dict[str, Any], notes: str = None) -> BleuScoreHistory:
        """
        Record BLEU score in the database.
        
        Args:
            source_lang_code: Source language code
            target_lang_code: Target language code
            bleu_data: BLEU score data
            notes: Optional notes
            
        Returns:
            BleuScoreHistory: Recorded BLEU score history object
        """
        try:
            source_language = Language.objects.get(code=source_lang_code)
            target_language = Language.objects.get(code=target_lang_code)
            
            # Create or update BLEU score history
            today = timezone.now().date()
            bleu_history, created = BleuScoreHistory.objects.get_or_create(
                date=today,
                source_language=source_language,
                target_language=target_language,
                defaults={
                    'bleu_score': bleu_data['bleu'],
                    'bleu_1gram': bleu_data['precisions'][0] if len(bleu_data['precisions']) > 0 else 0.0,
                    'bleu_2gram': bleu_data['precisions'][1] if len(bleu_data['precisions']) > 1 else 0.0,
                    'bleu_3gram': bleu_data['precisions'][2] if len(bleu_data['precisions']) > 2 else 0.0,
                    'bleu_4gram': bleu_data['precisions'][3] if len(bleu_data['precisions']) > 3 else 0.0,
                    'reference_count': len(bleu_data.get('reference_lengths', [])),
                    'test_set_size': 1,  # Single translation
                    'notes': notes
                }
            )
            
            if not created:
                # Update existing record
                bleu_history.bleu_score = (bleu_history.bleu_score * bleu_history.test_set_size + bleu_data['bleu']) / (bleu_history.test_set_size + 1)
                bleu_history.bleu_1gram = (bleu_history.bleu_1gram * bleu_history.test_set_size + bleu_data['precisions'][0]) / (bleu_history.test_set_size + 1) if len(bleu_data['precisions']) > 0 else bleu_history.bleu_1gram
                bleu_history.bleu_2gram = (bleu_history.bleu_2gram * bleu_history.test_set_size + bleu_data['precisions'][1]) / (bleu_history.test_set_size + 1) if len(bleu_data['precisions']) > 1 else bleu_history.bleu_2gram
                bleu_history.bleu_3gram = (bleu_history.bleu_3gram * bleu_history.test_set_size + bleu_data['precisions'][2]) / (bleu_history.test_set_size + 1) if len(bleu_data['precisions']) > 2 else bleu_history.bleu_3gram
                bleu_history.bleu_4gram = (bleu_history.bleu_4gram * bleu_history.test_set_size + bleu_data['precisions'][3]) / (bleu_history.test_set_size + 1) if len(bleu_data['precisions']) > 3 else bleu_history.bleu_4gram
                bleu_history.test_set_size += 1
                bleu_history.save()
            
            # Update translation metrics
            self._update_translation_metrics(source_language, target_language, bleu_data['bleu'])
            
            return bleu_history
            
        except Language.DoesNotExist:
            print(f"Language not found: {source_lang_code} or {target_lang_code}")
            return None
        except Exception as e:
            print(f"Error recording BLEU score: {str(e)}")
            return None
    
    def _update_translation_metrics(self, source_language: Language, 
                                  target_language: Language, 
                                  bleu_score: float) -> None:
        """
        Update translation metrics with BLEU score.
        
        Args:
            source_language: Source language
            target_language: Target language
            bleu_score: BLEU score
        """
        today = timezone.now().date()
        
        try:
            # Get or create metrics for today
            metrics, created = TranslationMetrics.objects.get_or_create(
                date=today,
                source_language=source_language,
                target_language=target_language,
                defaults={
                    'bleu_score': bleu_score,
                    'words_translated': 0,
                    'characters_translated': 0,
                    'sentences_translated': 0
                }
            )
            
            if not created:
                # Update BLEU score as a running average
                metrics.bleu_score = (metrics.bleu_score + bleu_score) / 2
                metrics.save()
                
        except Exception as e:
            print(f"Error updating translation metrics: {str(e)}")
    
    def get_bleu_history(self, source_lang_code: str, target_lang_code: str, 
                        days: int = 30) -> List[Dict[str, Any]]:
        """
        Get BLEU score history for a language pair.
        
        Args:
            source_lang_code: Source language code
            target_lang_code: Target language code
            days: Number of days to include (default: 30)
            
        Returns:
            list: BLEU score history as a list of dictionaries
        """
        try:
            source_language = Language.objects.get(code=source_lang_code)
            target_language = Language.objects.get(code=target_lang_code)
            
            # Calculate date range
            end_date = timezone.now().date()
            start_date = end_date - datetime.timedelta(days=days)
            
            # Get BLEU score history
            history = BleuScoreHistory.objects.filter(
                date__gte=start_date,
                date__lte=end_date,
                source_language=source_language,
                target_language=target_language
            ).order_by('date')
            
            # Convert to list of dictionaries
            result = []
            for entry in history:
                result.append({
                    'date': entry.date.isoformat(),
                    'bleu_score': entry.bleu_score,
                    'bleu_1gram': entry.bleu_1gram,
                    'bleu_2gram': entry.bleu_2gram,
                    'bleu_3gram': entry.bleu_3gram,
                    'bleu_4gram': entry.bleu_4gram,
                    'reference_count': entry.reference_count,
                    'test_set_size': entry.test_set_size
                })
            
            return result
            
        except Language.DoesNotExist:
            print(f"Language not found: {source_lang_code} or {target_lang_code}")
            return []
        except Exception as e:
            print(f"Error getting BLEU score history: {str(e)}")
            return []
    
    def get_average_bleu(self, source_lang_code: str, target_lang_code: str, 
                       days: int = 30) -> float:
        """
        Get average BLEU score for a language pair over a period.
        
        Args:
            source_lang_code: Source language code
            target_lang_code: Target language code
            days: Number of days to include (default: 30)
            
        Returns:
            float: Average BLEU score
        """
        try:
            source_language = Language.objects.get(code=source_lang_code)
            target_language = Language.objects.get(code=target_lang_code)
            
            # Calculate date range
            end_date = timezone.now().date()
            start_date = end_date - datetime.timedelta(days=days)
            
            # Get average BLEU score
            avg_bleu = BleuScoreHistory.objects.filter(
                date__gte=start_date,
                date__lte=end_date,
                source_language=source_language,
                target_language=target_language
            ).aggregate(avg_bleu=Avg('bleu_score'))
            
            return avg_bleu['avg_bleu'] or 0.0
            
        except Language.DoesNotExist:
            print(f"Language not found: {source_lang_code} or {target_lang_code}")
            return 0.0
        except Exception as e:
            print(f"Error getting average BLEU score: {str(e)}")
            return 0.0

# Singleton instance
bleu_calculator = BleuScoreCalculator()
