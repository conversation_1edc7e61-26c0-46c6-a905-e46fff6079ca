"""
Utility functions for incrementally updating the static translations file.
This avoids database locks by only updating the changed translations.
"""
import os
import json
import logging
import re
from django.utils import timezone

logger = logging.getLogger(__name__)

def incrementally_update_static_file(source_text, translation_text, source_lang, target_lang):
    """
    Incrementally update the static translations file with a single translation.
    This avoids regenerating the entire file and reduces database locks.

    Args:
        source_text: The source text
        translation_text: The translation
        source_lang: Source language code
        target_lang: Target language code

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Determine the translation direction
        if source_lang == 'tgl' and target_lang == 'ted':
            direction = 'tgl_to_ted'
        elif source_lang == 'ted' and target_lang == 'tgl':
            direction = 'ted_to_tgl'
        else:
            logger.warning(f"Unsupported language pair: {source_lang} to {target_lang}")
            return False

        # Check if the static file exists
        static_file_path = os.path.join('static', 'translation_app', 'js', 'all_translations.js')
        if not os.path.exists(static_file_path):
            # Try alternate path
            static_file_path = os.path.join('translation_app', 'static', 'translation_app', 'js', 'all_translations.js')
            if not os.path.exists(static_file_path):
                logger.error(f"Static file not found: {static_file_path}")
                return False

        # Read the current content
        with open(static_file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Find the start and end of the JSON object
        start_index = content.find('const ALL_TRANSLATIONS = ')
        if start_index == -1:
            logger.error("Could not find ALL_TRANSLATIONS variable in static file")
            return False

        # Extract the JSON part
        json_start = content.find('{', start_index)
        json_end = content.rfind(';')
        if json_start == -1 or json_end == -1 or json_end <= json_start:
            logger.error("Could not extract JSON from static file")
            return False

        json_str = content[json_start:json_end]

        try:
            # Parse the JSON
            translations_dict = json.loads(json_str)
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON from static file: {str(e)}")
            return False

        # Check if the direction exists
        if direction not in translations_dict:
            translations_dict[direction] = {}

        # Normalize the source text
        source_text_lower = source_text.lower()

        # Determine if it's a phrase or word
        is_phrase = ' ' in source_text_lower

        # Create the key
        key = f"phrase:{source_text_lower}" if is_phrase else source_text_lower

        # Update or add the translation
        translations_dict[direction][key] = {
            'translation': translation_text,
            'confidence': 0.99,  # High confidence for user feedback
            'source': 'user_feedback',
            'part_of_speech': 'phrase' if is_phrase else '',
            'notes': f'Updated via incremental update on {timezone.now().strftime("%Y-%m-%d")}',
            'priority': True,
            'do_not_modify': True
        }

        # Create the updated content
        updated_json = json.dumps(translations_dict, ensure_ascii=False, indent=2)
        updated_content = content[:json_start] + updated_json + content[json_end:]

        # Write the updated content back to the file
        with open(static_file_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)

        logger.info(f"Incrementally updated static file with translation: {source_text} → {translation_text}")
        return True

    except Exception as e:
        logger.error(f"Error incrementally updating static file: {str(e)}")
        return False
