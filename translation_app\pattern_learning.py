"""
Pattern Learning Module for Translation System

This module provides utilities for learning patterns from translations
and applying them to improve future translations.
"""

import re
import json
import os
import logging
from typing import Dict, List, Any, Tuple
from collections import defaultdict

logger = logging.getLogger(__name__)

class PatternLearner:
    """
    Class for learning patterns from translations and applying them to improve future translations.
    """
    
    def __init__(self):
        """Initialize the pattern learner"""
        self.word_patterns = {
            'tgl': [],  # Tagalog word patterns
            'ted': []   # Teduray word patterns
        }
        self.phrase_patterns = {
            'tgl': [],  # Tagalog phrase patterns
            'ted': []   # Teduray phrase patterns
        }
        self.grammar_patterns = {
            'tgl': [],  # Tagalog grammar patterns
            'ted': []   # Teduray grammar patterns
        }
        self.substitution_patterns = {
            'tgl': [],  # Tagalog substitution patterns
            'ted': []   # Teduray substitution patterns
        }
        
        # Load patterns from file if available
        self.load_patterns()
    
    def load_patterns(self):
        """Load patterns from file"""
        try:
            patterns_path = os.path.join('data', 'learned_patterns.json')
            if os.path.exists(patterns_path):
                with open(patterns_path, 'r', encoding='utf-8') as f:
                    patterns = json.load(f)
                
                # Load patterns
                if 'word_patterns' in patterns:
                    self.word_patterns = patterns['word_patterns']
                if 'phrase_patterns' in patterns:
                    self.phrase_patterns = patterns['phrase_patterns']
                if 'grammar_patterns' in patterns:
                    self.grammar_patterns = patterns['grammar_patterns']
                if 'substitution_patterns' in patterns:
                    self.substitution_patterns = patterns['substitution_patterns']
                
                logger.info(f"Loaded patterns from {patterns_path}")
                
                # Log pattern counts
                for lang in ['tgl', 'ted']:
                    logger.info(f"{lang.upper()} patterns:")
                    logger.info(f"  Word patterns: {len(self.word_patterns.get(lang, []))}")
                    logger.info(f"  Phrase patterns: {len(self.phrase_patterns.get(lang, []))}")
                    logger.info(f"  Grammar patterns: {len(self.grammar_patterns.get(lang, []))}")
                    logger.info(f"  Substitution patterns: {len(self.substitution_patterns.get(lang, []))}")
        except Exception as e:
            logger.error(f"Error loading patterns: {str(e)}")
    
    def save_patterns(self):
        """Save patterns to file"""
        try:
            patterns_path = os.path.join('data', 'learned_patterns.json')
            
            # Create data directory if it doesn't exist
            os.makedirs(os.path.dirname(patterns_path), exist_ok=True)
            
            # Save patterns
            patterns = {
                'word_patterns': self.word_patterns,
                'phrase_patterns': self.phrase_patterns,
                'grammar_patterns': self.grammar_patterns,
                'substitution_patterns': self.substitution_patterns
            }
            
            with open(patterns_path, 'w', encoding='utf-8') as f:
                json.dump(patterns, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Saved patterns to {patterns_path}")
        except Exception as e:
            logger.error(f"Error saving patterns: {str(e)}")
    
    def learn_from_example(self, source_text: str, target_text: str, 
                         source_lang: str, target_lang: str) -> Dict[str, Any]:
        """
        Learn patterns from a translation example.
        
        Args:
            source_text: Source text
            target_text: Target text (translation)
            source_lang: Source language code
            target_lang: Target language code
            
        Returns:
            dict: Information about learned patterns
        """
        # Skip empty texts
        if not source_text or not target_text:
            return {'learned': False, 'reason': 'Empty text'}
        
        # Learn word patterns
        word_patterns_learned = self._learn_word_patterns(source_text, target_text, source_lang)
        
        # Learn phrase patterns
        phrase_patterns_learned = self._learn_phrase_patterns(source_text, target_text, source_lang)
        
        # Learn grammar patterns
        grammar_patterns_learned = self._learn_grammar_patterns(source_text, target_text, source_lang)
        
        # Learn substitution patterns
        substitution_patterns_learned = self._learn_substitution_patterns(source_text, target_text, source_lang)
        
        # Save patterns
        self.save_patterns()
        
        return {
            'learned': True,
            'word_patterns': word_patterns_learned,
            'phrase_patterns': phrase_patterns_learned,
            'grammar_patterns': grammar_patterns_learned,
            'substitution_patterns': substitution_patterns_learned
        }
    
    def _learn_word_patterns(self, source_text: str, target_text: str, source_lang: str) -> int:
        """
        Learn word patterns from a translation example.
        
        Args:
            source_text: Source text
            target_text: Target text (translation)
            source_lang: Source language code
            
        Returns:
            int: Number of patterns learned
        """
        # Simple implementation: learn one-to-one word mappings
        source_words = source_text.split()
        target_words = target_text.split()
        
        # Skip if word counts are very different
        if abs(len(source_words) - len(target_words)) > max(len(source_words), len(target_words)) / 2:
            return 0
        
        patterns_learned = 0
        
        # Learn one-to-one mappings for words of similar length
        min_len = min(len(source_words), len(target_words))
        for i in range(min_len):
            source_word = source_words[i].lower()
            target_word = target_words[i].lower()
            
            # Skip very short words
            if len(source_word) <= 2 or len(target_word) <= 2:
                continue
            
            # Skip words with punctuation
            if any(p in source_word for p in '.,;:!?()[]{}') or any(p in target_word for p in '.,;:!?()[]{}'):
                continue
            
            # Add pattern if not already present
            pattern = {
                'source': source_word,
                'target': target_word,
                'confidence': 0.7  # Medium confidence for learned patterns
            }
            
            if pattern not in self.word_patterns[source_lang]:
                self.word_patterns[source_lang].append(pattern)
                patterns_learned += 1
        
        return patterns_learned
    
    def _learn_phrase_patterns(self, source_text: str, target_text: str, source_lang: str) -> int:
        """
        Learn phrase patterns from a translation example.
        
        Args:
            source_text: Source text
            target_text: Target text (translation)
            source_lang: Source language code
            
        Returns:
            int: Number of patterns learned
        """
        # Simple implementation: learn whole phrases
        pattern = {
            'source': source_text.lower(),
            'target': target_text,
            'confidence': 0.9  # High confidence for exact phrases
        }
        
        if pattern not in self.phrase_patterns[source_lang]:
            self.phrase_patterns[source_lang].append(pattern)
            return 1
        
        return 0
    
    def _learn_grammar_patterns(self, source_text: str, target_text: str, source_lang: str) -> int:
        """
        Learn grammar patterns from a translation example.
        
        Args:
            source_text: Source text
            target_text: Target text (translation)
            source_lang: Source language code
            
        Returns:
            int: Number of patterns learned
        """
        # This is a simplified implementation
        # In a production system, you would use more sophisticated grammar analysis
        
        patterns_learned = 0
        
        # Learn prefix patterns
        source_words = source_text.split()
        target_words = target_text.split()
        
        if len(source_words) >= 2 and len(target_words) >= 2:
            # Learn beginning of sentence patterns
            source_prefix = ' '.join(source_words[:2]).lower()
            target_prefix = ' '.join(target_words[:2])
            
            prefix_pattern = {
                'type': 'prefix',
                'source': source_prefix,
                'target': target_prefix,
                'confidence': 0.6
            }
            
            if prefix_pattern not in self.grammar_patterns[source_lang]:
                self.grammar_patterns[source_lang].append(prefix_pattern)
                patterns_learned += 1
            
            # Learn end of sentence patterns
            source_suffix = ' '.join(source_words[-2:]).lower()
            target_suffix = ' '.join(target_words[-2:])
            
            suffix_pattern = {
                'type': 'suffix',
                'source': source_suffix,
                'target': target_suffix,
                'confidence': 0.6
            }
            
            if suffix_pattern not in self.grammar_patterns[source_lang]:
                self.grammar_patterns[source_lang].append(suffix_pattern)
                patterns_learned += 1
        
        return patterns_learned
    
    def _learn_substitution_patterns(self, source_text: str, target_text: str, source_lang: str) -> int:
        """
        Learn substitution patterns from a translation example.
        
        Args:
            source_text: Source text
            target_text: Target text (translation)
            source_lang: Source language code
            
        Returns:
            int: Number of patterns learned
        """
        # This is a simplified implementation
        # In a production system, you would use more sophisticated pattern extraction
        
        patterns_learned = 0
        
        # Learn simple substitution patterns (e.g., "X Y" -> "Y X")
        source_words = source_text.split()
        target_words = target_text.split()
        
        if len(source_words) == 2 and len(target_words) == 2:
            # Check if the words are swapped
            if source_words[0].lower() == target_words[1].lower() and source_words[1].lower() == target_words[0].lower():
                pattern = {
                    'type': 'swap',
                    'source_pattern': 'X Y',
                    'target_pattern': 'Y X',
                    'confidence': 0.7
                }
                
                if pattern not in self.substitution_patterns[source_lang]:
                    self.substitution_patterns[source_lang].append(pattern)
                    patterns_learned += 1
        
        return patterns_learned
    
    def apply_patterns(self, text: str, source_lang: str, target_lang: str) -> Dict[str, Any]:
        """
        Apply learned patterns to improve a translation.
        
        Args:
            text: Text to translate
            source_lang: Source language code
            target_lang: Target language code
            
        Returns:
            dict: Improved translation information
        """
        # This is a placeholder implementation
        # In a production system, you would apply the patterns to improve the translation
        
        # Check if we have an exact phrase match
        text_lower = text.lower()
        for pattern in self.phrase_patterns[source_lang]:
            if pattern['source'] == text_lower:
                return {
                    'improved_translation': pattern['target'],
                    'confidence': pattern['confidence'],
                    'patterns_applied': [{'type': 'phrase', 'pattern': pattern}]
                }
        
        # If no exact match, return the original text
        return {
            'improved_translation': text,
            'confidence': 0.0,
            'patterns_applied': []
        }

# Singleton instance
pattern_learner = PatternLearner()

def apply_patterns(text: str, source_lang: str, target_lang: str) -> Dict[str, Any]:
    """
    Apply learned patterns to improve a translation.
    
    Args:
        text: Text to translate
        source_lang: Source language code
        target_lang: Target language code
        
    Returns:
        dict: Improved translation information
    """
    return pattern_learner.apply_patterns(text, source_lang, target_lang)

def analyze_translations():
    """
    Analyze translations to extract patterns.
    
    This function is called by the management command to analyze translations
    and extract patterns from them.
    """
    # This is a placeholder implementation
    # In a production system, you would analyze all translations in the database
    
    logger.info("Analyzing translations...")
    
    # Log pattern counts
    for lang in ['tgl', 'ted']:
        logger.info(f"{lang.upper()} patterns:")
        logger.info(f"  Word patterns: {len(pattern_learner.word_patterns.get(lang, []))}")
        logger.info(f"  Phrase patterns: {len(pattern_learner.phrase_patterns.get(lang, []))}")
        logger.info(f"  Grammar patterns: {len(pattern_learner.grammar_patterns.get(lang, []))}")
        logger.info(f"  Substitution patterns: {len(pattern_learner.substitution_patterns.get(lang, []))}")
    
    return {
        'tgl': {
            'word_patterns': len(pattern_learner.word_patterns.get('tgl', [])),
            'phrase_patterns': len(pattern_learner.phrase_patterns.get('tgl', [])),
            'grammar_patterns': len(pattern_learner.grammar_patterns.get('tgl', [])),
            'substitution_patterns': len(pattern_learner.substitution_patterns.get('tgl', []))
        },
        'ted': {
            'word_patterns': len(pattern_learner.word_patterns.get('ted', [])),
            'phrase_patterns': len(pattern_learner.phrase_patterns.get('ted', [])),
            'grammar_patterns': len(pattern_learner.grammar_patterns.get('ted', [])),
            'substitution_patterns': len(pattern_learner.substitution_patterns.get('ted', []))
        }
    }
