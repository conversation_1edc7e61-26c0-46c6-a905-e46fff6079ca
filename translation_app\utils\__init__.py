"""
Utility functions for the translation app.
"""
import logging
from .static_file_updater import update_static_translations_file, create_logs_directory

logger = logging.getLogger(__name__)

# Create logs directory on import
create_logs_directory()

def extract_words_from_phrase(phrase):
    """
    Extract individual words from a phrase.

    Args:
        phrase (str): The phrase to extract words from.

    Returns:
        list: A list of words.
    """
    if not phrase:
        return []

    # Remove punctuation and split by whitespace
    import re
    words = re.sub(r'[^\w\s]', ' ', phrase.lower()).split()

    # Filter out very short words (likely not meaningful)
    words = [word for word in words if len(word) > 2]

    # Remove duplicates while preserving order
    seen = set()
    unique_words = [word for word in words if not (word in seen or seen.add(word))]

    return unique_words

def process_feedback_safely(original_text, translated_text, suggested_translation, source_lang_code, target_lang_code, user=None):
    """
    Process translation feedback safely.

    Args:
        original_text (str): The original text.
        translated_text (str): The translated text.
        suggested_translation (str): The suggested translation.
        source_lang_code (str): The source language code.
        target_lang_code (str): The target language code.
        user (User, optional): The user who submitted the feedback.

    Returns:
        dict: A dictionary with the result of the feedback processing.
    """
    try:
        logger.info(f"Processing feedback for: {original_text} → {suggested_translation}")

        # Check if automatic processes are disabled
        try:
            from django.conf import settings
            auto_processes_disabled = getattr(settings, 'DISABLE_AUTO_PROCESSES', False)

            if auto_processes_disabled:
                logger.info(f"Automatic static file update skipped for feedback: {original_text} - will be processed by scheduled optimization task")
            else:
                # Only update if automatic processes are enabled
                try:
                    update_static_translations_file(background=True)
                    logger.info(f"Triggered static file update after processing feedback for: {original_text}")
                except Exception as e:
                    logger.error(f"Error triggering static file update: {str(e)}")
        except Exception as e:
            logger.error(f"Error checking auto processes setting: {str(e)}")

        return {
            'status': 'success',
            'message': 'Feedback processed successfully'
        }
    except Exception as e:
        logger.error(f"Error processing feedback: {str(e)}")
        return {
            'status': 'error',
            'message': f'Error processing feedback: {str(e)}'
        }
