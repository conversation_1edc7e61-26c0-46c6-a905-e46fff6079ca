#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to create the static directories needed for PythonAnywhere deployment.
This ensures that the directories exist before <PERSON>jango tries to use them.
"""

import os
import sys

# Define the directories to create
STATIC_SOURCE_DIR = '/home/<USER>/teduray/static_source'
STATIC_COLLECTED_DIR = '/home/<USER>/teduray/staticfiles_collected'

def create_directories():
    """Create the static directories if they don't exist."""
    print(f"Creating static directories for PythonAnywhere deployment...")
    
    # Create the static source directory
    if not os.path.exists(STATIC_SOURCE_DIR):
        try:
            os.makedirs(STATIC_SOURCE_DIR)
            print(f"Created directory: {STATIC_SOURCE_DIR}")
        except Exception as e:
            print(f"Error creating {STATIC_SOURCE_DIR}: {e}", file=sys.stderr)
    else:
        print(f"Directory already exists: {STATIC_SOURCE_DIR}")
    
    # Create the static collected directory
    if not os.path.exists(STATIC_COLLECTED_DIR):
        try:
            os.makedirs(STATIC_COLLECTED_DIR)
            print(f"Created directory: {STATIC_COLLECTED_DIR}")
        except Exception as e:
            print(f"Error creating {STATIC_COLLECTED_DIR}: {e}", file=sys.stderr)
    else:
        print(f"Directory already exists: {STATIC_COLLECTED_DIR}")
    
    print("Static directories setup complete.")

if __name__ == "__main__":
    create_directories()
