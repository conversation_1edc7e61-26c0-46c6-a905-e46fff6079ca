// Direct theme toggle fix
(function() {
    // Function to set the theme
    function setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
        
        // Update button states
        const buttons = document.querySelectorAll('.theme-toggle button');
        buttons.forEach(button => {
            button.setAttribute('aria-pressed', button.dataset.theme === theme);
        });
    }
    
    // Function to initialize the theme
    function initTheme() {
        // Get stored theme or use light as default
        const storedTheme = localStorage.getItem('theme') || 'light';
        setTheme(storedTheme);
        
        // Add click handlers to theme buttons
        const buttons = document.querySelectorAll('.theme-toggle button');
        buttons.forEach(button => {
            button.addEventListener('click', function() {
                setTheme(this.dataset.theme);
            });
        });
    }
    
    // Initialize when DOM is loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initTheme);
    } else {
        initTheme();
    }
})();
