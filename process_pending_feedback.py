"""
Process pending feedback files.

This script is designed to be run as a scheduled task in PythonAnywhere.
It processes feedback files that were saved when users submitted feedback
but couldn't be processed immediately due to database locks.
"""
import os
import json
import logging
import django
import time
import sys
from glob import glob

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/process_feedback.log')
    ]
)
logger = logging.getLogger(__name__)

# Import Django models and utilities
from django.db import connection
from translation_app.models import Language, TranslationFeedback
from translation_app.utils import process_feedback_safely
from translation_app.services import get_translation_service
from translation_app.middleware import with_retry

def process_feedback_file(filepath):
    """Process a single feedback file."""
    logger.info(f"Processing feedback file: {filepath}")
    
    try:
        # Read the feedback data
        with open(filepath, 'r', encoding='utf-8') as f:
            feedback_data = json.load(f)
        
        # Extract data
        original_text = feedback_data.get('original_text', '')
        translated_text = feedback_data.get('translated_text', '')
        suggested_translation = feedback_data.get('suggested_translation', '')
        source_lang_code = feedback_data.get('source_lang_code', 'tgl')
        target_lang_code = feedback_data.get('target_lang_code', 'ted')
        rating = feedback_data.get('rating')
        comments = feedback_data.get('comments', '')
        user_id = feedback_data.get('user_id')
        
        logger.info(f"Processing feedback: {original_text} → {suggested_translation}")
        
        # Ensure the database connection is open
        if connection.connection is None:
            connection.ensure_connection()
        
        # Get language objects
        def get_source_language():
            return Language.objects.get(code=source_lang_code)

        def get_target_language():
            return Language.objects.get(code=target_lang_code)

        source_language = with_retry(get_source_language)
        target_language = with_retry(get_target_language)
        
        # Create feedback record
        def create_feedback():
            return TranslationFeedback.objects.create(
                original_text=original_text,
                translated_text=translated_text,
                suggested_translation=suggested_translation,
                source_language=source_language,
                target_language=target_language,
                rating=rating,
                comments=comments,
                user_id=user_id
            )
        
        feedback = with_retry(create_feedback)
        logger.info(f"Created feedback record with ID: {feedback.id}")
        
        # If there's a suggested translation, process it
        if suggested_translation and original_text and suggested_translation != translated_text:
            # Add to learned_translations.txt
            try:
                with open(os.path.join('data', 'learned_translations.txt'), 'a', encoding='utf-8') as f:
                    f.write(f"{source_lang_code}|{original_text}|{target_lang_code}|{suggested_translation}\n")
                logger.info(f"Added to learned_translations.txt: {original_text} → {suggested_translation}")
            except Exception as e:
                logger.error(f"Error saving to learned_translations.txt: {str(e)}")
            
            # Process the feedback safely
            try:
                result = process_feedback_safely(
                    original_text,
                    translated_text,
                    suggested_translation,
                    source_lang_code,
                    target_lang_code,
                    None  # Don't pass user object
                )
                logger.info(f"Processed feedback safely: {result}")
            except Exception as e:
                logger.error(f"Error in process_feedback_safely: {str(e)}")
            
            # Update the translation in the cache
            try:
                translation_service = get_translation_service()
                translation_service.update_single_translation(
                    original_text,
                    suggested_translation,
                    source_lang_code,
                    target_lang_code
                )
                logger.info(f"Updated translation in cache: {original_text} → {suggested_translation}")
            except Exception as e:
                logger.error(f"Error updating translation in cache: {str(e)}")
        
        # Delete the file after processing
        os.remove(filepath)
        logger.info(f"Deleted feedback file: {filepath}")
        
        return True
    except Exception as e:
        logger.error(f"Error processing feedback file {filepath}: {str(e)}")
        return False

def process_all_pending_feedback():
    """Process all pending feedback files."""
    logger.info("Starting to process all pending feedback files")
    
    # Create the directory if it doesn't exist
    os.makedirs('data/pending_feedback', exist_ok=True)
    
    # Get all feedback files
    feedback_files = glob('data/pending_feedback/feedback_*.json')
    logger.info(f"Found {len(feedback_files)} pending feedback files")
    
    # Process each file
    success_count = 0
    error_count = 0
    
    for filepath in feedback_files:
        try:
            # Add a small delay between processing files to reduce database contention
            time.sleep(0.5)
            
            if process_feedback_file(filepath):
                success_count += 1
            else:
                error_count += 1
        except Exception as e:
            logger.error(f"Unhandled error processing {filepath}: {str(e)}")
            error_count += 1
    
    logger.info(f"Finished processing pending feedback. Success: {success_count}, Errors: {error_count}")
    return success_count, error_count

if __name__ == "__main__":
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)
    
    logger.info("Starting process_pending_feedback.py")
    
    try:
        success_count, error_count = process_all_pending_feedback()
        logger.info(f"Processed {success_count + error_count} feedback files. Success: {success_count}, Errors: {error_count}")
    except Exception as e:
        logger.error(f"Unhandled error in process_pending_feedback.py: {str(e)}")
    
    logger.info("Finished process_pending_feedback.py")
