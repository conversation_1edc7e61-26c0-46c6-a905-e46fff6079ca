from django.utils import timezone
from .models import (
    Language,
    ComprehensiveTranslation,
    TranslationExample,
    TranslationVersion,
    TranslationSuggestion
)

# Import pattern learning module
try:
    from .pattern_learning import learn_from_feedback
    PATTERN_LEARNING_ENABLED = True
except ImportError:
    PATTERN_LEARNING_ENABLED = False

def is_bible_translation(translation):
    """Check if a translation is from the Bible"""
    return (
        translation.notes and
        any(term in translation.notes for term in ['Bible', 'Matthew', 'Mark', 'Luke', 'John'])
    )

def update_translation_with_protection(original_text, new_translation, source_lang, target_lang, user=None, user_confidence=0.8):
    """
    Update translation with protection for high-confidence existing translations.

    Args:
        original_text: The source text to translate
        new_translation: The suggested new translation
        source_lang: Source language code
        target_lang: Target language code
        user: User object (optional)
        user_confidence: Confidence score for the new translation (default: 0.8)

    Returns:
        tuple: (success, message, translation_obj)
    """
    from .middleware import with_retry
    from django.db import connection

    # Ensure the database connection is open
    if connection.connection is None:
        connection.ensure_connection()

    # Get language objects using with_retry
    try:
        def get_source_language():
            return Language.objects.get(code=source_lang)

        def get_target_language():
            return Language.objects.get(code=target_lang)

        source_language = with_retry(get_source_language)
        target_language = with_retry(get_target_language)
    except Language.DoesNotExist:
        return False, "Language not found", None

    # Check if translation exists using with_retry
    def get_existing_translation():
        return ComprehensiveTranslation.objects.filter(
            base_word=original_text,
            source_language=source_language,
            target_language=target_language
        ).first()

    existing = with_retry(get_existing_translation)

    if existing:
        # Get current confidence (default to 0.95 for Bible translations)
        current_confidence = 0.95

        # Check if this is a Bible translation
        if is_bible_translation(existing):
            # For Bible translations, don't override automatically
            # Instead, create a suggested version for review
            def create_suggestion():
                return TranslationSuggestion.objects.create(
                    original_text=original_text,
                    system_translation=existing.translation,
                    suggested_translation=new_translation,
                    source_language=source_language,
                    target_language=target_language,
                    status='pending',
                    confidence_score=user_confidence,
                    notes=f'Suggested update for Bible translation. Original notes: {existing.notes}',
                    user=user
                )

            with_retry(create_suggestion)
            return False, "Bible translation cannot be updated directly. Your suggestion has been recorded for review.", existing
        elif user_confidence > current_confidence:
            # Create a new version and update the translation using with_retry
            def create_old_version():
                return TranslationVersion.objects.create(
                    comprehensive_translation=existing,
                    translation=existing.translation,  # Save the old translation
                    created_by='system',
                    confidence_score=current_confidence,
                    is_active=False,
                    notes='Archived before user update'
                )

            with_retry(create_old_version)

            # Update the translation using with_retry
            def update_existing_translation():
                existing.translation = new_translation
                existing.notes = (existing.notes or '') + f'\nUpdated via feedback on {timezone.now().strftime("%Y-%m-%d")}'
                existing.save()
                return existing

            with_retry(update_existing_translation)

            # Create a new version for the updated translation using with_retry
            def create_new_version():
                return TranslationVersion.objects.create(
                    comprehensive_translation=existing,
                    translation=new_translation,
                    created_by='user',
                    confidence_score=user_confidence,
                    is_active=True,
                    notes='Applied from user feedback'
                )

            with_retry(create_new_version)

            return True, "Translation updated successfully", existing
        else:
            return False, "Existing translation has higher confidence", existing
    else:
        # Create new translation using with_retry
        def create_new_translation():
            return ComprehensiveTranslation.objects.create(
                base_word=original_text,
                translation=new_translation,
                source_language=source_language,
                target_language=target_language,
                part_of_speech='phrase' if ' ' in original_text else 'word',
                notes='Added from user feedback'
            )

        new_trans = with_retry(create_new_translation)

        # Create initial version using with_retry
        def create_initial_version():
            return TranslationVersion.objects.create(
                comprehensive_translation=new_trans,
                translation=new_translation,
                created_by='user',
                confidence_score=user_confidence,
                is_active=True,
                notes='Initial version from user feedback'
            )

        with_retry(create_initial_version)

        # Create example using with_retry
        def create_example():
            return TranslationExample.objects.create(
                comprehensive_translation=new_trans,
                source_text=original_text,
                target_text=new_translation
            )

        with_retry(create_example)

        return True, "New translation created successfully", new_trans

def process_feedback_safely(original_text, system_translation, corrected_translation, source_lang, target_lang, user=None):
    """
    Process user feedback with special protection for Bible translations.

    Args:
        original_text: The source text that was translated
        system_translation: The translation provided by the system
        corrected_translation: The corrected translation provided by the user
        source_lang: Source language code
        target_lang: Target language code
        user: User object (optional)

    Returns:
        dict: Result of the operation
    """
    from .middleware import with_retry
    from django.db import connection

    # Skip if no correction was provided
    if not corrected_translation or corrected_translation == system_translation:
        return {
            'status': 'skipped',
            'message': 'No correction was provided or correction matches current translation.'
        }

    # Ensure the database connection is open
    if connection.connection is None:
        connection.ensure_connection()

    # Get language objects using with_retry
    try:
        def get_source_language():
            return Language.objects.get(code=source_lang)

        def get_target_language():
            return Language.objects.get(code=target_lang)

        source_language = with_retry(get_source_language)
        target_language = with_retry(get_target_language)
    except Language.DoesNotExist:
        return {
            'status': 'error',
            'message': 'Language not found'
        }

    # Check if this is a Bible translation using with_retry
    def get_existing_translation():
        return ComprehensiveTranslation.objects.filter(
            base_word=original_text,
            source_language=source_language,
            target_language=target_language
        ).first()

    existing = with_retry(get_existing_translation)

    if existing and is_bible_translation(existing):
        # For Bible translations, create a suggestion but don't apply automatically
        def create_suggestion():
            return TranslationSuggestion.objects.create(
                original_text=original_text,
                system_translation=system_translation,
                suggested_translation=corrected_translation,
                source_language=source_language,
                target_language=target_language,
                status='pending',
                confidence_score=0.8,
                notes='Suggested from user feedback',
                user=user
            )

        suggestion = with_retry(create_suggestion)

        return {
            'status': 'suggestion_created',
            'message': 'Your suggestion for this Bible translation has been recorded for review by an administrator.',
            'suggestion_id': suggestion.id
        }
    else:
        # For non-Bible translations, apply the update with confidence check
        success, message, translation = update_translation_with_protection(
            original_text,
            corrected_translation,
            source_lang,
            target_lang,
            user
        )

        # If update was successful, defer the static file update to the scheduled optimization task
        if success:
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"Deferred static file update for feedback: {original_text} - will be processed by scheduled optimization task")

        # Use pattern learning if enabled
        if PATTERN_LEARNING_ENABLED and success:
            try:
                # Learn from this feedback
                learning_result = learn_from_feedback(
                    original_text,
                    system_translation,
                    corrected_translation,
                    source_lang,
                    target_lang
                )

                # Add learning results to the response
                return {
                    'status': 'success',
                    'message': message + ' ' + learning_result.get('message', ''),
                    'translation_id': translation.id if translation else None,
                    'patterns_learned': learning_result.get('differences', [])
                }
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Error learning from feedback: {str(e)}")

        # Default response if pattern learning is disabled or failed
        return {
            'status': 'success' if success else 'info',
            'message': message,
            'translation_id': translation.id if translation else None
        }
