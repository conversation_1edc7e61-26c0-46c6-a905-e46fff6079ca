[uwsgi]
# Use a single process to avoid SQLite locking issues
processes = 1
threads = 2

# Enable single interpreter mode
single-interpreter = true

# Disable request logging to reduce disk I/O
disable-logging = true

# Increase timeouts for database operations
harakiri = 300  # Increased from 120 to 300 seconds
socket-timeout = 120  # Increased from 60 to 120 seconds
http-timeout = 120  # Increased from 60 to 120 seconds

# Buffer sizes
buffer-size = 65536  # Increased from 32768 to 65536
post-buffering = 16384  # Increased from 8192 to 16384

# Memory optimizations
optimize = true
memory-report = true
max-requests = 1000  # Limit max requests per worker
reload-on-rss = 256  # Reload workers when they reach 256MB

# Prevent thundering herd
thunder-lock = true

# Reload on file changes
touch-reload = /home/<USER>/teduray/reload.txt

# Graceful handling of SIGTERM
die-on-term = true

# Disable write exception handling to avoid deadlocks
disable-write-exception = true

# Increase listen queue size
listen = 1024

# Enable master process
master = true

# Retry on connection errors
retry-on-error = 30

# Offload threads for async I/O
offload-threads = 2
