"""
AI Translation Logging Module

This module provides logging functionality for AI translations.
"""

import logging
import json
import os
from datetime import datetime
from django.conf import settings
from django.db import models

# Set up a dedicated logger for AI translations
ai_logger = logging.getLogger('ai_translations')

# Create a file handler for AI translations
log_dir = os.path.join(settings.BASE_DIR, 'logs')
os.makedirs(log_dir, exist_ok=True)
ai_log_file = os.path.join(log_dir, 'ai_translations.log')

# Configure the file handler
file_handler = logging.FileHandler(ai_log_file)
file_handler.setLevel(logging.INFO)

# Create a formatter
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)

# Add the handler to the logger
ai_logger.addHandler(file_handler)
ai_logger.setLevel(logging.INFO)

def log_ai_translation(source_text, source_lang, target_lang, translation, confidence,
                      model='huggingface', status='generated', user=None, notes=None):
    """
    Log an AI translation.

    Args:
        source_text: The source text
        source_lang: Source language code
        target_lang: Target language code
        translation: The generated translation
        confidence: Confidence score
        model: The AI model used
        status: Status of the translation (generated, approved, rejected, etc.)
        user: User who triggered the translation (if applicable)
        notes: Additional notes
    """
    # Get the logging level from settings
    from translation_app.ai_settings import AITranslationSettings
    settings = AITranslationSettings.get_settings()

    # Check if we should log this translation
    if settings.logging_level == 'none':
        return
    elif settings.logging_level == 'error' and confidence >= settings.confidence_threshold:
        return

    # Create the log entry
    log_entry = {
        'timestamp': datetime.now().isoformat(),
        'source_text': source_text,
        'source_lang': source_lang,
        'target_lang': target_lang,
        'translation': translation,
        'confidence': confidence,
        'model': model,
        'status': status,
        'user': user.username if user else 'system',
        'notes': notes
    }

    # Log as JSON for easier parsing
    ai_logger.info(json.dumps(log_entry, ensure_ascii=False))

    # Also log to database if it's a significant event
    if status in ['approved', 'rejected', 'modified'] or confidence < settings.confidence_threshold:
        try:
            from translation_app.models import AITranslationLog
            AITranslationLog.objects.create(
                source_text=source_text,
                source_lang=source_lang,
                target_lang=target_lang,
                translation=translation,
                confidence=confidence,
                model=model,
                status=status,
                user=user,
                notes=notes
            )
        except Exception as e:
            ai_logger.error(f"Error logging to database: {str(e)}")

class AITranslationLog(models.Model):
    """
    Model for storing AI translation logs in the database.
    """
    source_text = models.TextField()
    source_lang = models.CharField(max_length=10)
    target_lang = models.CharField(max_length=10)
    translation = models.TextField()
    confidence = models.FloatField()
    model = models.CharField(max_length=100)
    status = models.CharField(max_length=20)
    user = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True)
    notes = models.TextField(null=True, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['source_lang', 'target_lang']),
            models.Index(fields=['timestamp']),
        ]

    def __str__(self):
        return f"{self.source_text[:30]} → {self.translation[:30]} ({self.status})"
