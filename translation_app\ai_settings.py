"""
AI Translation Settings Model

This is a fallback file in case the models directory structure doesn't work.
"""

from django.db import models
from django.core.cache import cache

class AITranslationSettings(models.Model):
    """
    Model for storing AI translation settings.
    This is a singleton model - only one instance should exist.
    """
    # Enable/disable AI translation
    ai_translation_enabled = models.BooleanField(default=True, 
                                               help_text="Enable or disable AI translation")
    
    # Confidence threshold for using AI translations
    confidence_threshold = models.FloatField(default=0.7, 
                                           help_text="Minimum confidence score required to use AI translations")
    
    # Whether to require review for AI translations
    require_review = models.BooleanField(default=True,
                                       help_text="Require human review before adding AI translations to the database")
    
    # Logging level for AI translations
    LOG_LEVELS = [
        ('none', 'No Logging'),
        ('error', 'Errors Only'),
        ('all', 'Log All Translations')
    ]
    logging_level = models.Char<PERSON>ield(max_length=10, choices=LOG_LEVELS, default='all',
                                   help_text="Level of logging for AI translations")
    
    # Maximum number of AI translations per day (0 = unlimited)
    max_daily_translations = models.IntegerField(default=0,
                                              help_text="Maximum number of AI translations per day (0 = unlimited)")
    
    # Last modified timestamp
    last_modified = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "AI Translation Settings"
        verbose_name_plural = "AI Translation Settings"
    
    def save(self, *args, **kwargs):
        """Override save to ensure only one instance exists"""
        self.pk = 1
        super().save(*args, **kwargs)
        # Clear cache when settings are updated
        cache.delete('ai_translation_settings')
    
    @classmethod
    def get_settings(cls):
        """Get the settings instance, creating it if it doesn't exist"""
        # Try to get from cache first
        settings = cache.get('ai_translation_settings')
        if settings is None:
            settings, created = cls.objects.get_or_create(pk=1)
            # Cache the settings for 1 hour
            cache.set('ai_translation_settings', settings, 3600)
        return settings
    
    def __str__(self):
        return "AI Translation Settings"
