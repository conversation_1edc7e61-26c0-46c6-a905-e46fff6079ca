"""
Utility functions for working with attention mechanism data.
This module provides helper functions for retrieving and manipulating attention data.
"""

import logging
import json
from typing import Dict, List, Any, Optional, Tuple

from .models import AttentionData, ComprehensiveTranslation

logger = logging.getLogger(__name__)

def get_attention_data_for_translation(translation_id: int) -> Optional[Dict[str, Any]]:
    """
    Get attention data for a specific translation.
    
    Args:
        translation_id: ID of the ComprehensiveTranslation
        
    Returns:
        dict: Attention data or None if not found
    """
    try:
        # Get the translation
        translation = ComprehensiveTranslation.objects.get(id=translation_id)
        
        # Get the attention data
        attention_data = AttentionData.objects.filter(
            source_text=translation.base_word,
            target_text=translation.translation,
            source_language=translation.source_language,
            target_language=translation.target_language
        ).first()
        
        if attention_data:
            # Parse the attention weights
            weights = json.loads(attention_data.attention_weights)
            
            # Return the data
            return {
                'source_text': translation.base_word,
                'target_text': translation.translation,
                'source_lang': translation.source_language.code,
                'target_lang': translation.target_language.code,
                'attention_weights': weights,
                'confidence': getattr(attention_data, 'confidence', 0.8),
                'bleu_score': getattr(attention_data, 'bleu_score', 0.0)
            }
    except ComprehensiveTranslation.DoesNotExist:
        logger.error(f"Translation with ID {translation_id} not found")
    except AttentionData.DoesNotExist:
        logger.error(f"Attention data for translation ID {translation_id} not found")
    except Exception as e:
        logger.error(f"Error getting attention data: {str(e)}")
    
    return None

def get_attention_heatmap_data(source_text: str, target_text: str, 
                              attention_weights: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Generate heatmap data for visualizing attention weights.
    
    Args:
        source_text: Source text
        target_text: Target text
        attention_weights: List of attention weight dictionaries
        
    Returns:
        dict: Heatmap data for visualization
    """
    # Tokenize the texts
    source_tokens = source_text.split()
    target_tokens = target_text.split()
    
    # Create a matrix for the heatmap
    matrix = [[0.0 for _ in range(len(source_tokens))] for _ in range(len(target_tokens))]
    
    # Fill in the matrix with attention weights
    for weight in attention_weights:
        source_idx = weight.get('source_idx', 0)
        target_idx = weight.get('target_idx', 0)
        weight_value = weight.get('weight', 0.0)
        
        if (source_idx < len(source_tokens) and 
            target_idx < len(target_tokens)):
            matrix[target_idx][source_idx] = weight_value
    
    return {
        'source_tokens': source_tokens,
        'target_tokens': target_tokens,
        'matrix': matrix
    }

def calculate_alignment_score(source_text: str, target_text: str, 
                             attention_weights: List[Dict[str, Any]]) -> float:
    """
    Calculate an alignment score based on attention weights.
    
    Args:
        source_text: Source text
        target_text: Target text
        attention_weights: List of attention weight dictionaries
        
    Returns:
        float: Alignment score between 0 and 1
    """
    if not attention_weights:
        return 0.0
    
    # Calculate the average weight
    total_weight = sum(w.get('weight', 0.0) for w in attention_weights)
    avg_weight = total_weight / len(attention_weights) if attention_weights else 0.0
    
    # Scale to 0-1 range
    return min(1.0, max(0.0, avg_weight))
