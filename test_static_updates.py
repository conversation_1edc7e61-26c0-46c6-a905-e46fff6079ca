"""
Test script to verify that automatic static file updates are properly disabled.
"""
import os
import django
import logging

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

from django.conf import settings
from translation_app.models import Language, TranslationFeedback
from translation_app.utils import process_feedback_safely
from translation_app.services import get_translation_service

# Check if automatic processes are disabled
auto_processes_disabled = getattr(settings, 'DISABLE_AUTO_PROCESSES', False)
logger.info(f"DISABLE_AUTO_PROCESSES setting: {auto_processes_disabled}")

# Test process_feedback_safely function
def test_process_feedback_safely():
    logger.info("Testing process_feedback_safely function...")
    result = process_feedback_safely(
        original_text="test_word",
        translated_text="test_translation",
        suggested_translation="improved_translation",
        source_lang_code="tgl",
        target_lang_code="ted",
        user=None
    )
    logger.info(f"Result: {result}")

# Test update_single_translation method
def test_update_single_translation():
    logger.info("Testing update_single_translation method...")
    translation_service = get_translation_service()
    translation_service.update_single_translation(
        source_text="test_word",
        translation_text="improved_translation",
        source_lang="tgl",
        target_lang="ted"
    )

# Test creating a TranslationFeedback object
def test_create_feedback():
    logger.info("Testing TranslationFeedback creation...")
    try:
        # Get language objects
        tagalog = Language.objects.get(code='tgl')
        teduray = Language.objects.get(code='ted')
        
        # Create feedback
        feedback = TranslationFeedback.objects.create(
            original_text="test_word",
            translated_text="test_translation",
            suggested_translation="improved_translation",
            source_language=tagalog,
            target_language=teduray,
            rating=5,
            comments="Test feedback"
        )
        logger.info(f"Created feedback with ID: {feedback.id}")
    except Exception as e:
        logger.error(f"Error creating feedback: {str(e)}")

if __name__ == "__main__":
    logger.info("Starting tests...")
    
    # Run tests
    test_process_feedback_safely()
    test_update_single_translation()
    test_create_feedback()
    
    logger.info("Tests completed.")
