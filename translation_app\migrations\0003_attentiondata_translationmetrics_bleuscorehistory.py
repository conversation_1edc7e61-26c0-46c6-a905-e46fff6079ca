# Generated by Django 4.2.7 on 2025-05-01 12:49

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('translation_app', '0002_translationfeedback_processed_translationversion_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AttentionData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('source_text', models.TextField()),
                ('target_text', models.TextField()),
                ('attention_weights', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('translation_id', models.IntegerField(blank=True, null=True)),
                ('source_language', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='source_attention', to='translation_app.language')),
                ('target_language', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='target_attention', to='translation_app.language')),
            ],
        ),
        migrations.CreateModel(
            name='TranslationMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(default=django.utils.timezone.now)),
                ('words_translated', models.IntegerField(default=0)),
                ('characters_translated', models.IntegerField(default=0)),
                ('sentences_translated', models.IntegerField(default=0)),
                ('bleu_score', models.FloatField(default=0.0)),
                ('meteor_score', models.FloatField(blank=True, default=0.0, null=True)),
                ('average_rating', models.FloatField(default=0.0)),
                ('feedback_count', models.IntegerField(default=0)),
                ('average_confidence', models.FloatField(default=0.0)),
                ('additional_metrics', models.TextField(blank=True, null=True)),
                ('source_language', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='source_metrics', to='translation_app.language')),
                ('target_language', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='target_metrics', to='translation_app.language')),
            ],
            options={
                'verbose_name': 'Translation Metrics',
                'verbose_name_plural': 'Translation Metrics',
                'ordering': ['-date'],
                'unique_together': {('date', 'source_language', 'target_language')},
            },
        ),
        migrations.CreateModel(
            name='BleuScoreHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(default=django.utils.timezone.now)),
                ('bleu_score', models.FloatField(default=0.0)),
                ('bleu_1gram', models.FloatField(default=0.0)),
                ('bleu_2gram', models.FloatField(default=0.0)),
                ('bleu_3gram', models.FloatField(default=0.0)),
                ('bleu_4gram', models.FloatField(default=0.0)),
                ('reference_count', models.IntegerField(default=0)),
                ('test_set_size', models.IntegerField(default=0)),
                ('notes', models.TextField(blank=True, null=True)),
                ('source_language', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='source_bleu', to='translation_app.language')),
                ('target_language', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='target_bleu', to='translation_app.language')),
            ],
            options={
                'verbose_name': 'BLEU Score History',
                'verbose_name_plural': 'BLEU Score History',
                'ordering': ['-date'],
                'unique_together': {('date', 'source_language', 'target_language')},
            },
        ),
    ]
