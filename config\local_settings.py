"""
Local settings for the translation project.
This file contains settings that are specific to the local environment.
It is not checked into version control.
"""

# Database settings
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'Vergil20$teduray',  # PythonAnywhere MySQL database name
        'USER': 'Vergil20',          # PythonAnywhere username
        'PASSWORD': 'your_mysql_password_here',  # Replace with your actual MySQL password
        'HOST': 'Vergil20.mysql.pythonanywhere-services.com',  # PythonAnywhere MySQL host
        'PORT': '3306',              # MySQL default port
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'charset': 'utf8mb4',    # Use utf8mb4 for full Unicode support
            'connect_timeout': 10,   # Connection timeout in seconds
        },
        'ATOMIC_REQUESTS': False,    # Disable atomic requests to reduce locking
        'CONN_MAX_AGE': 300,         # Keep connections open for 5 minutes
        'CONN_HEALTH_CHECKS': True,  # Enable connection health checks
    }
}

# Debug settings
DEBUG = False

# Disable automatic processes
DISABLE_AUTO_PROCESSES = True

# Enable ultra lazy loading for translations
USE_ULTRA_LAZY_LOADING = True

# Enable PythonAnywhere optimization
PYTHONANYWHERE_OPTIMIZATION = True
