#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to fix the settings.py file for PythonAnywhere deployment.
This script will:
1. Update the settings.py file to use a simpler approach for PythonAnywhere settings
2. Fix any issues with the static files configuration
"""

import os
import sys
import re
from pathlib import Path

# Get the base directory of the project
BASE_DIR = Path(__file__).resolve().parent

def fix_settings_file():
    """Fix the settings.py file for PythonAnywhere deployment."""
    print("Fixing settings.py file for PythonAnywhere deployment...")
    
    # Path to the settings file
    settings_path = os.path.join(BASE_DIR, 'config', 'settings.py')
    
    if not os.path.exists(settings_path):
        print(f"Error: Settings file not found at {settings_path}", file=sys.stderr)
        return
    
    # Read the current settings file
    with open(settings_path, 'r') as f:
        settings_content = f.read()
    
    # Create a backup of the original file
    backup_path = settings_path + '.bak'
    with open(backup_path, 'w') as f:
        f.write(settings_content)
    print(f"Created backup of settings file at {backup_path}")
    
    # Replace the PythonAnywhere settings section with a simpler approach
    pythonanywhere_pattern = r'# Apply PythonAnywhere-specific settings if running on PythonAnywhere.*?(?=\n\n)'
    pythonanywhere_replacement = """# Apply PythonAnywhere-specific settings if running on PythonAnywhere
if is_running_on_pythonanywhere():
    # Set PythonAnywhere-specific settings directly
    # Database settings
    DATABASES['default']['TIMEOUT'] = 300  # Increase database timeout
    DATABASES['default']['OPTIONS'] = {
        'timeout': 300,  # SQLite timeout
        'isolation_level': None,  # Use autocommit mode
        'check_same_thread': False,  # Allow access from multiple threads
        'cached_statements': 500,  # Increase statement cache size
    }
    DATABASES['default']['CONN_MAX_AGE'] = 300  # Keep connections open for 5 minutes
    
    # Debug settings
    DEBUG = False  # Disable debug mode
    
    # Logging settings
    LOGGING['handlers']['file']['filename'] = '/home/<USER>/teduray/translation.log'
    
    # Media files
    MEDIA_ROOT = '/home/<USER>/teduray/media'"""
    
    # Use re.DOTALL to match across multiple lines
    new_settings_content = re.sub(pythonanywhere_pattern, pythonanywhere_replacement, settings_content, flags=re.DOTALL)
    
    # Replace the static files section with a simpler approach
    static_files_pattern = r'# Static files \(CSS, JavaScript, Images\).*?(?=\n\n# Default primary key field type)'
    static_files_replacement = """# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'

# Configure static files based on environment
if is_running_on_pythonanywhere():
    # On PythonAnywhere, use the configuration from the web app settings
    STATIC_ROOT = '/home/<USER>/teduray/staticfiles'
    STATICFILES_DIRS = ['/home/<USER>/teduray/static']
else:
    # For local development
    STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
    STATICFILES_DIRS = [os.path.join(BASE_DIR, 'static')]"""
    
    # Use re.DOTALL to match across multiple lines
    new_settings_content = re.sub(static_files_pattern, static_files_replacement, new_settings_content, flags=re.DOTALL)
    
    # Write the updated settings file
    with open(settings_path, 'w') as f:
        f.write(new_settings_content)
    
    print(f"Updated settings file at {settings_path}")
    print("Settings file fixed successfully.")

if __name__ == "__main__":
    fix_settings_file()
