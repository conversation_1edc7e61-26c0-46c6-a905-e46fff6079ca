"""
Admin dashboard for translation management.
"""

from django.contrib import admin
from django.urls import path
from django.shortcuts import render, redirect
from django.contrib import messages
from django.contrib.admin.views.decorators import staff_member_required
import threading
import logging
import time

logger = logging.getLogger(__name__)

@staff_member_required
def translation_dashboard(request):
    """Admin view for the translation dashboard"""
    context = {
        'title': 'Translation Dashboard',
        'site_title': admin.site.site_title,
        'site_header': admin.site.site_header,
        'has_permission': request.user.is_staff,
    }
    
    return render(request, 'admin/translation_buttons.html', context)

@staff_member_required
def update_attention(request):
    """Run a script to enhance translations using the attention mechanism"""
    if request.method == 'POST':
        try:
            # Run the script in a background thread to avoid blocking the admin interface
            def run_update_attention():
                try:
                    from django.core.management import call_command
                    from translation_app.services import get_translation_service
                    
                    # First, run the optimize_system command with attention focus
                    logger.info("Starting attention mechanism update...")
                    call_command('optimize_system', attention_focus=True)
                    
                    # Then reload the translation service
                    translation_service = get_translation_service()
                    translation_service.load_translations(clear_cache=True)
                    
                    logger.info("Attention mechanism update completed successfully")
                except Exception as e:
                    logger.error(f"Error in attention mechanism update: {str(e)}")
            
            # Start the thread
            thread = threading.Thread(target=run_update_attention)
            thread.daemon = True
            thread.start()
            
            messages.success(request, "Attention mechanism update started in the background. This may take a few minutes.")
        except Exception as e:
            messages.error(request, f"Error starting attention update: {str(e)}")
    
    return redirect('admin:index')

@staff_member_required
def update_static(request):
    """Update static translation files"""
    if request.method == 'POST':
        try:
            # Run the script in a background thread to avoid blocking the admin interface
            def run_update_static():
                try:
                    from django.core.management import call_command
                    
                    # Run the update_static_translations command
                    logger.info("Starting static translations update...")
                    call_command('update_static_translations')
                    
                    # Also create a reduced version for mobile/low-bandwidth
                    call_command('update_static_translations', reduced=True)
                    
                    logger.info("Static translations update completed successfully")
                except Exception as e:
                    logger.error(f"Error in static translations update: {str(e)}")
            
            # Start the thread
            thread = threading.Thread(target=run_update_static)
            thread.daemon = True
            thread.start()
            
            messages.success(request, "Static translations update started in the background. This may take a few minutes.")
        except Exception as e:
            messages.error(request, f"Error starting static update: {str(e)}")
    
    return redirect('admin:index')

@staff_member_required
def update_feedback(request):
    """Process all pending translation feedback"""
    if request.method == 'POST':
        try:
            # Run the script in a background thread to avoid blocking the admin interface
            def run_update_feedback():
                try:
                    from django.core.management import call_command
                    from translation_app.models import TranslationFeedback
                    
                    # Get count of pending feedback before processing
                    pending_count = TranslationFeedback.objects.filter(processed=False).count()
                    
                    # Run the process_feedback command
                    logger.info(f"Starting feedback processing for {pending_count} pending items...")
                    call_command('process_feedback')
                    
                    # Get count after processing
                    processed_count = pending_count - TranslationFeedback.objects.filter(processed=False).count()
                    
                    logger.info(f"Feedback processing completed. Processed {processed_count} items.")
                except Exception as e:
                    logger.error(f"Error in feedback processing: {str(e)}")
            
            # Start the thread
            thread = threading.Thread(target=run_update_feedback)
            thread.daemon = True
            thread.start()
            
            messages.success(request, "Translation feedback processing started in the background. This may take a few minutes.")
        except Exception as e:
            messages.error(request, f"Error starting feedback processing: {str(e)}")
    
    return redirect('admin:index')

@staff_member_required
def update_translations(request):
    """Update the translation system with all processed translations"""
    if request.method == 'POST':
        try:
            # Run the script in a background thread to avoid blocking the admin interface
            def run_update_translations():
                try:
                    from django.core.management import call_command
                    from translation_app.services import get_translation_service
                    
                    # First, run the optimize_system command
                    logger.info("Starting translation system update...")
                    call_command('optimize_system', update_metrics=True, load_translations=True)
                    
                    # Then reload the translation service
                    translation_service = get_translation_service()
                    translation_service.load_translations(clear_cache=True)
                    
                    # Finally, update the static files
                    time.sleep(2)  # Small delay to ensure database operations complete
                    call_command('update_static_translations')
                    
                    logger.info("Translation system update completed successfully")
                except Exception as e:
                    logger.error(f"Error in translation system update: {str(e)}")
            
            # Start the thread
            thread = threading.Thread(target=run_update_translations)
            thread.daemon = True
            thread.start()
            
            messages.success(request, "Translation system update started in the background. This may take a few minutes.")
        except Exception as e:
            messages.error(request, f"Error starting translation system update: {str(e)}")
    
    return redirect('admin:index')

# URLs for the admin dashboard
urlpatterns = [
    path('translation-dashboard/', translation_dashboard, name='translation_dashboard'),
    path('update-attention/', update_attention, name='update_attention'),
    path('update-static/', update_static, name='update_static'),
    path('update-feedback/', update_feedback, name='update_feedback'),
    path('update-translations/', update_translations, name='update_translations'),
]
