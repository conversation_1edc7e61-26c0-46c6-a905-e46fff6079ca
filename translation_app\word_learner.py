"""
Simple word-pair learning system for the translation app.
"""
from django.utils import timezone
from .models import Language, ComprehensiveTranslation, TranslationVersion, TranslationExample

class WordLearner:
    """Simple word-pair learning system"""
    
    def learn_word_pair(self, source_word, target_word, source_lang, target_lang):
        """Learn a single word pair translation"""
        try:
            # Get language objects
            source_language = Language.objects.get(code=source_lang)
            target_language = Language.objects.get(code=target_lang)
            
            # Check if translation already exists
            existing = ComprehensiveTranslation.objects.filter(
                base_word=source_word,
                source_language=source_language,
                target_language=target_language
            ).first()
            
            if existing:
                # Update if confidence is low
                current_version = existing.versions.filter(is_active=True).first()
                if current_version and current_version.confidence_score < 0.8:
                    # Create new version
                    TranslationVersion.objects.create(
                        comprehensive_translation=existing,
                        translation=target_word,
                        created_by='learner',
                        confidence_score=0.8,
                        is_active=True,
                        notes='Learned from feedback'
                    )
                    return True, "Updated existing word"
            else:
                # Create new translation
                new_trans = ComprehensiveTranslation.objects.create(
                    base_word=source_word,
                    translation=target_word,
                    source_language=source_language,
                    target_language=target_language,
                    part_of_speech='word',
                    notes='Learned from feedback'
                )
                
                # Create initial version
                TranslationVersion.objects.create(
                    comprehensive_translation=new_trans,
                    translation=target_word,
                    created_by='learner',
                    confidence_score=0.8,
                    is_active=True,
                    notes='Initial version from learning'
                )
                return True, "Added new word"
            
            return False, "No update needed"
            
        except Exception as e:
            print(f"Error learning word pair: {str(e)}")
            return False, str(e)
    
    def learn_from_sentence(self, source_text, target_text, source_lang, target_lang):
        """Extract and learn word pairs from sentences"""
        # Simple implementation - just split by spaces
        source_words = source_text.split()
        target_words = target_text.split()
        
        # Skip if lengths are very different
        if abs(len(source_words) - len(target_words)) > 2:
            return 0
        
        # Learn word pairs (assuming alignment)
        words_learned = 0
        for i in range(min(len(source_words), len(target_words))):
            # Skip very short words
            if len(source_words[i]) <= 2 or len(target_words[i]) <= 2:
                continue
                
            # Learn the word pair
            success, _ = self.learn_word_pair(
                source_words[i], 
                target_words[i],
                source_lang,
                target_lang
            )
            
            if success:
                words_learned += 1
        
        return words_learned

# Create singleton instance
word_learner = WordLearner()
