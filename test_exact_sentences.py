import os
import django
import json
import requests

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Test sentences that should have exact matches
test_sentences = [
    "Ang ilang mga kababa<PERSON>an, kapag sila ay nanganak, ay hindi umiiyak.",
    "Ang bata ay umiiyak dahil sa gutom.",
    "Hindi siya umiiyak kahit na nasasaktan siya.",
    "Umiiyak ang sanggol kapag gutom siya.",
    "Kapag umiiyak ang bata, kailangan nating alamin kung bakit."
]

# Test the API endpoint
print("Testing API endpoint for exact sentence matches:")
url = "http://127.0.0.1:8000/api/translate/"

for sentence in test_sentences:
    data = {
        'text': sentence,
        'source_lang': 'tgl',
        'target_lang': 'ted'
    }

    response = requests.post(url, json=data)
    print(f"\nSentence: {sentence}")
    print(f"Status code: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        if 'result' in result and 'translation' in result['result']:
            print(f"Translation: {result['result']['translation']}")
            print(f"Confidence: {result['result']['confidence']}")
            print(f"Source: {result['result']['source']}")
        else:
            print(f"Unexpected response format: {result}")
    else:
        print(f"Error response: {response.text}")
