# Generated by Django 4.2.7 on 2025-05-01 05:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('translation_app', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='translationfeedback',
            name='processed',
            field=models.BooleanField(default=False),
        ),
        migrations.CreateModel(
            name='TranslationVersion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('translation', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.CharField(default='system', max_length=255)),
                ('confidence_score', models.FloatField(default=0.95)),
                ('is_active', models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('comprehensive_translation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='versions', to='translation_app.comprehensivetranslation')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='TranslationSuggestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('original_text', models.TextField()),
                ('system_translation', models.TextField()),
                ('suggested_translation', models.TextField()),
                ('status', models.CharField(choices=[('pending', 'Pending Review'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='pending', max_length=20)),
                ('confidence_score', models.FloatField(default=0.8)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('reviewed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_suggestions', to=settings.AUTH_USER_MODEL)),
                ('source_language', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='suggestion_source', to='translation_app.language')),
                ('target_language', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='suggestion_target', to='translation_app.language')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
