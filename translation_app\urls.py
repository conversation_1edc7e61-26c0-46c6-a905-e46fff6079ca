from django.urls import path
from . import views
from . import admin_views
from . import views_attention
from django.shortcuts import render
from django.contrib.admin.views.decorators import staff_member_required

app_name = 'translation'

# Simple test view
def test_buttons(request):
    return render(request, 'translation/test_buttons.html')

urlpatterns = [
    # Main views
    path('', views.index, name='index'),
    path('dictionary/', views.dictionary_lookup, name='dictionary'),
    path('phrases/', views.phrase_search, name='phrases'),

    # API endpoints
    path('api/translate/', views.translate_text, name='translate_text'),
    path('api/translate-ai/', views.translate_with_ai, name='translate_with_ai'),
    path('api/feedback/', views.submit_feedback, name='submit_feedback'),
    path('api/suggest/', views.suggest_translation, name='suggest_translation'),
    path('api/rate_translation/', views.rate_translation, name='rate_translation'),
    path('api/improve_translation/', views.improve_translation, name='improve_translation'),
    path('api/common_translations/', views.get_common_translations, name='get_common_translations'),
    path('api/reload_translations/', views.api_reload_translations, name='api_reload_translations'),  # Public API endpoint
    path('admin/api/reload_translations/', views.reload_translations, name='admin_reload_translations'),  # Admin-only endpoint

    # Attention mechanism endpoints
    path('api/run_attention_mechanism/', views_attention.run_attention_mechanism, name='run_attention_mechanism'),
    path('api/delete_attention_data/', views_attention.delete_attention_data, name='delete_attention_data'),

    # Admin views
    path('add-translation/', admin_views.add_translation_view, name='add_translation'),
    path('metrics/', views.metrics_dashboard, name='metrics_dashboard'),
    path('attention/', views_attention.attention_dashboard, name='attention_dashboard'),
    path('api/attention/<int:translation_id>/', views.get_translation_attention, name='get_translation_attention'),
    # Translation management URL is now in the main URLs file

    # Test views
    path('test-buttons/', test_buttons, name='test_buttons'),
]
