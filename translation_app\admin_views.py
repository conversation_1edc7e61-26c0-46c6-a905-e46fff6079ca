"""
Admin views for the translation app.
"""

from django.shortcuts import render, redirect
from django.contrib import messages
from django.contrib.admin.views.decorators import staff_member_required
from django.db import transaction
from django.utils.translation import gettext as _

from .models import Language, ComprehensiveTranslation, TranslationExample
from .services import get_translation_service

# Import pattern learning module if available
try:
    from .pattern_learning import pattern_learner
    PATTERN_LEARNING_ENABLED = True
except ImportError:
    PATTERN_LEARNING_ENABLED = False

@staff_member_required
def add_translation_view(request):
    """
    Admin view for adding translations.
    """
    languages = Language.objects.all().order_by('name')

    if request.method == 'POST':
        source_lang_code = request.POST.get('source_lang')
        target_lang_code = request.POST.get('target_lang')
        source_text = request.POST.get('source_text')
        target_text = request.POST.get('target_text')
        context = request.POST.get('context', '')
        part_of_speech = request.POST.get('part_of_speech', 'word')
        # Automatically set confidence based on part of speech and context
        confidence = 0.9  # Default confidence value
        if context and 'Bible' in context:
            confidence = 0.95  # Higher confidence for Bible translations
        elif part_of_speech in ['noun', 'verb']:
            confidence = 0.92  # Higher confidence for nouns and verbs
        elif part_of_speech == 'phrase':
            confidence = 0.88  # Slightly lower for phrases
        extract_words = 'extract_words' in request.POST
        add_reverse = 'add_reverse' in request.POST

        try:
            # Get language objects
            source_language = Language.objects.get(code=source_lang_code)
            target_language = Language.objects.get(code=target_lang_code)

            # Add the translation to the database
            with transaction.atomic():
                # Create or update the translation
                trans, created = ComprehensiveTranslation.objects.get_or_create(
                    base_word=source_text,
                    source_language=source_language,
                    target_language=target_language,
                    defaults={
                        'translation': target_text,
                        'part_of_speech': part_of_speech,
                        'notes': f'Context: {context}' if context else 'Added manually'
                    }
                )

                if not created:
                    trans.translation = target_text
                    trans.part_of_speech = part_of_speech
                    if context:
                        trans.notes = f'Context: {context}'
                    trans.save()

                # Add an example
                TranslationExample.objects.get_or_create(
                    comprehensive_translation=trans,
                    source_text=source_text,
                    target_text=target_text
                )

                # Add reverse translation if requested
                if add_reverse:
                    reverse_trans, reverse_created = ComprehensiveTranslation.objects.get_or_create(
                        base_word=target_text,
                        source_language=target_language,
                        target_language=source_language,
                        defaults={
                            'translation': source_text,
                            'part_of_speech': part_of_speech,
                            'notes': f'Context: {context}' if context else 'Added manually (reverse)'
                        }
                    )

                    if not reverse_created:
                        reverse_trans.translation = source_text
                        reverse_trans.part_of_speech = part_of_speech
                        if context:
                            reverse_trans.notes = f'Context: {context}'
                        reverse_trans.save()

                    # Add example for reverse translation
                    TranslationExample.objects.get_or_create(
                        comprehensive_translation=reverse_trans,
                        source_text=target_text,
                        target_text=source_text
                    )

                # Use pattern learning to extract patterns
                if PATTERN_LEARNING_ENABLED:
                    try:
                        pattern_learner.learn_from_example(
                            source_text,
                            target_text,
                            source_lang_code,
                            target_lang_code
                        )
                    except Exception as e:
                        # Log the error but continue with the rest of the process
                        print(f"Error learning patterns: {str(e)}")

                # Extract individual words and phrases if requested
                if extract_words and ' ' in source_text and ' ' in target_text:
                    # This is a simplified implementation
                    # In a production system, you would use more sophisticated alignment algorithms
                    source_words = source_text.split()
                    target_words = target_text.split()

                    # Only attempt word extraction if the number of words is similar
                    if 0.5 <= len(source_words) / len(target_words) <= 2.0:
                        # Prepare for batch processing
                        words_to_process = []

                        # First pass: collect all word pairs to process
                        for i, source_word in enumerate(source_words):
                            if i < len(target_words):
                                target_word = target_words[i]

                                # Skip very short words
                                if len(source_word) <= 2 or len(target_word) <= 2:
                                    continue

                                # Add to processing list
                                words_to_process.append((source_word, target_word))

                        # Second pass: process in batches to improve performance
                        batch_size = 10  # Process 10 words at a time
                        for i in range(0, len(words_to_process), batch_size):
                            batch = words_to_process[i:i+batch_size]

                            with transaction.atomic():
                                for source_word, target_word in batch:
                                    # Check if translation already exists
                                    existing = ComprehensiveTranslation.objects.filter(
                                        base_word=source_word,
                                        source_language=source_language,
                                        target_language=target_language
                                    ).first()

                                    if existing:
                                        # Update existing translation if confidence is low
                                        if getattr(existing, 'confidence', 0) < 0.8:
                                            existing.translation = target_word
                                            existing.notes = f'Updated from: {source_text} → {target_text}'
                                            existing._skip_signal = True  # Skip signal processing
                                            existing.save()
                                    else:
                                        # Create new translation
                                        trans = ComprehensiveTranslation(
                                            base_word=source_word,
                                            translation=target_word,
                                            source_language=source_language,
                                            target_language=target_language,
                                            part_of_speech='word',
                                            notes=f'Extracted from: {source_text} → {target_text}'
                                        )
                                        # Set flag to avoid triggering signals
                                        trans._skip_signal = True
                                        trans.save()

                                    # Add reverse translation if requested
                                    if add_reverse:
                                        existing_reverse = ComprehensiveTranslation.objects.filter(
                                            base_word=target_word,
                                            source_language=target_language,
                                            target_language=source_language
                                        ).first()

                                        if existing_reverse:
                                            # Update existing translation if confidence is low
                                            if getattr(existing_reverse, 'confidence', 0) < 0.8:
                                                existing_reverse.translation = source_word
                                                existing_reverse.notes = f'Updated from: {target_text} → {source_text} (reverse)'
                                                existing_reverse._skip_signal = True  # Skip signal processing
                                                existing_reverse.save()
                                        else:
                                            # Create new reverse translation
                                            reverse_trans = ComprehensiveTranslation(
                                                base_word=target_word,
                                                translation=source_word,
                                                source_language=target_language,
                                                target_language=source_language,
                                                part_of_speech='word',
                                                notes=f'Extracted from: {target_text} → {source_text} (reverse)'
                                            )
                                            # Set flag to avoid triggering signals
                                            reverse_trans._skip_signal = True
                                            reverse_trans.save()

            # Schedule a delayed reload of the translation service
            # This allows the response to be sent to the user first
            try:
                from threading import Timer
                import logging
                logger = logging.getLogger(__name__)

                def delayed_reload():
                    try:
                        # Reload the translation service
                        translation_service = get_translation_service()
                        translation_service.load_translations(clear_cache=True)
                        print("Translation service reloaded successfully in background")

                        # Update the static translations file
                        try:
                            from .utils.static_file_updater import update_static_translations_file
                            update_static_translations_file(background=False)  # Do it synchronously here
                            print("Static translations file updated successfully")
                        except Exception as e:
                            print(f"Error updating static translations file: {str(e)}")
                            logger.error(f"Error updating static translations file: {str(e)}")
                    except Exception as e:
                        print(f"Error in delayed reload: {str(e)}")
                        logger.error(f"Error in delayed reload: {str(e)}")

                # Reload after 1 second
                Timer(1.0, delayed_reload).start()
                print("Scheduled translation service reload and static file update")
            except Exception as e:
                # If threading fails, do a normal reload but with minimal work
                print(f"Threading failed, doing minimal reload: {str(e)}")
                logger.error(f"Threading failed, doing minimal reload: {str(e)}")
                translation_service = get_translation_service()
                translation_service.load_translations(clear_cache=False)

            messages.success(request, _('Translation added successfully! The static file will be updated in the background.'))
            return redirect('translation:add_translation')

        except Language.DoesNotExist:
            messages.error(request, _('Language not found.'))
        except Exception as e:
            messages.error(request, _('Error adding translation: %s') % str(e))

    return render(request, 'admin/add_translation.html', {
        'languages': languages,
        'title': _('Add Translation'),
    })
