#!/bin/bash
# Wrapper script to run update_translations.py with the correct Python environment

# Change to the project directory
cd "$(dirname "$0")"

# Activate the virtual environment if it exists
if [ -d "venv" ]; then
    echo "Activating virtual environment..."
    source venv/bin/activate
elif [ -d "env" ]; then
    echo "Activating virtual environment..."
    source env/bin/activate
elif [ -d ".venv" ]; then
    echo "Activating virtual environment..."
    source .venv/bin/activate
fi

# Run the Python script with all arguments passed to this script
echo "Running update_translations.py with arguments: $@"
python update_translations.py "$@"

# Print completion message
echo "Script execution completed."
