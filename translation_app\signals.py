"""
Signal handlers for the translation app.
"""
import logging
from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import ComprehensiveTranslation, TranslationVersion, TranslationFeedback, TranslationRating

logger = logging.getLogger(__name__)

@receiver(post_save, sender=ComprehensiveTranslation)
def update_static_file_on_translation_save(sender, instance, created, **kwargs):
    """
    Update the static translations file when a translation is saved.
    """
    # Skip if the _skip_signal flag is set
    if hasattr(instance, '_skip_signal') and instance._skip_signal:
        return

    # Check if automatic updates are disabled
    try:
        from django.conf import settings
        if getattr(settings, 'DISABLE_AUTO_PROCESSES', False):
            logger.info(f"Automatic static file update skipped for translation: {instance.base_word}")
            return
    except Exception as e:
        logger.error(f"Error checking auto processes setting: {str(e)}")
        # Continue with default behavior if there's an error

    # Defer static file update to the scheduled optimization task
    logger.info(f"Deferred static file update for translation: {instance.base_word} - will be processed by scheduled optimization task")

@receiver(post_save, sender=TranslationVersion)
def update_static_file_on_version_save(sender, instance, created, **kwargs):
    """
    Update the static translations file when a translation version is saved.
    """
    # Check if automatic updates are disabled
    try:
        from django.conf import settings
        if getattr(settings, 'DISABLE_AUTO_PROCESSES', False):
            logger.info(f"Automatic static file update skipped for translation version: {instance.comprehensive_translation.base_word}")
            return
    except Exception as e:
        logger.error(f"Error checking auto processes setting: {str(e)}")
        # Continue with default behavior if there's an error

    # Defer static file update to the scheduled optimization task
    logger.info(f"Deferred static file update for translation version: {instance.comprehensive_translation.base_word} - will be processed by scheduled optimization task")

@receiver(post_save, sender=TranslationFeedback)
def update_static_file_on_feedback_save(sender, instance, created, **kwargs):
    """
    Update the static translations file when feedback is saved.
    """
    # Only update if there's a suggested translation
    if instance.suggested_translation:
        # Check if automatic updates are disabled
        try:
            from django.conf import settings
            if getattr(settings, 'DISABLE_AUTO_PROCESSES', False):
                logger.info(f"Automatic static file update skipped for feedback: {instance.original_text}")
                return
        except Exception as e:
            logger.error(f"Error checking auto processes setting: {str(e)}")
            # Continue with default behavior if there's an error

        # Defer static file update to the scheduled optimization task
        logger.info(f"Deferred static file update for feedback: {instance.original_text} - will be processed by scheduled optimization task")

@receiver(post_save, sender=TranslationRating)
def update_static_file_on_rating_save(sender, instance, created, **kwargs):
    """
    Update the static translations file when a rating is saved.
    """
    # Only update if the rating is high (4 or 5)
    if instance.rating >= 4:
        # Check if automatic updates are disabled
        try:
            from django.conf import settings
            if getattr(settings, 'DISABLE_AUTO_PROCESSES', False):
                logger.info(f"Automatic static file update skipped for rating: {instance.comprehensive_translation.base_word}")
                return
        except Exception as e:
            logger.error(f"Error checking auto processes setting: {str(e)}")
            # Continue with default behavior if there's an error

        # Defer static file update to the scheduled optimization task
        logger.info(f"Deferred static file update for high rating: {instance.comprehensive_translation.base_word} - will be processed by scheduled optimization task")