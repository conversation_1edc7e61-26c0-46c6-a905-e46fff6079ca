// Custom JavaScript to ensure theme toggle works properly

document.addEventListener('DOMContentLoaded', function() {
    // Hide the skip to main content link
    const skipLinks = document.querySelectorAll('a[href="#content-start"], .skip-to-content-link');
    skipLinks.forEach(function(link) {
        link.style.display = 'none';
        link.style.visibility = 'hidden';
        link.style.opacity = '0';
        link.style.position = 'absolute';
        link.style.left = '-9999px';
        link.style.top = '-9999px';
    });

    // Ensure theme toggle works
    const themeToggle = document.querySelector('.theme-toggle');
    if (themeToggle) {
        // Make sure it's visible
        themeToggle.style.display = 'inline-block';
        themeToggle.style.visibility = 'visible';
        themeToggle.style.opacity = '1';
        
        // Ensure the click handler is working
        const toggleButtons = themeToggle.querySelectorAll('button');
        toggleButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const theme = this.dataset.theme;
                document.documentElement.dataset.theme = theme;
                localStorage.setItem('theme', theme);
                
                // Update active state
                toggleButtons.forEach(btn => btn.setAttribute('aria-pressed', 'false'));
                this.setAttribute('aria-pressed', 'true');
            });
        });
    }
    
    // Set initial theme based on localStorage or system preference
    const storedTheme = localStorage.getItem('theme');
    if (storedTheme) {
        document.documentElement.dataset.theme = storedTheme;
        const activeButton = document.querySelector(`.theme-toggle button[data-theme="${storedTheme}"]`);
        if (activeButton) {
            activeButton.setAttribute('aria-pressed', 'true');
        }
    } else {
        // Default to light theme
        document.documentElement.dataset.theme = 'light';
        const lightButton = document.querySelector('.theme-toggle button[data-theme="light"]');
        if (lightButton) {
            lightButton.setAttribute('aria-pressed', 'true');
        }
    }
});
