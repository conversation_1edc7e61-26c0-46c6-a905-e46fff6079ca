{% extends "admin/base_site.html" %}
{% load i18n %}

{% block title %}{% trans "Translation Management" %} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; {% trans 'Translation Management' %}
</div>
{% endblock %}

{% block content %}
<div id="content-main">
    <div class="module">
        <h1>{% trans "Translation Management" %}</h1>
        <p>{% trans "Use the buttons below to manage the translation system." %}</p>
        
        <div class="form-row">
            <form method="post">
                {% csrf_token %}
                <input type="hidden" name="action" value="update_attention">
                <button type="submit" class="button">{% trans "Update Attention" %}</button>
                <p class="help">{% trans "Enhance translations using the attention mechanism for better quality with longer sentences." %}</p>
            </form>
        </div>
        
        <div class="form-row">
            <form method="post">
                {% csrf_token %}
                <input type="hidden" name="action" value="update_static">
                <button type="submit" class="button">{% trans "Update Static Files" %}</button>
                <p class="help">{% trans "Update static translation files used by the frontend for faster access." %}</p>
            </form>
        </div>
        
        <div class="form-row">
            <form method="post">
                {% csrf_token %}
                <input type="hidden" name="action" value="update_feedback">
                <button type="submit" class="button">{% trans "Process Feedback" %}</button>
                <p class="help">{% trans "Process pending translation feedback and generate suggestions from user input." %}</p>
            </form>
        </div>
        
        <div class="form-row">
            <form method="post">
                {% csrf_token %}
                <input type="hidden" name="action" value="update_translations">
                <button type="submit" class="button">{% trans "Update System" %}</button>
                <p class="help">{% trans "Update the entire translation system with all processed translations." %}</p>
            </form>
        </div>
    </div>
</div>
{% endblock %}
