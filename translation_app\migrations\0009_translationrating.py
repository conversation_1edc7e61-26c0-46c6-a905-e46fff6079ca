# Generated by Django 4.2.7 on 2025-05-04 02:04

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('translation_app', '0008_fix_migration_dependencies'),
    ]

    operations = [
        migrations.CreateModel(
            name='TranslationRating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.IntegerField(choices=[(1, '1 - Poor'), (2, '2 - Fair'), (3, '3 - Good'), (4, '4 - Very Good'), (5, '5 - Excellent')])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('comprehensive_translation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ratings', to='translation_app.comprehensivetranslation')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
