import json
import os
import re
from django.core.management.base import BaseCommand
from django.db.models import Count, Avg
from translation_app.models import Language, ComprehensiveTranslation, TranslationRating
from translation_app.utils import extract_words_from_phrase
from translation_app.utils.platform_detection import PLATFORM_SETTINGS

class Command(BaseCommand):
    help = 'Export all translations to a static JSON file for client-side use'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reduced',
            action='store_true',
            help='Create a reduced version of the static file with only essential translations'
        )
        parser.add_argument(
            '--max-size',
            type=int,
            default=PLATFORM_SETTINGS['max_static_file_size'],
            help=f'Maximum size of the static file in bytes (default: {PLATFORM_SETTINGS["max_static_file_size"]})'
        )
        parser.add_argument(
            '--essential-only',
            action='store_true',
            help='Include only essential translations (high confidence, frequently used)'
        )

    def handle(self, *args, **options):
        reduced = options.get('reduced', False)
        max_size = options.get('max_size', PLATFORM_SETTINGS['max_static_file_size'])
        essential_only = options.get('essential_only', False)

        if reduced:
            self.stdout.write('Exporting reduced translations to static JSON file...')
        else:
            self.stdout.write('Exporting translations to static JSON file...')

        # Create the static directory if it doesn't exist
        static_dir = os.path.join('static', 'translation_app', 'js')
        os.makedirs(static_dir, exist_ok=True)

        # Also check the alternate path
        alt_static_dir = os.path.join('translation_app', 'static', 'translation_app', 'js')
        os.makedirs(alt_static_dir, exist_ok=True)

        # Get all language pairs
        language_pairs = [
            ('tgl', 'ted'),  # Tagalog to Teduray
            ('ted', 'tgl')   # Teduray to Tagalog
        ]

        all_translations = {}
        word_by_word_mappings = {}

        for source_lang, target_lang in language_pairs:
            self.stdout.write(f'Processing {source_lang} to {target_lang}...')

            # Get language objects
            try:
                source_lang_obj = Language.objects.get(code=source_lang)
                target_lang_obj = Language.objects.get(code=target_lang)
            except Language.DoesNotExist:
                self.stdout.write(self.style.ERROR(f'Language not found: {source_lang} or {target_lang}'))
                continue

            # Get translations for this language pair
            query = ComprehensiveTranslation.objects.filter(
                source_language=source_lang_obj,
                target_language=target_lang_obj
            ).select_related('source_language', 'target_language').prefetch_related('versions')

            # If we're creating a reduced version, filter to only include essential translations
            if reduced or essential_only:
                self.stdout.write(f'Filtering to include only essential translations...')

                # Get translations with high confidence or high ratings
                high_confidence_ids = []
                for ct in query:
                    # Check if this translation has a high-confidence version
                    active_version = ct.versions.filter(is_active=True).first()
                    if active_version and active_version.confidence_score >= 0.8:
                        high_confidence_ids.append(ct.id)

                # Get translations with high ratings
                high_rating_ids = list(TranslationRating.objects.filter(
                    comprehensive_translation__source_language=source_lang_obj,
                    comprehensive_translation__target_language=target_lang_obj,
                    rating__gte=4
                ).values_list('comprehensive_translation_id', flat=True).distinct())

                # Get frequently used translations
                frequently_used_ids = list(ComprehensiveTranslation.objects.filter(
                    source_language=source_lang_obj,
                    target_language=target_lang_obj
                ).annotate(
                    rating_count=Count('ratings')
                ).filter(
                    rating_count__gte=2
                ).values_list('id', flat=True))

                # Get single words (these are more important than phrases)
                single_word_ids = list(ComprehensiveTranslation.objects.filter(
                    source_language=source_lang_obj,
                    target_language=target_lang_obj,
                    part_of_speech__in=['word', 'noun', 'verb', 'adjective', 'adverb']
                ).values_list('id', flat=True))

                # Get the most recently updated translations
                recent_ids = list(ComprehensiveTranslation.objects.filter(
                    source_language=source_lang_obj,
                    target_language=target_lang_obj
                ).order_by('-updated_at')[:1000].values_list('id', flat=True))

                # Combine all the IDs
                essential_ids = set(high_confidence_ids + high_rating_ids + frequently_used_ids + single_word_ids + recent_ids)

                # Limit to a reasonable number if we're in reduced mode
                if reduced and len(essential_ids) > 5000:
                    self.stdout.write(f'Limiting to 5000 most essential translations (from {len(essential_ids)})')
                    essential_ids = list(essential_ids)[:5000]

                # Filter the query to only include essential translations
                query = query.filter(id__in=essential_ids)

                self.stdout.write(f'Selected {query.count()} essential translations')

            # Get the translations
            translations = query

            # Format the translations for the client
            direction = f'{source_lang}_to_{target_lang}'
            all_translations[direction] = {}
            word_by_word_mappings[direction] = {}

            count = 0
            for ct in translations:
                # Get the active version if available
                active_version = ct.versions.filter(is_active=True).first()

                # Use the version's translation if available, otherwise use the base translation
                translation_text = active_version.translation if active_version else ct.translation
                confidence_score = active_version.confidence_score if active_version else 0.9

                # Add to the translations data
                all_translations[direction][ct.base_word.lower()] = {
                    'translation': translation_text,
                    'confidence': confidence_score,
                    'source': 'database',
                    'part_of_speech': ct.part_of_speech,
                    'notes': ct.notes
                }

                # Extract individual words for word-by-word mapping
                # Only do this for shorter phrases (less than 50 characters)
                if len(ct.base_word) < 50:
                    # Add the word to the word-by-word mappings
                    word_by_word_mappings[direction][ct.base_word.lower()] = {
                        'translation': translation_text,
                        'confidence': confidence_score
                    }
                else:
                    # For longer phrases, extract individual words
                    source_words = extract_words_from_phrase(ct.base_word)

                    # If we have a reasonable number of words, try to map them
                    if 2 <= len(source_words) <= 20:
                        self.stdout.write(f'Extracting words from longer phrase: {ct.base_word[:50]}...')

                        # Add each word to the word-by-word mappings if not already present
                        for word in source_words:
                            if word not in word_by_word_mappings[direction]:
                                # Check if we have a direct translation for this word
                                existing_translation = ComprehensiveTranslation.objects.filter(
                                    base_word__iexact=word,
                                    source_language=source_lang_obj,
                                    target_language=target_lang_obj
                                ).first()

                                if existing_translation:
                                    # Use the existing translation
                                    word_translation = existing_translation.translation
                                    word_confidence = 0.9
                                    self.stdout.write(f'  Found existing translation for word: {word} → {word_translation}')
                                else:
                                    # No direct translation available
                                    word_by_word_mappings[direction][word] = {
                                        'translation': f'[{word}]',  # Placeholder
                                        'confidence': 0.5,
                                        'needs_translation': True
                                    }
                                    self.stdout.write(f'  Added placeholder for word: {word}')

                count += 1

            self.stdout.write(f'Exported {count} translations for {source_lang} to {target_lang}')
            self.stdout.write(f'Created {len(word_by_word_mappings[direction])} word-by-word mappings')

        # Determine the output file name
        if reduced:
            output_file = os.path.join(static_dir, 'essential_translations.js')
            alt_output_file = os.path.join(alt_static_dir, 'essential_translations.js')
        else:
            output_file = os.path.join(static_dir, 'all_translations.js')
            alt_output_file = os.path.join(alt_static_dir, 'all_translations.js')

        # Write translations to file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('const ALL_TRANSLATIONS = ')
            json.dump(all_translations, f, ensure_ascii=False, indent=2)
            f.write(';\n\n')

            # Add word-by-word mappings
            f.write('const WORD_BY_WORD_MAPPINGS = ')
            json.dump(word_by_word_mappings, f, ensure_ascii=False, indent=2)
            f.write(';\n')

        # Also write to the alternate location if it's different
        if output_file != alt_output_file:
            try:
                # Copy the file to the alternate location
                with open(output_file, 'r', encoding='utf-8') as src:
                    content = src.read()

                with open(alt_output_file, 'w', encoding='utf-8') as dest:
                    dest.write(content)

                self.stdout.write(f'Also copied to alternate location: {alt_output_file}')
            except Exception as e:
                self.stdout.write(self.style.WARNING(f'Error copying to alternate location: {str(e)}'))

        # Check the file size
        file_size = os.path.getsize(output_file)
        self.stdout.write(f'File size: {file_size} bytes')

        # If we're in reduced mode, also create a symlink or copy to the main file
        if reduced:
            try:
                main_file = os.path.join(static_dir, 'all_translations.js')
                alt_main_file = os.path.join(alt_static_dir, 'all_translations.js')

                # Create a symlink or copy the file
                if os.path.exists(main_file):
                    os.remove(main_file)

                # Try to create a symlink first
                try:
                    os.symlink(output_file, main_file)
                    self.stdout.write(f'Created symlink from {output_file} to {main_file}')
                except:
                    # If symlink fails, copy the file
                    with open(output_file, 'r', encoding='utf-8') as src:
                        content = src.read()

                    with open(main_file, 'w', encoding='utf-8') as dest:
                        dest.write(content)

                    self.stdout.write(f'Copied from {output_file} to {main_file}')

                # Also handle the alternate location
                if os.path.exists(alt_main_file):
                    os.remove(alt_main_file)

                with open(alt_output_file, 'r', encoding='utf-8') as src:
                    content = src.read()

                with open(alt_main_file, 'w', encoding='utf-8') as dest:
                    dest.write(content)

                self.stdout.write(f'Copied from {alt_output_file} to {alt_main_file}')

            except Exception as e:
                self.stdout.write(self.style.WARNING(f'Error creating symlink or copy: {str(e)}'))

        self.stdout.write(self.style.SUCCESS(f'Successfully exported translations to {output_file}'))
