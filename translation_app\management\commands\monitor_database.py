"""
Management command to monitor database locking issues.
"""

import os
import time
import logging
import sqlite3
from django.core.management.base import BaseCommand
from django.conf import settings
from django.db import connection

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Monitor database for locking issues and perform maintenance'

    def add_arguments(self, parser):
        parser.add_argument(
            '--vacuum',
            action='store_true',
            help='Vacuum the database to optimize storage and performance',
        )
        parser.add_argument(
            '--check-locks',
            action='store_true',
            help='Check for database locks',
        )
        parser.add_argument(
            '--optimize',
            action='store_true',
            help='Apply PRAGMA optimizations to the database',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='Perform all maintenance operations',
        )
        parser.add_argument(
            '--interval',
            type=int,
            default=0,
            help='Run continuously with the specified interval in seconds',
        )

    def handle(self, *args, **options):
        vacuum = options.get('vacuum', False)
        check_locks = options.get('check_locks', False)
        optimize = options.get('optimize', False)
        all_ops = options.get('all', False)
        interval = options.get('interval', 0)

        if all_ops:
            vacuum = check_locks = optimize = True

        if not any([vacuum, check_locks, optimize]):
            self.stdout.write('No operations specified. Use --help to see available options.')
            return

        # Run once or continuously based on interval
        while True:
            if vacuum or all_ops:
                self.vacuum_database()

            if check_locks or all_ops:
                self.check_locks()

            if optimize or all_ops:
                self.optimize_database()

            if interval <= 0:
                break

            self.stdout.write(f'Waiting {interval} seconds before next check...')
            time.sleep(interval)

    def vacuum_database(self):
        """Vacuum the database to optimize storage and performance."""
        self.stdout.write('Vacuuming database...')
        try:
            # Close Django's connection first
            connection.close()

            # Connect directly to the database
            db_path = settings.DATABASES['default']['NAME']
            conn = sqlite3.connect(db_path, timeout=300)
            cursor = conn.cursor()

            # Run PRAGMA optimize first
            cursor.execute("PRAGMA optimize;")
            self.stdout.write('PRAGMA optimize completed')

            # Then vacuum the database
            start_time = time.time()
            cursor.execute("VACUUM;")
            duration = time.time() - start_time
            
            # Close the connection
            conn.close()
            
            self.stdout.write(self.style.SUCCESS(
                f'Database vacuum completed successfully in {duration:.2f} seconds'
            ))
            return True
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error vacuuming database: {str(e)}'))
            return False

    def check_locks(self):
        """Check for database locks."""
        self.stdout.write('Checking for database locks...')
        try:
            # Close Django's connection first
            connection.close()

            # Connect directly to the database
            db_path = settings.DATABASES['default']['NAME']
            conn = sqlite3.connect(db_path, timeout=5)  # Short timeout to detect locks
            cursor = conn.cursor()

            # Try to get an exclusive lock
            try:
                cursor.execute("BEGIN EXCLUSIVE;")
                cursor.execute("COMMIT;")
                self.stdout.write(self.style.SUCCESS('No database locks detected'))
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e):
                    self.stdout.write(self.style.WARNING('Database is currently locked!'))
                    
                    # Try to get information about the lock
                    try:
                        # On some systems, we can query for lock information
                        cursor.execute("PRAGMA lock_status;")
                        lock_info = cursor.fetchall()
                        self.stdout.write(f'Lock status: {lock_info}')
                    except:
                        pass
                else:
                    raise

            # Close the connection
            conn.close()
            return True
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error checking database locks: {str(e)}'))
            return False

    def optimize_database(self):
        """Apply PRAGMA optimizations to the database."""
        self.stdout.write('Optimizing database...')
        try:
            # Close Django's connection first
            connection.close()

            # Connect directly to the database
            db_path = settings.DATABASES['default']['NAME']
            conn = sqlite3.connect(db_path, timeout=300)
            cursor = conn.cursor()

            # Apply optimizations
            cursor.execute("PRAGMA busy_timeout = 120000;")
            cursor.execute("PRAGMA journal_mode = DELETE;")
            cursor.execute("PRAGMA synchronous = NORMAL;")
            cursor.execute("PRAGMA temp_store = MEMORY;")
            cursor.execute("PRAGMA cache_size = 5000;")
            cursor.execute("PRAGMA page_size = 4096;")
            cursor.execute("PRAGMA locking_mode = EXCLUSIVE;")

            # Get current settings
            settings_queries = [
                "PRAGMA busy_timeout;",
                "PRAGMA journal_mode;",
                "PRAGMA synchronous;",
                "PRAGMA temp_store;",
                "PRAGMA cache_size;",
                "PRAGMA page_size;",
                "PRAGMA locking_mode;"
            ]

            self.stdout.write('Current database settings:')
            for query in settings_queries:
                cursor.execute(query)
                setting = cursor.fetchone()
                setting_name = query.replace("PRAGMA ", "").replace(";", "")
                self.stdout.write(f'  {setting_name}: {setting[0]}')

            # Close the connection
            conn.close()
            
            self.stdout.write(self.style.SUCCESS('Database optimization completed successfully'))
            return True
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error optimizing database: {str(e)}'))
            return False
