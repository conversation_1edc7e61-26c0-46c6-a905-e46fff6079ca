{% extends "admin/login_base.html" %}
{% load i18n static jazzmin %}

{% block bodyclass %}login-page{% endblock %}

{% block title %}{{ _('Log in') }} | {{ site_title }}{% endblock %}

{% block content %}
<div class="login-box">
    <div class="login-logo">
        <h1>{{ site_header|default:_('Django Administration') }}</h1>
    </div>

    <div class="card login-card">
        <div class="card-body login-card-body">
            <p class="login-box-msg">{{ _('Sign in to start your session') }}</p>

            <form action="{{ app_path }}" method="post" id="login-form">
                {% csrf_token %}

                <div class="input-group mb-3">
                    <input type="text" name="username" class="form-control" placeholder="{{ _('Username') }}" required autofocus>
                    <div class="input-group-append">
                        <div class="input-group-text">
                            <span class="fas fa-user"></span>
                        </div>
                    </div>
                </div>

                <div class="input-group mb-3">
                    <input type="password" name="password" class="form-control" placeholder="{{ _('Password') }}" required>
                    <div class="input-group-append">
                        <div class="input-group-text">
                            <span class="fas fa-lock"></span>
                        </div>
                    </div>
                </div>

                {% if form.errors or form.non_field_errors %}
                <div class="alert alert-danger">
                    {% if form.non_field_errors %}
                        {{ form.non_field_errors }}
                    {% endif %}
                    {% if form.errors and not form.non_field_errors %}
                    {% trans 'Please correct the errors below.' %}
                    {% endif %}
                </div>
                {% endif %}

                <div class="row">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary btn-block">{{ _('Log in') }}</button>
                    </div>
                </div>

                <input type="hidden" name="next" value="{{ next }}">
            </form>

            <p class="mb-0 mt-3">
                <a href="/" class="text-center">{{ _('Return to Translation Platform') }}</a>
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extrajs %}
<script type="text/javascript">
document.getElementById('id_username').focus();
</script>
{% endblock %}

{% block style %}
<style>
    body.login-page {
        background: linear-gradient(135deg, #f5f0e1 0%, #e8e4d8 100%);
        background-size: cover;
        background-repeat: no-repeat;
        background-attachment: fixed;
        height: 100vh;
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0;
        padding: 0;
    }

    /* Add decorative elements to the background */
    body.login-page:before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
            radial-gradient(circle at 10% 20%, rgba(0, 123, 255, 0.05) 0%, transparent 20%),
            radial-gradient(circle at 90% 80%, rgba(0, 123, 255, 0.05) 0%, transparent 20%),
            radial-gradient(circle at 50% 50%, rgba(0, 123, 255, 0.03) 0%, transparent 40%);
        z-index: -1;
    }

    .login-box {
        width: 400px;
        margin: 0 auto;
        z-index: 10;
    }

    .login-logo {
        margin-bottom: 20px;
        text-align: center;
    }

    .login-logo h1 {
        font-size: 28px;
        color: #333;
        font-weight: 600;
    }

    .login-card {
        border-radius: 10px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        border: none;
        position: relative;
        overflow: hidden;
    }

    .login-card:before {
        content: "";
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        height: 5px;
        background: linear-gradient(90deg, #007bff, #6610f2);
    }

    .login-card-body {
        padding: 30px;
        border-radius: 10px;
        background-color: #fff;
    }

    .login-box-msg {
        margin-bottom: 15px;
        font-size: 16px;
        color: #555;
    }

    .btn-primary {
        background-color: #007bff;
        border-color: #007bff;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        background-color: #0069d9;
        border-color: #0062cc;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .input-group-text {
        background-color: #f8f9fa;
        border-color: #ced4da;
    }

    .form-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    @media (max-width: 576px) {
        .login-box {
            width: 90%;
            margin-top: 20%;
        }
    }
</style>
{% endblock %}
