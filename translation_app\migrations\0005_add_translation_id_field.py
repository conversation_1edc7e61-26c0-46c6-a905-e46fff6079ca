# Generated by Django 3.2.25 on 2025-05-02 15:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('translation_app', '0004_merge_20250502_2328'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='aitranslationlog',
            name='ai_log_status_idx',
        ),
        migrations.RemoveIndex(
            model_name='aitranslationlog',
            name='ai_log_lang_idx',
        ),
        migrations.RemoveIndex(
            model_name='aitranslationlog',
            name='ai_log_time_idx',
        ),
        migrations.RemoveIndex(
            model_name='aitranslationreview',
            name='ai_review_status_idx',
        ),
        migrations.RemoveIndex(
            model_name='aitranslationreview',
            name='ai_review_lang_idx',
        ),
        migrations.RemoveField(
            model_name='attentiondata',
            name='created_at',
        ),
        migrations.RemoveField(
            model_name='attentiondata',
            name='translation_id',
        ),
        migrations.AddField(
            model_name='aitranslationreview',
            name='translation_id',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='aitranslationlog',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='aitranslationreview',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='aitranslationsettings',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AddIndex(
            model_name='aitranslationlog',
            index=models.Index(fields=['status'], name='translation_status_5568a6_idx'),
        ),
        migrations.AddIndex(
            model_name='aitranslationlog',
            index=models.Index(fields=['source_lang', 'target_lang'], name='translation_source__3752ed_idx'),
        ),
        migrations.AddIndex(
            model_name='aitranslationlog',
            index=models.Index(fields=['timestamp'], name='translation_timesta_0c936b_idx'),
        ),
        migrations.AddIndex(
            model_name='aitranslationreview',
            index=models.Index(fields=['status'], name='translation_status_15b77e_idx'),
        ),
        migrations.AddIndex(
            model_name='aitranslationreview',
            index=models.Index(fields=['source_language', 'target_language'], name='translation_source__80037b_idx'),
        ),
    ]
