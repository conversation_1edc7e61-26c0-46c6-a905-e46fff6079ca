import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'translation.settings')
django.setup()

# Import the models
from translation_app.models import ComprehensiveTranslation, Language

# Get language objects
tagalog = Language.objects.get(code='tgl')
teduray = Language.objects.get(code='ted')

# Check for Matthew 2:16 translations
print("Checking for Matthew 2:16 translations in the database...")

# Check for key phrases
key_phrases = [
    ('tgl', 'Hero<PERSON>'),
    ('tgl', 'nagalit na mainam'),
    ('tgl', 'mga Pantas na lalake'),
    ('tgl', 'dalawang taon'),
    ('ted', 'Herode'),
    ('ted', 'toow fo ménkérit'),
    ('ted', 'de gétuwan do étéw'),
    ('ted', 'ruwo gébélintuwa'),
]

for lang_code, phrase in key_phrases:
    source_lang = tagalog if lang_code == 'tgl' else teduray
    target_lang = teduray if lang_code == 'tgl' else tagalog
    
    translations = ComprehensiveTranslation.objects.filter(
        base_word__icontains=phrase,
        source_language=source_lang,
        target_language=target_lang
    )
    
    print(f"Found {translations.count()} translations for '{phrase}' ({lang_code}):")
    for trans in translations:
        print(f"  - {trans.base_word} → {trans.translation}")

print("\nCheck complete!")
