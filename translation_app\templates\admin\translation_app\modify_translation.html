{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block extrahead %}{{ block.super }}
<script type="text/javascript" src="{% url 'admin:jsi18n' %}"></script>
{{ media }}
{% endblock %}

{% block extrastyle %}{{ block.super }}<link rel="stylesheet" type="text/css" href="{% static "admin/css/forms.css" %}">{% endblock %}

{% block coltype %}colM{% endblock %}

{% block bodyclass %}{{ block.super }} app-{{ opts.app_label }} model-{{ opts.model_name }} change-form{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; <a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a>
&rsaquo; <a href="{% url 'admin:translation_app_aitranslationreview_changelist' %}">AI Translation Reviews</a>
&rsaquo; Modify Translation
</div>
{% endblock %}

{% block content %}
<div id="content-main">
    <form method="post">
        {% csrf_token %}
        <div>
            <fieldset class="module aligned">
                <div class="form-row">
                    <div class="field-box">
                        <label>Source Text:</label>
                        <div class="readonly">{{ review.source_text }}</div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="field-box">
                        <label>Source Language:</label>
                        <div class="readonly">{{ review.source_language }}</div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="field-box">
                        <label>Target Language:</label>
                        <div class="readonly">{{ review.target_language }}</div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="field-box">
                        <label>AI Translation:</label>
                        <div class="readonly">{{ review.ai_translation }}</div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="field-box">
                        <label for="id_modified_translation">Modified Translation:</label>
                        <textarea name="modified_translation" id="id_modified_translation" rows="5" cols="80">{{ review.ai_translation }}</textarea>
                    </div>
                </div>
                <div class="form-row">
                    <div class="field-box">
                        <label for="id_notes">Notes:</label>
                        <textarea name="notes" id="id_notes" rows="3" cols="80">{{ review.notes }}</textarea>
                    </div>
                </div>
            </fieldset>
            
            <div class="submit-row">
                <input type="submit" value="Save Modified Translation" class="default" name="_save">
                <a href="{% url 'admin:translation_app_aitranslationreview_changelist' %}" class="button cancel-link">Cancel</a>
            </div>
        </div>
    </form>
</div>
{% endblock %}
