"""
Simple script to add the 'Ipakita' translation to the database.
"""

import os
import sys
import django
import logging

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Import Django models
from translation_app.models import Language, ComprehensiveTranslation, TranslationVersion
from django.db import transaction

def add_ipakita_translation():
    """Add the 'Ipakita' translation to the database."""
    try:
        # Get language objects
        tgl = Language.objects.get(code='tgl')
        ted = Language.objects.get(code='ted')
        
        # Use transaction to prevent database locks
        with transaction.atomic():
            # Check if the translation already exists
            existing = ComprehensiveTranslation.objects.filter(
                base_word__iexact="Ipakita",
                source_language=tgl,
                target_language=ted
            ).first()
            
            if existing:
                logger.info(f"Found existing translation: {existing.base_word} → {existing.translation}")
                
                # Update if needed
                if existing.translation != "Fëgitonën":
                    logger.info(f"Updating translation from '{existing.translation}' to 'Fëgitonën'")
                    existing.translation = "Fëgitonën"
                    existing.save()
                    
                    # Add a new version
                    TranslationVersion.objects.create(
                        comprehensive_translation=existing,
                        translation="Fëgitonën",
                        created_by='manual',
                        confidence_score=1.0,
                        is_active=True,
                        notes='Manually corrected'
                    )
                    
                    logger.info("Translation updated successfully")
                else:
                    logger.info("Translation is already correct")
            else:
                logger.info("Creating new translation: Ipakita → Fëgitonën")
                
                # Create new translation
                new_translation = ComprehensiveTranslation.objects.create(
                    base_word="Ipakita",
                    translation="Fëgitonën",
                    source_language=tgl,
                    target_language=ted,
                    part_of_speech='verb',
                    notes='Added manually'
                )
                
                # Create a version
                TranslationVersion.objects.create(
                    comprehensive_translation=new_translation,
                    translation="Fëgitonën",
                    created_by='manual',
                    confidence_score=1.0,
                    is_active=True,
                    notes='Initial version'
                )
                
                logger.info("New translation created successfully")
        
        return True
    except Exception as e:
        logger.error(f"Error adding translation: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("Starting script to add Ipakita translation...")
    
    if add_ipakita_translation():
        logger.info("Translation added/updated successfully!")
    else:
        logger.error("Failed to add/update translation.")
