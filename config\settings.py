"""
Django settings for translation project.
"""

import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Define a function to check if we're running on PythonAnywhere
def is_running_on_pythonanywhere():
    """
    Detect if the app is running on PythonAnywhere.

    Returns:
        bool: True if running on PythonAnywhere, False otherwise
    """
    # Check for PythonAnywhere-specific environment variables or paths
    if 'PYTHONANYWHERE_DOMAIN' in os.environ:
        return True

    # Check for PythonAnywhere in the hostname
    try:
        import socket
        if 'pythonanywhere' in socket.gethostname().lower():
            return True
    except:
        pass

    # Check for PythonAnywhere in the sys.path
    import sys
    for path in sys.path:
        if 'pythonanywhere' in str(path).lower():
            return True

    return False

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-translation-project-secret-key-for-development'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = [
    'localhost',
    '127.0.0.1',
    'webapp-2551651.pythonanywhere.com',
    'teduraytranslator.online',
    'www.teduraytranslator.online',
]

# Application definition
INSTALLED_APPS = [
    'jazzmin',  # Jazzmin admin theme - must be before django.contrib.admin
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    # 'django_crontab',  # For scheduled tasks - commented out for testing
    'translation_app.apps.TranslationAppConfig',
]

# Jazzmin settings
JAZZMIN_SETTINGS = {
    # UI Theme settings
    "theme": "default",  # Use the default light theme
    "dark_mode_theme": None,  # Disable dark mode
    "dark_theme": None,  # Disable dark theme
    "brand_colour": "navbar-light",
    "accent": "accent-primary",
    "navbar": "navbar-white navbar-light",
    "no_navbar_border": False,
    "sidebar": "sidebar-light-primary",
    "sidebar_nav_child_indent": True,
    "sidebar_nav_compact_style": False,  # Set to False for better readability
    "sidebar_nav_legacy_style": False,
    "sidebar_nav_flat_style": False,
    # Fix sidebar behavior - make it static and non-collapsible
    "sidebar_fixed": True,  # Keep sidebar fixed when scrolling
    "sidebar_collapse": False,  # Don't collapse sidebar by default
    "sidebar_small": False,  # Don't use small sidebar
    "sidebar_disable_expand": True,  # Disable sidebar expand/collapse functionality
    "sidebar_disable_menu_hover": True,  # Disable menu hover expand feature
    # title of the window (Will default to current_admin_site.site_title if absent or None)
    "site_title": "Teduray-Tagalog Translator Admin",
    # Title on the login screen (19 chars max) (defaults to current_admin_site.site_header if absent or None)
    "site_header": "Teduray-Tagalog Translator",
    # Title on the brand (19 chars max) (defaults to current_admin_site.site_header if absent or None)
    "site_brand": "Translator Admin",
    # Logo to use for your site, must be present in static files, used for brand on top left
    # "site_logo": "books/img/logo.png",
    # Logo to use for your site, must be present in static files, used for login form logo (defaults to site_logo)
    # "login_logo": None,
    # Logo to use for login form in dark theme (defaults to login_logo)
    # "login_logo_dark": None,
    # CSS classes for the logo (defaults to 'img-circle')
    # "site_logo_classes": "img-circle",
    # Relative path to a favicon for your site, will default to site_logo if absent (ideally 32x32 px)
    # "site_icon": None,
    # Welcome text on the login screen
    "welcome_sign": "Welcome to the Teduray-Tagalog Translator",
    # Copyright on the footer
    "copyright": "Teduray-Tagalog Translator",
    # Hide the Jazzmin version in footer
    "show_ui_builder": False,
    "show_footer": True,  # Keep footer but we'll hide version with CSS
    # List of model admins to search from the search bar, search bar omitted if excluded
    # If you want to use a single search field you don't need to use a list, you can use a simple string
    "search_model": ["translation_app.ComprehensiveTranslation", "translation_app.Language"],
    # Field name on user model that contains avatar ImageField/URLField/Charfield or a callable that receives the user
    # "user_avatar": None,
    ############
    # Top Menu #
    ############
    # Links to put along the top menu
    "topmenu_links": [
        # Url that gets reversed (Permissions can be added)
        {"name": "Home", "url": "admin:index", "permissions": ["auth.view_user"]},
        # External url that opens in a new window (Permissions can be added)
        {"name": "Translator", "url": "/", "new_window": True},
        # Translation Management
        {"name": "Translation Management", "url": "/translation-management/", "permissions": ["auth.view_user"]},
    ],
    #############
    # User Menu #
    #############
    # Additional links to include in the user menu on the top right ("app" url type is not allowed)
    "usermenu_links": [
        {"name": "Translator Site", "url": "/", "new_window": True},
    ],
    # Logout settings
    "logout_redirect_url": "/",  # Where to redirect after logout
    #############
    # Side Menu #
    #############
    # Whether to display the side menu
    "show_sidebar": True,
    # Whether to aut expand the menu
    "navigation_expanded": True,
    # Hide these apps when generating side menu e.g (auth)
    "hide_apps": [],
    # Hide these models when generating side menu (e.g auth.user)
    "hide_models": [],
    # List of apps (and/or models) to base side menu ordering off of (does not need to contain all apps/models)
    "order_with_respect_to": ["translation_app"],
    # Custom links for side menu
    "custom_links": {
        "translation_app": [
            {
                "name": "Translation Management",
                "url": "/translation-management/",
                "icon": "fas fa-cogs",
                "permissions": ["auth.view_user"]
            },
        ],
    },

    # Custom icons for side menu apps/models
    "icons": {
        # App icons
        "auth": "fas fa-users-cog",
        "translation_app": "fas fa-language",

        # Model icons
        "auth.user": "fas fa-user",
        "auth.group": "fas fa-users",

        # Translation app models
        "translation_app.language": "fas fa-globe",
        "translation_app.word": "fas fa-font",
        "translation_app.translation": "fas fa-exchange-alt",
        "translation_app.comprehensivetranslation": "fas fa-book",
        "translation_app.relatedwordform": "fas fa-project-diagram",
        "translation_app.translationexample": "fas fa-quote-right",
        "translation_app.translationfeedback": "fas fa-comment-dots",
        "translation_app.translationversion": "fas fa-history",
        "translation_app.translationsuggestion": "fas fa-lightbulb",
        "translation_app.translationmetrics": "fas fa-chart-line",
        "translation_app.bleuscorehistory": "fas fa-chart-bar",
        "translation_app.attentiondata": "fas fa-brain",
        # AI-related models are hidden from the admin
        # "translation_app.aitranslationreview": "fas fa-check-double",
        # "translation_app.aitranslationsettings": "fas fa-cogs",
        # "translation_app.aitranslationlog": "fas fa-clipboard-list",
    },
    # Icons that are used when one is not manually specified
    "default_icon_parents": "fas fa-folder",
    "default_icon_children": "fas fa-file-alt",
    #################
    # Related Modal #
    #################
    # Use modals instead of popups
    "related_modal_active": True,
    #############
    # UI Tweaks #
    #############
    # Relative paths to custom CSS/JS scripts (must be present in static files)
    "custom_css": "css/admin_custom.css",
    "custom_js": None,
    # Login customization
    "login_logo": None,  # Use text instead of logo
    "login_logo_dark": None,
    "login_background": None,  # We'll handle this in our custom template
    # Whether to show the UI customizer on the sidebar
    "show_ui_builder": False,
    # Control the main content width
    "body_small_text": False,  # Use normal text size
    "navbar_small_text": False,  # Use normal text size in navbar
    "footer_small_text": False,  # Use normal text size in footer
    "form_small_text": False,  # Use normal text size in forms
    # Make sure content doesn't overlap with sidebar
    "layout_boxed": False,  # Don't use boxed layout
    "footer_fixed": False,  # Don't fix footer
    "grid_gutter_width": "3",  # Adjust gutter width for better spacing
    ###############
    # Change view #
    ###############
    # Render out the change view as a single form, or in tabs, current options are
    # - single
    # - horizontal_tabs (default)
    # - vertical_tabs
    # - collapsible
    # - carousel
    "changeform_format": "horizontal_tabs",
    # override change forms on a per modeladmin basis
    "changeform_format_overrides": {
        "auth.user": "collapsible",
        "auth.group": "vertical_tabs",
    },
    # Add a language dropdown into the admin
    "language_chooser": False,
}

# Admin site customization (kept for backwards compatibility)
ADMIN_SITE_HEADER = "Teduray-Tagalog Translator"
ADMIN_SITE_TITLE = "Teduray-Tagalog Translator Admin"
ADMIN_INDEX_TITLE = "Translator Administration"
ADMIN_SITE_URL = "/"  # URL to the main site

# Logout settings
LOGOUT_REDIRECT_URL = '/'  # Redirect to homepage after logout

MIDDLEWARE = [
    # Add our request middleware first to make request available in thread locals
    'translation_app.middleware.RequestMiddleware',
    # Add our database optimization middleware to handle database connections
    'translation_app.middleware.DatabaseOptimizationMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# Only add these middleware if we're not in ultra lazy loading mode
if not os.environ.get('USE_ULTRA_LAZY_LOADING', 'False').lower() == 'true':
    MIDDLEWARE += [
        'translation_app.middleware.MetricsUpdateMiddleware',
        'translation_app.middleware.RemoveSkipLinkMiddleware',
    ]

# Settings to disable automatic processes during admin loading
SKIP_ADMIN_DB_OPTIMIZATION = True
SKIP_ADMIN_METRICS_UPDATE = True
SKIP_ADMIN_TRANSLATION_LOADING = True

# Disable all automatic processes on startup
# This prevents the app from automatically loading translations and updating static files
# Set to True to disable all automatic processes, or False to enable them
DISABLE_AUTO_PROCESSES = True  # Disabled by default - translations will only load on demand or by scheduled task

# Enable lazy loading for translations
# This loads only essential translations at startup and loads the rest in the background
# Set to True to enable lazy loading, or False to load all translations at startup
USE_LAZY_LOADING = True

# Enable ultra lazy loading for translations (even more aggressive)
# This loads only the absolute minimum translations at startup
# Set to True to enable ultra lazy loading, or False to use regular lazy loading
USE_ULTRA_LAZY_LOADING = os.environ.get('USE_ULTRA_LAZY_LOADING', 'False').lower() == 'true'

# Enable PythonAnywhere optimization
# This applies specific optimizations for the PythonAnywhere environment
# Set to True to enable PythonAnywhere optimization, or False to use regular optimization
PYTHONANYWHERE_OPTIMIZATION = os.environ.get('PYTHONANYWHERE_OPTIMIZATION', 'False').lower() == 'true'

ROOT_URLCONF = 'config.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'config.wsgi.application'

# Database - Default to SQLite for local development
if not is_running_on_pythonanywhere():
    # Use SQLite for local development
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
            'OPTIONS': {
                'timeout': 60,  # Timeout in seconds
                'isolation_level': 'IMMEDIATE',  # More aggressive isolation level
                'check_same_thread': False,  # Allow access from multiple threads
            },
            'ATOMIC_REQUESTS': False,  # Disable atomic requests to reduce locking
            'CONN_MAX_AGE': 60,  # Keep connections open for 1 minute
            'CONN_HEALTH_CHECKS': True,  # Enable connection health checks
        }
    }
else:
    # Use MySQL for PythonAnywhere
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': 'Vergil20$teduray',  # PythonAnywhere MySQL database name
            'USER': 'Vergil20',          # PythonAnywhere username
            'PASSWORD': os.environ.get('MYSQL_PASSWORD', '@Verg@25!'),  # Get password from environment variable, default to actual password
            'HOST': 'Vergil20.mysql.pythonanywhere-services.com',  # PythonAnywhere MySQL host
            'PORT': '3306',              # MySQL default port
            'OPTIONS': {
                'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
                'charset': 'utf8mb4',    # Use utf8mb4 for full Unicode support
            },
            'ATOMIC_REQUESTS': False,    # Disable atomic requests to reduce locking
            'CONN_MAX_AGE': 300,         # Keep connections open for 5 minutes
            'CONN_HEALTH_CHECKS': True,  # Enable connection health checks
        }
    }

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'

# Configure static files based on environment
if is_running_on_pythonanywhere():
    # On PythonAnywhere, use the configuration from the web app settings
    # The static files URL is configured as /static/ -> /home/<USER>/teduray/staticfiles/
    STATIC_ROOT = '/home/<USER>/teduray/staticfiles'

    # Use a completely different directory for source files to avoid conflicts
    STATICFILES_DIRS = ['/home/<USER>/teduray/static']

    # Debug information
    import sys
    print(f"PythonAnywhere detected. Using configured paths:", file=sys.stderr)
    print(f"STATIC_ROOT: {STATIC_ROOT}", file=sys.stderr)
    print(f"STATICFILES_DIRS: {STATICFILES_DIRS}", file=sys.stderr)
else:
    # For local development
    STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
    STATICFILES_DIRS = [os.path.join(BASE_DIR, 'static')]

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'translation.log'),
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
        },
        'translation': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}

# Metrics Update Configuration
METRICS_UPDATE_INTERVAL = 3600  # Update metrics every hour (in seconds)

# Crontab settings for scheduled tasks
CRONJOBS = [
    # Run auto-training check every day at 2:00 AM
    ('0 2 * * *', 'translation_app.auto_training.check_and_trigger_training'),

    # Run scheduled training every Sunday at 3:00 AM
    ('0 3 * * 0', 'translation_app.auto_training.run_scheduled_training'),
]

# Apply PythonAnywhere-specific settings if running on PythonAnywhere
if is_running_on_pythonanywhere():
    try:
        # Log that we're using custom static files settings
        import logging
        logger = logging.getLogger(__name__)
        logger.info("Using custom static files settings for PythonAnywhere")

        # Set PythonAnywhere-specific settings directly
        # No need to configure database here as it's already set up above
        logger.info("Using MySQL database for PythonAnywhere")

        # Debug settings
        DEBUG = False  # Disable debug mode

        # Logging settings
        LOGGING['handlers']['file']['filename'] = '/home/<USER>/teduray/translation.log'

        # Media files
        MEDIA_ROOT = '/home/<USER>/teduray/media'

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"Error applying PythonAnywhere settings: {e}")
