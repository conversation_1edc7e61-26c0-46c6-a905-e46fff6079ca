"""
Database initialization module.
This module checks if the database is properly set up and initializes it if needed.
Works with both SQLite and MySQL databases.
"""

import os
import sys
import logging
import time
import threading
from django.db import connection, connections, OperationalError
from django.conf import settings

# Configure logging
logger = logging.getLogger(__name__)

# Define a function to check if we're running on PythonAnywhere
def is_running_on_pythonanywhere():
    """
    Detect if the app is running on PythonAnywhere.
    """
    # Check for PythonAnywhere-specific environment variables or paths
    if 'PYTHONANYWHERE_DOMAIN' in os.environ or 'PYTHONANYWHERE_SITE' in os.environ:
        return True

    # Check for PythonAnywhere in the hostname
    try:
        import socket
        if 'pythonanywhere' in socket.gethostname().lower():
            return True
    except:
        pass

    return False

def check_database_tables():
    """
    Check if the required database tables exist.
    If not, try to create them by running migrations.
    Works with both SQLite and MySQL databases.
    """
    logger.info("Checking database tables...")

    try:
        # Get the database engine being used
        db_engine = settings.DATABASES['default']['ENGINE']
        logger.info(f"Database engine: {db_engine}")

        # Check if the Language table exists
        with connection.cursor() as cursor:
            if 'sqlite3' in db_engine:
                # SQLite query
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='translation_app_language'")
            elif 'mysql' in db_engine:
                # MySQL query
                db_name = settings.DATABASES['default']['NAME']
                cursor.execute(f"SELECT table_name FROM information_schema.tables WHERE table_schema = '{db_name}' AND table_name = 'translation_app_language'")
            else:
                # Generic query that works with most databases
                try:
                    cursor.execute("SELECT 1 FROM translation_app_language LIMIT 1")
                    # If we get here, the table exists
                    logger.info("Language table exists (generic check).")
                    return True
                except Exception:
                    # Try an alternative approach
                    from django.db.models import Count
                    from django.apps import apps
                    try:
                        # Try to get the Language model and count records
                        Language = apps.get_model('translation_app', 'Language')
                        Language.objects.count()
                        logger.info("Language table exists (model check).")
                        return True
                    except Exception:
                        logger.warning("Language table does not exist (model check).")
                        return False

            # Check the result for SQLite or MySQL
            if cursor.fetchone():
                logger.info("Language table exists.")
                return True
            else:
                logger.warning("Language table does not exist. Database may not be initialized.")
                return False
    except Exception as e:
        logger.error(f"Error checking database tables: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def initialize_database():
    """
    Initialize the database by running migrations.
    """
    logger.info("Initializing database...")

    try:
        # Run migrations
        from django.core.management import call_command
        call_command('migrate', '--noinput')
        logger.info("Migrations applied successfully.")

        # Check if the Language table exists now
        if check_database_tables():
            logger.info("Database initialized successfully.")
            return True
        else:
            logger.error("Failed to initialize database.")
            return False
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        return False

def initialize_database_in_background():
    """
    Initialize the database in a background thread.
    """
    def _initialize():
        try:
            # Wait a moment to allow the server to start
            time.sleep(2)

            # Initialize the database
            initialize_database()
        except Exception as e:
            logger.error(f"Error in background database initialization: {str(e)}")

    # Start a background thread
    thread = threading.Thread(target=_initialize)
    thread.daemon = True
    thread.start()
    logger.info("Started background thread for database initialization.")

def optimize_database_connection():
    """
    Optimize database connection settings based on the database engine.
    Works with both SQLite and MySQL.
    """
    # Get the database engine being used
    db_engine = settings.DATABASES['default']['ENGINE']

    if 'sqlite3' in db_engine:
        return optimize_sqlite_connection()
    elif 'mysql' in db_engine:
        return optimize_mysql_connection()
    else:
        logger.info(f"No specific optimization available for {db_engine}.")
        return True

def optimize_sqlite_connection():
    """
    Optimize SQLite connection settings.
    """
    logger.info("Optimizing SQLite connection settings...")

    try:
        # Get the SQLite connection
        conn = connections['default'].connection

        # Set pragmas for better performance
        conn.execute('PRAGMA journal_mode = WAL')
        conn.execute('PRAGMA synchronous = NORMAL')
        conn.execute('PRAGMA cache_size = 10000')
        conn.execute('PRAGMA temp_store = MEMORY')
        conn.execute('PRAGMA mmap_size = 30000000000')
        conn.execute('PRAGMA busy_timeout = 60000')  # 60 seconds timeout

        logger.info("SQLite connection optimized.")
        return True
    except Exception as e:
        logger.error(f"Error optimizing SQLite connection: {str(e)}")
        return False

def optimize_mysql_connection():
    """
    Optimize MySQL connection settings.
    """
    logger.info("Optimizing MySQL connection settings...")

    try:
        # Get the MySQL connection
        conn = connections['default'].connection

        # Set session variables for better performance
        with conn.cursor() as cursor:
            # Increase the connection timeout
            cursor.execute("SET SESSION wait_timeout = 28800")  # 8 hours

            # Set the transaction isolation level
            cursor.execute("SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED")

            # Set the character set
            cursor.execute("SET NAMES utf8mb4")
            cursor.execute("SET CHARACTER SET utf8mb4")

            # Optimize for performance
            cursor.execute("SET SESSION sql_mode = 'STRICT_TRANS_TABLES'")

        logger.info("MySQL connection optimized.")
        return True
    except Exception as e:
        logger.error(f"Error optimizing MySQL connection: {str(e)}")
        return False

def ensure_database_ready():
    """
    Ensure the database is ready for use.
    This function should be called at application startup.
    Works with both SQLite and MySQL databases.
    """
    logger.info("Ensuring database is ready...")

    # Get the database engine being used
    db_engine = settings.DATABASES['default']['ENGINE']
    logger.info(f"Database engine: {db_engine}")

    if 'sqlite3' in db_engine:
        # SQLite-specific checks
        db_path = settings.DATABASES['default']['NAME']
        if not os.path.exists(db_path):
            logger.warning(f"Database file does not exist: {db_path}")

            # Create the database directory if it doesn't exist
            db_dir = os.path.dirname(db_path)
            if not os.path.exists(db_dir):
                os.makedirs(db_dir)
                logger.info(f"Created database directory: {db_dir}")

            # Initialize the database
            return initialize_database()
    elif 'mysql' in db_engine:
        # MySQL-specific checks
        try:
            # Try to connect to the database
            connection.ensure_connection()
            logger.info("Successfully connected to MySQL database.")
        except OperationalError as e:
            logger.error(f"Error connecting to MySQL database: {str(e)}")
            logger.warning("Database connection failed. Will attempt to initialize.")
            return initialize_database()

    # Check if the required tables exist
    if not check_database_tables():
        # Initialize the database
        return initialize_database()

    # Optimize database connection
    optimize_database_connection()

    logger.info("Database is ready.")
    return True
