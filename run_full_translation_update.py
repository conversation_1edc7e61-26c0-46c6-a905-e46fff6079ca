#!/usr/bin/env python
"""
Run Full Translation Update Script

This script runs both the process_translations.py and optimize_translation_system.py scripts
to perform a complete update of the translation system. It can be added as a scheduled task
in PythonAnywhere.

Usage:
    python run_full_translation_update.py
"""

import os
import sys
import subprocess
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('full_translation_update.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('run_full_translation_update')

def run_process_translations():
    """Run the process_translations.py script."""
    logger.info("Running process_translations.py")
    
    # Check if the script exists
    if not os.path.exists('process_translations.py'):
        logger.error("process_translations.py script not found")
        return False
    
    # Run the script
    try:
        result = subprocess.run(
            [sys.executable, 'process_translations.py'],
            capture_output=True,
            text=True
        )
        
        # Log the output
        if result.stdout:
            logger.info(f"Process output:\n{result.stdout}")
        
        # Log any errors
        if result.stderr:
            logger.error(f"Process errors:\n{result.stderr}")
        
        # Check return code
        if result.returncode == 0:
            logger.info("Translation processing completed successfully")
            return True
        else:
            logger.error(f"Translation processing failed with return code {result.returncode}")
            return False
    except Exception as e:
        logger.error(f"Error running translation processing: {str(e)}")
        return False

def run_optimize_translations():
    """Run the optimize_translation_system.py script with process-all flag."""
    logger.info("Running optimize_translation_system.py with process-all flag")
    
    # Check if the script exists
    if not os.path.exists('optimize_translation_system.py'):
        logger.error("optimize_translation_system.py script not found")
        return False
    
    # Run the script with process-all flag
    try:
        result = subprocess.run(
            [sys.executable, 'optimize_translation_system.py', '--process-all'],
            capture_output=True,
            text=True
        )
        
        # Log the output
        if result.stdout:
            logger.info(f"Process output:\n{result.stdout}")
        
        # Log any errors
        if result.stderr:
            logger.error(f"Process errors:\n{result.stderr}")
        
        # Check return code
        if result.returncode == 0:
            logger.info("Translation optimization completed successfully")
            return True
        else:
            logger.error(f"Translation optimization failed with return code {result.returncode}")
            return False
    except Exception as e:
        logger.error(f"Error running translation optimization: {str(e)}")
        return False

def main():
    """Run both scripts for a complete update of the translation system."""
    start_time = datetime.now()
    logger.info(f"Starting full translation update at {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # First, run the process_translations.py script
    processing_success = run_process_translations()
    
    # Then, run the optimize_translation_system.py script
    optimization_success = run_optimize_translations()
    
    # Log the overall result
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    if processing_success and optimization_success:
        logger.info(f"Full translation update completed successfully in {duration:.2f} seconds")
        return 0
    else:
        logger.error(f"Full translation update completed with errors in {duration:.2f} seconds")
        return 1

if __name__ == "__main__":
    sys.exit(main())
