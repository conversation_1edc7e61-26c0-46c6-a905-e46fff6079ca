# Copy and paste these commands directly into the Python console

# First, import the required modules
import requests
import json

# Define the server URL and endpoints
SERVER_URL = "http://localhost:8000"
API_ENDPOINT = "/api/translate/"
RELOAD_ENDPOINT = "/api/reload_translations/"

# Function to test translation
def test_translation(source_text, source_lang, target_lang, expected_translation=""):
    print(f"\nTesting translation of '{source_text}' from {source_lang} to {target_lang}")
    
    # Prepare the request
    headers = {'Content-Type': 'application/json'}
    data = {
        'text': source_text,
        'source_lang': source_lang,
        'target_lang': target_lang
    }
    
    # Make the API request
    response = requests.post(
        f"{SERVER_URL}{API_ENDPOINT}",
        headers=headers,
        data=json.dumps(data)
    )
    
    # Parse the response
    result = response.json()
    print(f"Response: {json.dumps(result, indent=2)}")
    
    # Extract the translation
    if result.get('success') and 'result' in result:
        translation = result['result'].get('translation', '')
        source = result['result'].get('source', '')
        
        print(f"Extracted translation: {translation}")
        print(f"Source: {source}")
        
        # Check if the translation matches the expected result
        if expected_translation and translation == expected_translation:
            print("SUCCESS: Translation matches expected result!")
        elif expected_translation:
            print("FAILURE: Translation does not match expected result.")
            print(f"Expected: {expected_translation}")
            print(f"Got: {translation}")
        else:
            print("No expected translation provided. Manual verification required.")
    else:
        print(f"API request failed: {json.dumps(result, indent=2)}")

# Function to test reload translations
def test_reload():
    print("\nTesting reload translations API...")
    
    # Prepare the request
    headers = {'Content-Type': 'application/json'}
    
    # Make the API request
    response = requests.post(
        f"{SERVER_URL}{RELOAD_ENDPOINT}",
        headers=headers
    )
    
    # Parse the response
    result = response.json()
    print(f"Response: {json.dumps(result, indent=2)}")
    
    # Check if the response indicates success
    if result.get('success'):
        print("SUCCESS: Translations reloaded successfully!")
    else:
        print("FAILURE: Failed to reload translations.")

# Now you can run these commands one by one:

# Test basic translations
test_translation("kumusta", "tgl", "ted")
test_translation("salamat", "tgl", "ted")

# Test the longer phrase that was having issues
test_translation("Pumunta ka sa bahay kahapon", "tgl", "ted", "Mënangëy go diyo natëmëgëno")

# Test the "baka" translation
test_translation("baka", "tgl", "ted", "beengk")

# Test reloading translations
test_reload()

# Test translations again after reload
test_translation("Pumunta ka sa bahay kahapon", "tgl", "ted", "Mënangëy go diyo natëmëgëno")
test_translation("baka", "tgl", "ted", "beengk")
