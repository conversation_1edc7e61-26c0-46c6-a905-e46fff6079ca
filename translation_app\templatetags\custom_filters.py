from django import template
from django.template.defaultfilters import floatformat

register = template.Library()

@register.filter
def absolute(value):
    """Returns the absolute value"""
    try:
        return abs(float(value))
    except (ValueError, TypeError):
        return value

@register.filter
def multiply(value, arg):
    """Multiplies the value by the argument"""
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return value

@register.filter
def sub(value, arg):
    """Subtracts the argument from the value"""
    try:
        return float(value) - float(arg)
    except (ValueError, TypeError):
        return value

@register.filter
def split(value, arg):
    """Splits a string by the argument"""
    try:
        return value.split(arg)
    except (AttributeErro<PERSON>, TypeError):
        return value