"""
<PERSON><PERSON><PERSON> to test the translation of 'Ipakita'.
This script tests how the translation service retrieves the translation for 'Ipakita'.
"""

import os
import sys
import django
import logging

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Import Django models and services
from translation_app.services import get_translation_service
from translation_app.models import Language, ComprehensiveTranslation, TranslationVersion
from django.core.cache import cache

def test_ipakita_translation():
    """Test the translation of 'Ipakita'."""
    try:
        # Get the translation service
        translation_service = get_translation_service()
        logger.info("Got translation service")

        # Get language objects
        tgl = Language.objects.get(code='tgl')
        ted = Language.objects.get(code='ted')
        logger.info(f"Got languages: {tgl.name} and {ted.name}")

        # Define the text to translate
        source_text = "Ipakita"

        # Check if the translation exists in the database
        existing_translation = ComprehensiveTranslation.objects.filter(
            base_word__iexact=source_text,
            source_language=tgl,
            target_language=ted
        ).first()

        if existing_translation:
            logger.info(f"Found in database: {source_text} → {existing_translation.translation}")

            # Check if there are any active versions
            active_version = TranslationVersion.objects.filter(
                comprehensive_translation=existing_translation,
                is_active=True
            ).first()

            if active_version:
                logger.info(f"Active version: {active_version.translation} (confidence: {active_version.confidence_score})")
            else:
                logger.warning("No active version found")
        else:
            logger.warning(f"Not found in database: {source_text}")

        # Test translation
        logger.info(f"Testing translation of '{source_text}'...")

        # Get translation
        result = translation_service.translate_text(source_text, tgl.code, ted.code)
        logger.info(f"Translation result: '{result['translation']}'")
        logger.info(f"Translation source: {result['source']}")
        logger.info(f"Translation confidence: {result['confidence']}")
        logger.info(f"Translation details: {result}")

        # Test direct lookup in the translations cache
        logger.info("Testing direct lookup in translations cache...")

        # Check if the service has translations loaded
        if hasattr(translation_service, 'translations'):
            # Check Tagalog to Teduray
            tgl_to_ted = translation_service.translations.get('tgl_to_ted', {})
            if source_text.lower() in tgl_to_ted:
                logger.info(f"Found in cache: {source_text} → {tgl_to_ted[source_text.lower()]}")
            else:
                logger.warning(f"Not found in cache: {source_text}")

            # List all similar entries
            similar_entries = {k: v for k, v in tgl_to_ted.items() if 'ipakita' in k.lower()}
            if similar_entries:
                logger.info(f"Found {len(similar_entries)} similar entries:")
                for k, v in similar_entries.items():
                    logger.info(f"  - {k} → {v['translation']}")
            else:
                logger.warning("No similar entries found in cache")
        else:
            logger.warning("Translation service does not have translations loaded")

        return True
    except Exception as e:
        logger.error(f"Error testing translation: {str(e)}")
        return False

def add_ipakita_translation():
    """Add the correct translation for 'Ipakita'."""
    try:
        # Get language objects
        tgl = Language.objects.get(code='tgl')
        ted = Language.objects.get(code='ted')

        # Check if the translation already exists
        existing_translation = ComprehensiveTranslation.objects.filter(
            base_word__iexact="Ipakita",
            source_language=tgl,
            target_language=ted
        ).first()

        if existing_translation:
            logger.info(f"Translation already exists: Ipakita → {existing_translation.translation}")

            # Update the translation if it's incorrect
            if existing_translation.translation != "Fëgitonën":
                logger.info(f"Updating translation from '{existing_translation.translation}' to 'Fëgitonën'")

                # Create a new version with the old translation (inactive)
                TranslationVersion.objects.create(
                    comprehensive_translation=existing_translation,
                    translation=existing_translation.translation,
                    created_by='system',
                    confidence_score=0.8,
                    is_active=False,
                    notes='Archived before correction'
                )

                # Update the translation
                existing_translation.translation = "Fëgitonën"
                existing_translation.save()

                # Create a new active version
                TranslationVersion.objects.create(
                    comprehensive_translation=existing_translation,
                    translation="Fëgitonën",
                    created_by='system',
                    confidence_score=1.0,
                    is_active=True,
                    notes='Corrected translation'
                )

                logger.info("Translation updated successfully")
            else:
                logger.info("Translation is already correct")
        else:
            logger.info("Creating new translation: Ipakita → Fëgitonën")

            # Create new translation
            new_translation = ComprehensiveTranslation.objects.create(
                base_word="Ipakita",
                translation="Fëgitonën",
                source_language=tgl,
                target_language=ted,
                part_of_speech='verb',
                notes='Added manually to fix translation issue'
            )

            # Create a version
            TranslationVersion.objects.create(
                comprehensive_translation=new_translation,
                translation="Fëgitonën",
                created_by='system',
                confidence_score=1.0,
                is_active=True,
                notes='Initial version'
            )

            logger.info("New translation created successfully")

        # Clear cache
        cache.clear()
        logger.info("Cache cleared")

        return True
    except Exception as e:
        logger.error(f"Error adding translation: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("Starting Ipakita translation test script...")

    # First test the current translation
    logger.info("Testing current translation...")
    test_ipakita_translation()

    # Add or update the translation
    logger.info("\nAdding/updating translation...")
    if add_ipakita_translation():
        logger.info("Translation added/updated successfully!")
    else:
        logger.error("Failed to add/update translation.")

    # Test the translation again
    logger.info("\nTesting translation after update...")
    if test_ipakita_translation():
        logger.info("Translation test completed successfully!")
    else:
        logger.error("Translation test failed.")

    # Test the translation directly
    translation_service = get_translation_service()
    result = translation_service.translate_text("Ipakita", "tgl", "ted")
    logger.info(f"Final translation result: '{result['translation']}' (source: {result['source']})")
