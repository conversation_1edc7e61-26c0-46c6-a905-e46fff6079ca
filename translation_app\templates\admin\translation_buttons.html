{% load i18n %}

<style>
    .translation-buttons {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 5px;
        border: 1px solid #dee2e6;
    }
    
    .translation-buttons h2 {
        margin-top: 0;
        color: #343a40;
        font-size: 1.5rem;
        margin-bottom: 15px;
    }
    
    .translation-buttons .btn-row {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 10px;
    }
    
    .translation-buttons .btn {
        padding: 8px 16px;
        border-radius: 4px;
        font-weight: 500;
        text-decoration: none;
        display: inline-block;
        color: white;
        cursor: pointer;
        border: none;
    }
    
    .translation-buttons .btn-success {
        background-color: #28a745;
    }
    
    .translation-buttons .btn-primary {
        background-color: #007bff;
    }
    
    .translation-buttons .btn-warning {
        background-color: #ffc107;
        color: #212529;
    }
    
    .translation-buttons .btn-info {
        background-color: #17a2b8;
    }
    
    .translation-buttons .btn-description {
        font-size: 0.9rem;
        color: #6c757d;
        margin-top: 5px;
    }
    
    .translation-buttons .btn-container {
        flex: 1 1 calc(50% - 10px);
        min-width: 250px;
    }
    
    @media (max-width: 768px) {
        .translation-buttons .btn-container {
            flex: 1 1 100%;
        }
    }
</style>

<div class="translation-buttons">
    <h2>{% trans "Translation System Management" %}</h2>
    
    <div class="btn-row">
        <div class="btn-container">
            <form method="post" action="{% url 'admin:update_attention' %}">
                {% csrf_token %}
                <button type="submit" class="btn btn-success">{% trans "Update Attention" %}</button>
                <div class="btn-description">{% trans "Enhance translations using the attention mechanism for better quality with longer sentences." %}</div>
            </form>
        </div>
        
        <div class="btn-container">
            <form method="post" action="{% url 'admin:update_static' %}">
                {% csrf_token %}
                <button type="submit" class="btn btn-primary">{% trans "Update Static Files" %}</button>
                <div class="btn-description">{% trans "Update static translation files used by the frontend for faster access." %}</div>
            </form>
        </div>
    </div>
    
    <div class="btn-row">
        <div class="btn-container">
            <form method="post" action="{% url 'admin:update_feedback' %}">
                {% csrf_token %}
                <button type="submit" class="btn btn-warning">{% trans "Process Feedback" %}</button>
                <div class="btn-description">{% trans "Process pending translation feedback and generate suggestions from user input." %}</div>
            </form>
        </div>
        
        <div class="btn-container">
            <form method="post" action="{% url 'admin:update_translations' %}">
                {% csrf_token %}
                <button type="submit" class="btn btn-info">{% trans "Update System" %}</button>
                <div class="btn-description">{% trans "Update the entire translation system with all processed translations." %}</div>
            </form>
        </div>
    </div>
</div>
