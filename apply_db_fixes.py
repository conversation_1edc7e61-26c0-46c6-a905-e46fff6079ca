"""
<PERSON><PERSON><PERSON> to apply database locking fixes to the translation project.
Works on both Windows and Unix systems.

This script applies comprehensive fixes to prevent SQLite database locking issues:
1. Creates a backup of the database
2. Updates the uWSGI configuration
3. Optimizes the SQLite database with PRAGMA statements
4. Creates a reload file to restart the application
5. Implements connection pooling and retry logic
"""

import os
import logging
import subprocess
import shutil
import platform
import time
import sqlite3

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

def run_command(command):
    """Run a shell command and return the output."""
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        return result.stdout
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {e}")
        logger.error(f"Error output: {e.stderr}")
        return None

def optimize_database(db_path):
    """Apply PRAGMA optimizations directly to the SQLite database."""
    if not os.path.exists(db_path):
        logger.warning(f"Database file not found: {db_path}")
        return False

    try:
        logger.info(f"Optimizing database: {db_path}")
        conn = sqlite3.connect(db_path, timeout=300)
        cursor = conn.cursor()

        # Apply optimizations
        cursor.execute("PRAGMA busy_timeout = 120000;")
        cursor.execute("PRAGMA journal_mode = DELETE;")
        cursor.execute("PRAGMA synchronous = NORMAL;")
        cursor.execute("PRAGMA temp_store = MEMORY;")
        cursor.execute("PRAGMA cache_size = 5000;")
        cursor.execute("PRAGMA page_size = 4096;")

        # Optimize and vacuum the database
        cursor.execute("PRAGMA optimize;")
        cursor.execute("VACUUM;")

        # Commit changes and close connection
        conn.commit()
        conn.close()

        logger.info("Database optimization completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error optimizing database: {str(e)}")
        return False

def apply_fixes():
    """Apply all the database locking fixes."""
    logger.info("Applying database locking fixes...")

    # 1. Create a backup of the database
    logger.info("Creating database backup...")
    try:
        if os.path.exists("db.sqlite3"):
            backup_name = f"db.sqlite3.bak.{int(time.time())}"
            shutil.copy2("db.sqlite3", backup_name)
            logger.info(f"Database backup created successfully: {backup_name}")
        else:
            logger.warning("Database file not found in current directory")
    except Exception as e:
        logger.error(f"Error creating database backup: {str(e)}")

    # 2. Update uwsgi configuration
    logger.info("Updating uWSGI configuration...")
    uwsgi_config = """[uwsgi]
# Use a single process to avoid SQLite locking issues
processes = 1
threads = 2

# Enable single interpreter mode
single-interpreter = true

# Disable request logging to reduce disk I/O
disable-logging = true

# Increase timeouts for database operations
harakiri = 300  # Increased from 120 to 300 seconds
socket-timeout = 120  # Increased from 60 to 120 seconds
http-timeout = 120  # Increased from 60 to 120 seconds

# Buffer sizes
buffer-size = 65536  # Increased from 32768 to 65536
post-buffering = 16384  # Increased from 8192 to 16384

# Memory optimizations
optimize = true
memory-report = true
max-requests = 1000  # Limit max requests per worker
reload-on-rss = 256  # Reload workers when they reach 256MB

# Prevent thundering herd
thunder-lock = true

# Reload on file changes
touch-reload = /home/<USER>/teduray/reload.txt

# Graceful handling of SIGTERM
die-on-term = true

# Disable write exception handling to avoid deadlocks
disable-write-exception = true

# Increase listen queue size
listen = 1024

# Enable master process
master = true

# Retry on connection errors
retry-on-error = 30

# Offload threads for async I/O
offload-threads = 2
"""
    try:
        with open("uwsgi.ini", "w") as f:
            f.write(uwsgi_config)
        # Also save to uwsgi_fix.ini for reference
        with open("uwsgi_fix.ini", "w") as f:
            f.write(uwsgi_config)
        logger.info("uWSGI configuration updated successfully")
    except Exception as e:
        logger.error(f"Error updating uWSGI configuration: {str(e)}")

    # 3. Optimize the database
    logger.info("Optimizing the database...")
    db_path = "db.sqlite3"
    if optimize_database(db_path):
        logger.info("Database optimization completed")
    else:
        logger.warning("Database optimization failed or skipped")

    # 4. Copy the db_utils.py file to the translation_app directory
    logger.info("Installing database utilities...")
    db_utils_content = """\"\"\"
Database utilities for the translation app.

This module provides utilities for managing database connections
and preventing locking issues with SQLite.
\"\"\"

import logging
import time
import random
import threading
from contextlib import contextmanager
from django.db import connection, OperationalError

# Configure logging
logger = logging.getLogger(__name__)

# Thread-local storage for connection state
_local = threading.local()

# Lock for synchronizing access to connection pool
_lock = threading.Lock()

# Maximum number of retries for database operations
MAX_RETRIES = 5

# Initial delay between retries in seconds
INITIAL_RETRY_DELAY = 0.5

# Maximum delay between retries in seconds
MAX_RETRY_DELAY = 30

# Whether to add jitter to retry delays
USE_JITTER = True

# Connection pool size
MAX_CONNECTIONS = 5

# Connection pool (not actually used with SQLite, but useful for future migration)
_connection_pool = []


def optimize_connection(conn=None):
    \"\"\"
    Apply optimizations to a database connection.

    Args:
        conn: Database connection to optimize (uses default if None)
    \"\"\"
    conn = conn or connection

    try:
        with conn.cursor() as cursor:
            # Set busy timeout to 120 seconds
            cursor.execute("PRAGMA busy_timeout = 120000;")

            # Set journal mode to DELETE (safer than WAL for PythonAnywhere)
            cursor.execute("PRAGMA journal_mode = DELETE;")

            # Set synchronous mode to NORMAL (safer than OFF)
            cursor.execute("PRAGMA synchronous = NORMAL;")

            # Set temp store to MEMORY
            cursor.execute("PRAGMA temp_store = MEMORY;")

            # Set cache size (in pages, 1 page = 4KB)
            cursor.execute("PRAGMA cache_size = 5000;")

            # Set locking mode to EXCLUSIVE to reduce lock contention
            cursor.execute("PRAGMA locking_mode = EXCLUSIVE;")

            # Set page size for better performance
            cursor.execute("PRAGMA page_size = 4096;")

            logger.debug("Database connection optimized successfully")
    except Exception as e:
        logger.error(f"Error optimizing database connection: {str(e)}")


@contextmanager
def db_retry_context(max_retries=MAX_RETRIES, retry_delay=INITIAL_RETRY_DELAY,
                    max_delay=MAX_RETRY_DELAY, jitter=USE_JITTER):
    \"\"\"
    Context manager for database operations with retry logic.

    Args:
        max_retries: Maximum number of retries
        retry_delay: Initial delay between retries in seconds
        max_delay: Maximum delay between retries in seconds
        jitter: Whether to add random jitter to delay times

    Yields:
        Database connection
    \"\"\"
    retries = 0

    while True:
        try:
            # Optimize the connection before use
            optimize_connection()

            # Yield the connection for use
            yield connection

            # If we get here, the operation succeeded
            break

        except OperationalError as e:
            error_str = str(e).lower()
            # Check for various locking-related errors
            is_lock_error = any(err in error_str for err in [
                "locking protocol", "database is locked", "busy",
                "timeout", "no such table", "disk i/o error"
            ])

            if is_lock_error and retries < max_retries:
                retries += 1
                # Calculate delay with exponential backoff
                delay = min(retry_delay * (2 ** (retries - 1)), max_delay)

                # Add jitter to prevent retry storms
                if jitter:
                    delay = delay * (0.5 + random.random())

                logger.warning(f"Database lock detected: '{error_str}', retrying ({retries}/{max_retries}) in {delay:.2f}s...")
                time.sleep(delay)
            else:
                logger.error(f"Database error after {retries} retries: {str(e)}")
                raise
        finally:
            # Always ensure the connection is properly closed
            try:
                connection.close()
            except Exception as e:
                logger.error(f"Error closing database connection: {str(e)}")


def with_db_retry(func, *args, **kwargs):
    \"\"\"
    Execute a database function with retry on locking errors.

    Args:
        func: Function to execute
        *args: Arguments to pass to the function
        **kwargs: Keyword arguments to pass to the function

    Returns:
        Result of the function
    \"\"\"
    max_retries = kwargs.pop('max_retries', MAX_RETRIES)
    retry_delay = kwargs.pop('retry_delay', INITIAL_RETRY_DELAY)
    max_delay = kwargs.pop('max_delay', MAX_RETRY_DELAY)
    jitter = kwargs.pop('jitter', USE_JITTER)

    with db_retry_context(max_retries, retry_delay, max_delay, jitter):
        return func(*args, **kwargs)


def execute_query(query, params=None, fetchall=True):
    \"\"\"
    Execute a SQL query with retry logic.

    Args:
        query: SQL query to execute
        params: Parameters for the query
        fetchall: Whether to fetch all results

    Returns:
        Query results
    \"\"\"
    def _execute():
        with connection.cursor() as cursor:
            cursor.execute(query, params or [])
            if fetchall:
                return cursor.fetchall()
            return cursor.fetchone()

    return with_db_retry(_execute)


def vacuum_database():
    \"\"\"
    Vacuum the database to optimize storage and performance.
    \"\"\"
    try:
        logger.info("Vacuuming database...")
        with connection.cursor() as cursor:
            # First run PRAGMA optimize
            cursor.execute("PRAGMA optimize;")
            # Then vacuum the database
            cursor.execute("VACUUM;")
        logger.info("Database vacuum completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error vacuuming database: {str(e)}")
        return False
"""
    try:
        db_utils_path = os.path.join("translation_app", "db_utils.py")
        os.makedirs(os.path.dirname(db_utils_path), exist_ok=True)
        with open(db_utils_path, "w") as f:
            f.write(db_utils_content)
        logger.info(f"Database utilities installed successfully: {db_utils_path}")
    except Exception as e:
        logger.error(f"Error installing database utilities: {str(e)}")

    # 5. Create a reload file
    logger.info("Creating reload file...")
    try:
        # Create an empty file named reload.txt
        with open("reload.txt", "w") as f:
            f.write(f"Reload triggered at {time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("Reload file created successfully")
    except Exception as e:
        logger.error(f"Error creating reload file: {str(e)}")

    logger.info("All fixes applied successfully!")
    logger.info("The application will restart when deployed with the new configuration.")
    logger.info("Monitor the error logs for any remaining locking issues.")

if __name__ == "__main__":
    try:
        apply_fixes()
    except Exception as e:
        logger.exception("Error applying fixes")
        print(f"Error: {str(e)}")
