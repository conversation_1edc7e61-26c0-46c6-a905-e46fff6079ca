#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to fix the translation system to ensure database translations are always prioritized.
This script:
1. Clears all caches
2. Forces a complete reload of translations from the database
3. Updates the in-memory dictionary to prioritize database translations
4. Disables static file usage for translations
"""

import os
import sys
import logging
import django
from django.utils import timezone
from django.core.cache import cache
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fix_translation_system.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('fix_translation_system')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Import Django models and services
from django.conf import settings
from translation_app.models import (
    Language,
    ComprehensiveTranslation,
    TranslationVersion,
    TranslationExample
)
from translation_app.services import get_translation_service

def is_pythonanywhere():
    """Check if running on PythonAnywhere"""
    return 'PYTHONANYWHERE_DOMAIN' in os.environ or os.path.exists('/home/<USER>')

def fix_translation_system():
    """
    Fix the translation system to ensure database translations are always prioritized.
    """
    logger.info("Starting to fix the translation system")
    
    try:
        # 1. Clear all caches
        logger.info("Clearing all caches")
        cache.clear()
        
        # 2. Disable static file usage by setting a flag in settings
        logger.info("Disabling static file usage")
        setattr(settings, 'USE_DATABASE_ONLY', True)
        
        # 3. Enable Hugging Face and attention mechanism
        logger.info("Enabling Hugging Face integration and attention mechanism")
        setattr(settings, 'HUGGINGFACE_ENABLED', True)
        setattr(settings, 'ATTENTION_MECHANISM_ENABLED', True)
        
        # 4. Get the translation service and force a reload
        logger.info("Getting translation service and forcing reload")
        translation_service = get_translation_service(skip_loading_for_admin=False)
        
        # 5. Clear the in-memory dictionary
        logger.info("Clearing in-memory dictionary")
        translation_service.word_translations = {
            'tgl_to_ted': {},
            'ted_to_tgl': {}
        }
        
        # 6. Load all translations from the database
        logger.info("Loading all translations from database")
        
        # Get language objects
        tagalog = Language.objects.get(code='tgl')
        teduray = Language.objects.get(code='ted')
        
        # Get all translations
        tgl_to_ted_translations = ComprehensiveTranslation.objects.filter(
            source_language=tagalog, 
            target_language=teduray
        ).prefetch_related('versions')
        
        ted_to_tgl_translations = ComprehensiveTranslation.objects.filter(
            source_language=teduray, 
            target_language=tagalog
        ).prefetch_related('versions')
        
        logger.info(f"Found {tgl_to_ted_translations.count()} Tagalog to Teduray translations")
        logger.info(f"Found {ted_to_tgl_translations.count()} Teduray to Tagalog translations")
        
        # Process Tagalog to Teduray translations
        for ct in tgl_to_ted_translations:
            # Get the active version if available
            active_version = None
            try:
                active_version = next((v for v in ct.versions.all() if v.is_active), None)
            except Exception:
                pass
            
            translation_text = active_version.translation if active_version else ct.translation
            confidence_score = active_version.confidence_score if active_version else 0.99
            
            # Add to in-memory dictionary with high priority
            if ' ' in ct.base_word:
                # It's a phrase
                phrase_key = f"phrase:{ct.base_word.lower()}"
                translation_service.word_translations['tgl_to_ted'][phrase_key] = {
                    'translation': translation_text,
                    'part_of_speech': ct.part_of_speech,
                    'notes': ct.notes,
                    'cultural_notes': ct.cultural_notes,
                    'examples': [],
                    'priority': True,
                    'confidence': confidence_score,
                    'source': 'database',
                    'version_id': active_version.id if active_version else None,
                    'do_not_modify': True
                }
            else:
                # It's a word
                translation_service.word_translations['tgl_to_ted'][ct.base_word.lower()] = {
                    'translation': translation_text,
                    'part_of_speech': ct.part_of_speech,
                    'notes': ct.notes,
                    'cultural_notes': ct.cultural_notes,
                    'examples': [],
                    'priority': True,
                    'confidence': confidence_score,
                    'source': 'database',
                    'version_id': active_version.id if active_version else None,
                    'do_not_modify': True
                }
        
        # Process Teduray to Tagalog translations
        for ct in ted_to_tgl_translations:
            # Get the active version if available
            active_version = None
            try:
                active_version = next((v for v in ct.versions.all() if v.is_active), None)
            except Exception:
                pass
            
            translation_text = active_version.translation if active_version else ct.translation
            confidence_score = active_version.confidence_score if active_version else 0.99
            
            # Add to in-memory dictionary with high priority
            if ' ' in ct.base_word:
                # It's a phrase
                phrase_key = f"phrase:{ct.base_word.lower()}"
                translation_service.word_translations['ted_to_tgl'][phrase_key] = {
                    'translation': translation_text,
                    'part_of_speech': ct.part_of_speech,
                    'notes': ct.notes,
                    'cultural_notes': ct.cultural_notes,
                    'examples': [],
                    'priority': True,
                    'confidence': confidence_score,
                    'source': 'database',
                    'version_id': active_version.id if active_version else None,
                    'do_not_modify': True
                }
            else:
                # It's a word
                translation_service.word_translations['ted_to_tgl'][ct.base_word.lower()] = {
                    'translation': translation_text,
                    'part_of_speech': ct.part_of_speech,
                    'notes': ct.notes,
                    'cultural_notes': ct.cultural_notes,
                    'examples': [],
                    'priority': True,
                    'confidence': confidence_score,
                    'source': 'database',
                    'version_id': active_version.id if active_version else None,
                    'do_not_modify': True
                }
        
        # 7. Update the cache with the new translations
        logger.info("Updating cache with new translations")
        cache.set(translation_service.cache_key, translation_service.word_translations, translation_service.cache_timeout)
        cache.set('translation_service_complete_load', True, translation_service.cache_timeout)
        
        # 8. Reset flags
        translation_service.is_loading = False
        translation_service.complete_load_done = True
        
        # 9. Test a few translations
        logger.info("Testing translations")
        test_translations = [
            ("Yumuko ka", "tgl", "ted"),
            ("kumusta", "tgl", "ted"),
            ("Tumakbo si Tom", "tgl", "ted")
        ]
        
        for text, source_lang, target_lang in test_translations:
            result = translation_service.translate_text(text, source_lang, target_lang)
            logger.info(f"Translation of '{text}': {result.get('translation', 'Unknown')}")
            logger.info(f"Source: {result.get('source', 'Unknown')}")
            logger.info(f"Confidence: {result.get('confidence', 0) * 100:.0f}%")
        
        return True
    except Exception as e:
        logger.error(f"Error fixing translation system: {str(e)}")
        return False

if __name__ == "__main__":
    success = fix_translation_system()
    if success:
        logger.info("Successfully fixed the translation system")
    else:
        logger.error("Failed to fix the translation system")
