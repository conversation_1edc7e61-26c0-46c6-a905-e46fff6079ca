"""
Fast lookup module for single word translations.
This is optimized for speed and used as a fallback when the main translation system is slow.
"""

import logging

logger = logging.getLogger(__name__)

# Known word pairs for fast lookup
KNOWN_WORD_PAIRS = {
    'tgl_to_ted': {
        'umiiyak': 'këm<PERSON><PERSON>',
        'hindi': 'ënda',
        'nanganak': 'mëgënga',
        'kababaihan': 'libun',
        'ilang': 'uwëni',
        'sila': 'ro',
        'kapag': 'amuk',
        'nagsisisi': 'gésénule',
        'tunay': 'toow',
        'inyong': 'tete',
        'kasalanan': 'brab',
        'patunayan': 'ténagak',
        'gawa': 'gom',
        'magsisimula': 'kuwede',
        'saan': 'hon'
    },
    'ted_to_tgl': {
        'këm<PERSON>rew': 'umiiyak',
        'ënda': 'hindi',
        'mëgënga': 'nanganak',
        'libun': 'kababa<PERSON>an',
        'uwëni': 'ilang',
        'ro': 'sila',
        'amuk': 'kapag',
        'g<PERSON><PERSON>ule': 'nagsisisi',
        'toow': 'tunay',
        'tete': 'inyong',
        'brab': 'kasalanan',
        'ténagak': 'patunayan',
        'gom': 'gawa',
        'kuwede': 'magsisimula',
        'hon': 'saan'
    }
}

def fast_lookup(text, source_lang, target_lang):
    """
    Fast lookup for words and sentences. This is optimized for speed.

    Args:
        text: The text to look up
        source_lang: Source language code
        target_lang: Target language code

    Returns:
        dict: Translation result or None if not found
    """
    # Determine the translation direction
    if source_lang == 'tgl' and target_lang == 'ted':
        direction = 'tgl_to_ted'
    elif source_lang == 'ted' and target_lang == 'tgl':
        direction = 'ted_to_tgl'
    else:
        return None

    # Normalize the text
    text_lower = text.lower().strip()

    # Check if this is a single word
    if ' ' not in text_lower:
        # Check if this is a known word
        if direction in KNOWN_WORD_PAIRS and text_lower in KNOWN_WORD_PAIRS[direction]:
            translation = KNOWN_WORD_PAIRS[direction][text_lower]
            logger.info(f"Fast lookup found known word: {text} → {translation}")
            return {
                'original': text,
                'translation': translation,
                'confidence': 0.99,
                'source': 'known_word',
                'word_by_word': [{
                    'original': text,
                    'translation': translation,
                    'confidence': 0.99,
                    'source': 'known_word'
                }]
            }

    # Check if this is a sentence with known words
    if ' ' in text_lower:
        # Try to find exact sentence match in database
        try:
            from django.apps import apps
            ComprehensiveTranslation = apps.get_model('translation_app', 'ComprehensiveTranslation')
            Language = apps.get_model('translation_app', 'Language')

            source_language = Language.objects.get(code=source_lang)
            target_language = Language.objects.get(code=target_lang)

            # Look for exact match
            exact_match = ComprehensiveTranslation.objects.filter(
                base_word=text,
                source_language=source_language,
                target_language=target_language
            ).first()

            if exact_match:
                logger.info(f"Fast lookup found exact sentence match: {text} → {exact_match.translation}")
                return {
                    'original': text,
                    'translation': exact_match.translation,
                    'confidence': 0.95,
                    'source': 'exact_match',
                    'word_by_word': [{
                        'original': text,
                        'translation': exact_match.translation,
                        'confidence': 0.95,
                        'source': 'exact_match'
                    }]
                }

            # Look for similar sentences
            words = text_lower.split()
            for word in words:
                if word in KNOWN_WORD_PAIRS.get(direction, {}):
                    # If we find a known word, look for sentences containing it
                    similar_sentences = ComprehensiveTranslation.objects.filter(
                        base_word__icontains=word,
                        source_language=source_language,
                        target_language=target_language
                    ).order_by('-updated_at')[:5]

                    if similar_sentences.exists():
                        best_match = similar_sentences.first()
                        logger.info(f"Fast lookup found similar sentence: {text} → {best_match.translation}")
                        return {
                            'original': text,
                            'translation': best_match.translation,
                            'confidence': 0.7,
                            'source': 'similar_sentence',
                            'word_by_word': [{
                                'original': text,
                                'translation': best_match.translation,
                                'confidence': 0.7,
                                'source': 'similar_sentence'
                            }]
                        }
        except Exception as e:
            logger.error(f"Error in fast lookup database check: {str(e)}")

    # Not found
    return None
