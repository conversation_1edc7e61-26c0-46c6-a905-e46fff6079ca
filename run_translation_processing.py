#!/usr/bin/env python
"""
Run Translation Processing Script

This script is a simple wrapper to run the process_translations.py script.
It can be added as a scheduled task in PythonAnywhere.

Usage:
    python run_translation_processing.py
"""

import os
import sys
import subprocess
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('translation_processing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('run_translation_processing')

def main():
    """Run the process_translations.py script."""
    start_time = datetime.now()
    logger.info(f"Starting translation processing at {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check if the process_translations.py script exists
    if not os.path.exists('process_translations.py'):
        logger.error("process_translations.py script not found")
        return 1
    
    # Run the script
    try:
        result = subprocess.run(
            [sys.executable, 'process_translations.py'],
            capture_output=True,
            text=True
        )
        
        # Log the output
        if result.stdout:
            logger.info(f"Process output:\n{result.stdout}")
        
        # Log any errors
        if result.stderr:
            logger.error(f"Process errors:\n{result.stderr}")
        
        # Check return code
        if result.returncode == 0:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.info(f"Translation processing completed successfully in {duration:.2f} seconds")
            return 0
        else:
            logger.error(f"Translation processing failed with return code {result.returncode}")
            return result.returncode
    except Exception as e:
        logger.error(f"Error running translation processing: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
