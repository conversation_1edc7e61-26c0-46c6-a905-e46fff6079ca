"""
<PERSON><PERSON><PERSON> to check and fix database translations.
This script examines the database for a specific translation and provides detailed information.
"""

import os
import sys
import django
import logging
from datetime import datetime

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Import Django models
from django.db import connection
from django.db.models import Q
from translation_app.models import (
    Language, 
    ComprehensiveTranslation, 
    TranslationVersion,
    TranslationFeedback,
    TranslationRating,
    TranslationSuggestion,
    Word
)

def check_database_connection():
    """Check if the database connection is working."""
    try:
        connection.ensure_connection()
        logger.info("Database connection is working.")
        return True
    except Exception as e:
        logger.error(f"Database connection error: {str(e)}")
        return False

def check_languages():
    """Check if the required languages exist in the database."""
    try:
        languages = Language.objects.all()
        logger.info(f"Found {languages.count()} languages in the database:")
        
        for lang in languages:
            logger.info(f"  - {lang.name} ({lang.code})")
        
        # Check for Tagalog and Teduray specifically
        tgl = Language.objects.filter(code='tgl').first()
        ted = Language.objects.filter(code='ted').first()
        
        if tgl:
            logger.info(f"Tagalog language found: {tgl.name} ({tgl.code})")
        else:
            logger.error("Tagalog language not found in the database!")
        
        if ted:
            logger.info(f"Teduray language found: {ted.name} ({ted.code})")
        else:
            logger.error("Teduray language not found in the database!")
        
        return tgl, ted
    except Exception as e:
        logger.error(f"Error checking languages: {str(e)}")
        return None, None

def check_specific_translation(source_text, tgl, ted):
    """Check for a specific translation in the database."""
    try:
        # Check for exact match
        exact_match = ComprehensiveTranslation.objects.filter(
            base_word__iexact=source_text,
            source_language=tgl,
            target_language=ted
        ).first()
        
        if exact_match:
            logger.info(f"Found exact match: {exact_match.base_word} → {exact_match.translation}")
            logger.info(f"  - ID: {exact_match.id}")
            logger.info(f"  - Created: {exact_match.created_at}")
            logger.info(f"  - Updated: {exact_match.updated_at}")
            logger.info(f"  - Part of speech: {exact_match.part_of_speech}")
            logger.info(f"  - Notes: {exact_match.notes}")
            
            # Check for versions
            versions = TranslationVersion.objects.filter(comprehensive_translation=exact_match)
            logger.info(f"  - Versions: {versions.count()}")
            
            for version in versions:
                logger.info(f"    - Version {version.id}: {version.translation}")
                logger.info(f"      - Created: {version.created_at}")
                logger.info(f"      - Active: {version.is_active}")
                logger.info(f"      - Confidence: {version.confidence_score}")
        else:
            logger.warning(f"No exact match found for '{source_text}'")
        
        # Check for match with period
        with_period = ComprehensiveTranslation.objects.filter(
            base_word__iexact=source_text + ".",
            source_language=tgl,
            target_language=ted
        ).first()
        
        if with_period:
            logger.info(f"Found match with period: {with_period.base_word} → {with_period.translation}")
            logger.info(f"  - ID: {with_period.id}")
            logger.info(f"  - Created: {with_period.created_at}")
            logger.info(f"  - Updated: {with_period.updated_at}")
            
            # Check for versions
            versions = TranslationVersion.objects.filter(comprehensive_translation=with_period)
            logger.info(f"  - Versions: {versions.count()}")
            
            for version in versions:
                logger.info(f"    - Version {version.id}: {version.translation}")
                logger.info(f"      - Created: {version.created_at}")
                logger.info(f"      - Active: {version.is_active}")
                logger.info(f"      - Confidence: {version.confidence_score}")
        else:
            logger.warning(f"No match with period found for '{source_text}.'")
        
        # Check for similar matches
        similar_matches = ComprehensiveTranslation.objects.filter(
            base_word__icontains=source_text,
            source_language=tgl,
            target_language=ted
        ).exclude(id__in=[m.id for m in [exact_match, with_period] if m])
        
        if similar_matches.exists():
            logger.info(f"Found {similar_matches.count()} similar matches:")
            
            for match in similar_matches:
                logger.info(f"  - {match.base_word} → {match.translation}")
                logger.info(f"    - ID: {match.id}")
                logger.info(f"    - Created: {match.created_at}")
                logger.info(f"    - Updated: {match.updated_at}")
        else:
            logger.info("No similar matches found.")
        
        # Check for feedback
        feedback = TranslationFeedback.objects.filter(
            Q(original_text__icontains=source_text) | Q(suggested_translation__icontains="Mënule go"),
            source_language=tgl,
            target_language=ted
        )
        
        if feedback.exists():
            logger.info(f"Found {feedback.count()} feedback entries:")
            
            for fb in feedback:
                logger.info(f"  - {fb.original_text} → {fb.suggested_translation}")
                logger.info(f"    - ID: {fb.id}")
                logger.info(f"    - Created: {fb.created_at}")
                logger.info(f"    - Processed: {fb.processed}")
                logger.info(f"    - Rating: {fb.rating}")
                logger.info(f"    - Comments: {fb.comments}")
        else:
            logger.info("No feedback found.")
        
        # Check for suggestions
        suggestions = TranslationSuggestion.objects.filter(
            Q(original_text__icontains=source_text) | Q(suggested_translation__icontains="Mënule go"),
            source_language=tgl,
            target_language=ted
        )
        
        if suggestions.exists():
            logger.info(f"Found {suggestions.count()} suggestions:")
            
            for suggestion in suggestions:
                logger.info(f"  - {suggestion.original_text} → {suggestion.suggested_translation}")
                logger.info(f"    - ID: {suggestion.id}")
                logger.info(f"    - Created: {suggestion.created_at}")
                logger.info(f"    - Status: {suggestion.status}")
        else:
            logger.info("No suggestions found.")
        
        return exact_match, with_period
    except Exception as e:
        logger.error(f"Error checking specific translation: {str(e)}")
        return None, None

def fix_translation(source_text, translation_text, tgl, ted, exact_match, with_period):
    """Fix the translation in the database."""
    try:
        now = datetime.now()
        
        if exact_match:
            # Update existing translation
            logger.info(f"Updating existing translation: {exact_match.base_word} → {translation_text}")
            
            # Create a new version
            version = TranslationVersion.objects.create(
                comprehensive_translation=exact_match,
                translation=translation_text,
                confidence_score=0.95,
                is_active=True
            )
            
            # Deactivate other versions
            TranslationVersion.objects.filter(
                comprehensive_translation=exact_match
            ).exclude(id=version.id).update(is_active=False)
            
            # Update the main translation
            exact_match.translation = translation_text
            exact_match.updated_at = now
            exact_match.save()
            
            logger.info(f"Updated translation: {exact_match.base_word} → {translation_text}")
            logger.info(f"Created version: {version.id}")
        else:
            # Create new translation
            new_translation = ComprehensiveTranslation.objects.create(
                base_word=source_text,
                translation=translation_text,
                source_language=tgl,
                target_language=ted,
                part_of_speech='',
                notes='Created via check_database_translation.py script'
            )
            
            logger.info(f"Created new translation: {source_text} → {translation_text}")
            exact_match = new_translation
        
        if with_period:
            # Update existing translation with period
            logger.info(f"Updating existing translation with period: {with_period.base_word} → {translation_text}")
            
            # Create a new version
            version = TranslationVersion.objects.create(
                comprehensive_translation=with_period,
                translation=translation_text,
                confidence_score=0.95,
                is_active=True
            )
            
            # Deactivate other versions
            TranslationVersion.objects.filter(
                comprehensive_translation=with_period
            ).exclude(id=version.id).update(is_active=False)
            
            # Update the main translation
            with_period.translation = translation_text
            with_period.updated_at = now
            with_period.save()
            
            logger.info(f"Updated translation with period: {with_period.base_word} → {translation_text}")
            logger.info(f"Created version: {version.id}")
        else:
            # Create new translation with period
            new_translation = ComprehensiveTranslation.objects.create(
                base_word=source_text + ".",
                translation=translation_text,
                source_language=tgl,
                target_language=ted,
                part_of_speech='',
                notes='Created via check_database_translation.py script (with period)'
            )
            
            logger.info(f"Created new translation with period: {source_text}. → {translation_text}")
            with_period = new_translation
        
        # Also update the Word table if it exists
        try:
            # Check if the word exists
            word = Word.objects.filter(
                text__iexact=source_text,
                language=tgl
            ).first()
            
            if word:
                logger.info(f"Found word in Word table: {word.text}")
                
                # Check if there's a translation
                translation_word = Word.objects.filter(
                    text__iexact=translation_text,
                    language=ted
                ).first()
                
                if not translation_word:
                    # Create the translation word
                    translation_word = Word.objects.create(
                        text=translation_text,
                        language=ted
                    )
                    logger.info(f"Created translation word: {translation_word.text}")
                
                # Update the word's translation
                word.translations.add(translation_word)
                word.save()
                logger.info(f"Updated word's translation: {word.text} → {translation_word.text}")
            else:
                logger.info(f"Word '{source_text}' not found in Word table")
        except Exception as e:
            logger.warning(f"Error updating Word table: {str(e)}")
        
        return True
    except Exception as e:
        logger.error(f"Error fixing translation: {str(e)}")
        return False

def reload_translations():
    """Reload translations into memory."""
    try:
        # Import the translation service
        from django.core.cache import cache
        from translation_app.services import get_translation_service
        
        # Clear the cache
        cache.clear()
        logger.info("Cache cleared")
        
        # Get the translation service and force a reload
        translation_service = get_translation_service(skip_loading_for_admin=False)
        translation_service.reload_translations()
        
        logger.info("Translations reloaded successfully")
        return True
    except Exception as e:
        logger.error(f"Error reloading translations: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("Starting database translation check script...")
    
    # Check database connection
    if not check_database_connection():
        sys.exit(1)
    
    # Check languages
    tgl, ted = check_languages()
    if not tgl or not ted:
        sys.exit(1)
    
    # Define the translation details
    source_text = "umuwi ka na"
    translation_text = "Mënule go"
    
    # Check for the specific translation
    exact_match, with_period = check_specific_translation(source_text, tgl, ted)
    
    # Ask if we should fix the translation
    fix_it = input("\nDo you want to fix the translation? (y/n): ").lower().strip() == 'y'
    
    if fix_it:
        # Fix the translation
        if fix_translation(source_text, translation_text, tgl, ted, exact_match, with_period):
            logger.info("Translation fixed successfully!")
            
            # Reload translations
            if reload_translations():
                logger.info("Translations reloaded successfully!")
            else:
                logger.error("Failed to reload translations.")
        else:
            logger.error("Failed to fix translation.")
    else:
        logger.info("No changes made to the translation.")
    
    logger.info("Database translation check completed.")
