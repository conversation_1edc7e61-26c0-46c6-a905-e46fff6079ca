"""
Utility functions for text processing in the translation app.
"""

import re
import string
import logging
import difflib
from typing import List, Tuple, Dict

logger = logging.getLogger(__name__)

def normalize_for_lookup(text):
    """
    Normalize text for database lookup by removing punctuation.

    This function removes punctuation marks while preserving spaces,
    allowing for better matching in the translation database.

    Args:
        text (str): The text to normalize

    Returns:
        str: Normalized text without punctuation
    """
    if not text:
        return text

    # Remove punctuation but preserve spaces
    normalized = re.sub(r'[^\w\s]', '', text)
    return normalized.strip().lower()


def apply_punctuation(translation, original_text):
    """
    Apply the punctuation from the original text to the translation.

    This function preserves question marks, exclamation marks, periods, etc.
    from the original text and applies them to the translation.

    Args:
        translation (str): The translation without punctuation
        original_text (str): The original text with punctuation

    Returns:
        str: Translation with punctuation applied
    """
    if not translation or not original_text:
        return translation

    # Extract ending punctuation from original text
    ending_punct = ''
    for char in reversed(original_text):
        if char in string.punctuation:
            ending_punct = char + ending_punct
        else:
            break

    # If no ending punctuation, return the translation as is
    if not ending_punct:
        return translation

    # Remove any existing ending punctuation from translation
    clean_translation = translation.rstrip(string.punctuation)

    # Apply the original punctuation
    return clean_translation + ending_punct


def preserve_capitalization(translation, original_text):
    """
    Preserve the capitalization pattern from the original text.

    Args:
        translation (str): The translation to modify
        original_text (str): The original text with capitalization

    Returns:
        str: Translation with capitalization preserved
    """
    if not translation or not original_text:
        return translation

    # If original starts with uppercase, capitalize the translation
    if original_text and original_text[0].isupper():
        return translation[0].upper() + translation[1:] if len(translation) > 1 else translation.upper()

    return translation


def align_words(source_text: str, target_text: str, existing_translations: Dict[str, str] = None) -> List[Tuple[str, str, float]]:
    """
    Align words between source and target text using a smarter algorithm.

    This function uses a combination of existing translations and statistical alignment
    to create more accurate word-by-word translations.

    Args:
        source_text (str): The source text
        target_text (str): The target text
        existing_translations (Dict[str, str]): Dictionary of known word translations

    Returns:
        List[Tuple[str, str, float]]: List of (source_word, target_word, confidence) tuples
    """
    if not source_text or not target_text:
        return []

    # Initialize with empty dictionary if none provided
    if existing_translations is None:
        existing_translations = {}

    # Clean up the texts - remove punctuation for better tokenization
    clean_source = re.sub(r'[^\w\s]', ' ', source_text)
    clean_target = re.sub(r'[^\w\s]', ' ', target_text)

    # Tokenize the texts - split by whitespace for more accurate word boundaries
    source_words = [w for w in clean_source.lower().split() if w]
    target_words = [w for w in clean_target.lower().split() if w]

    # If either text has no words, return empty list
    if not source_words or not target_words:
        return []

    # Get original words with proper capitalization
    original_source_words = [w for w in clean_source.split() if w]

    # Initialize result list
    alignments = []

    # First pass: use existing translations
    used_target_indices = set()
    for i, source_word in enumerate(source_words):
        if source_word in existing_translations:
            # Look for the known translation in target words
            known_translation = existing_translations[source_word].lower()
            for j, target_word in enumerate(target_words):
                if j not in used_target_indices and target_word == known_translation:
                    # Use original capitalization
                    alignments.append((original_source_words[i], target_word, 0.95))
                    used_target_indices.add(j)
                    break

    # Second pass: for unaligned words, use position-based alignment with length ratio check
    remaining_source_indices = [i for i in range(len(source_words)) if not any(a[0].lower() == source_words[i] for a in alignments)]
    remaining_target_indices = [i for i in range(len(target_words)) if i not in used_target_indices]

    # If we have a similar number of remaining words, align them by position
    if 0.5 <= len(remaining_source_indices) / max(1, len(remaining_target_indices)) <= 2.0:
        # Sort by position to maintain word order
        remaining_source_indices.sort()
        remaining_target_indices.sort()

        # Align remaining words by position with length-based confidence
        for i, src_idx in enumerate(remaining_source_indices):
            if i < len(remaining_target_indices):
                tgt_idx = remaining_target_indices[i]
                source_word = original_source_words[src_idx]  # Use original capitalization
                target_word = target_words[tgt_idx]

                # Calculate confidence based on relative length
                src_len = len(source_word)
                tgt_len = len(target_word)
                length_ratio = min(src_len, tgt_len) / max(src_len, tgt_len)
                confidence = 0.5 + (length_ratio * 0.3)  # Between 0.5 and 0.8

                alignments.append((source_word, target_word, confidence))

    # Sort alignments by source word position
    source_word_positions = {word.lower(): i for i, word in enumerate(original_source_words)}
    alignments.sort(key=lambda x: source_word_positions.get(x[0].lower(), 999))

    return alignments
