"""
Enhanced learning system for the translation platform.
This module provides a more robust learning mechanism that improves over time.
"""

import logging
import re
import json
import os
import time
import queue
import threading
from django.utils import timezone
from django.db.models import Avg
from django.db import transaction, connection, OperationalError
from .utils.platform_detection import PLATFORM_SETTINGS
from .models import (
    Language,
    ComprehensiveTranslation,
    TranslationExample,
    TranslationVersion,
    TranslationRating
)

logger = logging.getLogger(__name__)

class EnhancedLearner:
    """
    Enhanced learning system that improves translations over time based on user feedback,
    ratings, and patterns in the data.
    """

    def __init__(self):
        """Initialize the enhanced learner"""
        self.confidence_threshold = 0.7  # Minimum confidence to consider a translation reliable
        self.min_ratings = 3  # Minimum number of ratings to consider a translation reliable
        self.min_rating_score = 4.0  # Minimum average rating to consider a translation reliable
        self.static_file_path = os.path.join('static', 'translation_app', 'js', 'all_translations.js')

        # Create a queue for database operations to prevent locking
        self.db_queue = queue.Queue()
        self.db_lock = threading.Lock()
        self.is_processing = False

        # Get platform-specific settings
        self.use_threading = PLATFORM_SETTINGS['use_threading']
        self.max_static_file_size = PLATFORM_SETTINGS['max_static_file_size']
        self.db_retry_count = PLATFORM_SETTINGS['db_retry_count']
        self.db_retry_delay = PLATFORM_SETTINGS['db_retry_delay']

        # Start the database worker thread if threading is enabled
        if self.use_threading:
            self.worker_thread = threading.Thread(target=self._db_worker, daemon=True)
            self.worker_thread.start()
            logger.info("Started database worker thread for enhanced learner")
        else:
            logger.info("Threading disabled for this platform, using synchronous operations")

    def learn_from_feedback(self, source_text, original_translation, corrected_translation,
                           source_lang, target_lang):
        # Note: original_translation is kept for API compatibility but not used in this implementation
        """
        Learn from user feedback when a translation is corrected.

        Args:
            source_text: The original text
            original_translation: The system's translation
            corrected_translation: The user's corrected translation
            source_lang: Source language code
            target_lang: Target language code

        Returns:
            dict: Results of the learning process
        """
        logger.info(f"Learning from feedback: {source_text} → {corrected_translation}")

        # Define the actual database operation
        def _do_learn_from_feedback():
            # Get language objects
            source_language = Language.objects.get(code=source_lang)
            target_language = Language.objects.get(code=target_lang)

            # Find or create the translation
            translation, created = ComprehensiveTranslation.objects.get_or_create(
                base_word=source_text,
                source_language=source_language,
                target_language=target_language,
                defaults={
                    'translation': corrected_translation,
                    'part_of_speech': 'phrase' if ' ' in source_text else 'word',
                    'notes': 'Learned from user feedback'
                }
            )

            if not created:
                # Create a new version with the corrected translation
                TranslationVersion.objects.create(
                    comprehensive_translation=translation,
                    translation=corrected_translation,
                    created_by='user_feedback',
                    confidence_score=0.9,  # High confidence for user corrections
                    is_active=True,
                    notes='Corrected by user'
                )

                # Update the main translation
                translation.translation = corrected_translation
                translation.updated_at = timezone.now()
                translation.save()

            # Add an example
            TranslationExample.objects.get_or_create(
                comprehensive_translation=translation,
                source_text=source_text,
                target_text=corrected_translation
            )

            # Extract word pairs if this is a sentence
            word_pairs = []
            if ' ' in source_text and ' ' in corrected_translation:
                word_pairs = self._extract_word_pairs(
                    source_text, corrected_translation, source_lang, target_lang
                )

            return {
                'success': True,
                'message': f"Learned translation: {source_text} → {corrected_translation}",
                'word_pairs': word_pairs,
                'translation_id': translation.id
            }

        # Define the callback function
        def _on_learn_complete(result):
            if result and result.get('success'):
                # Check if automatic processes are disabled
                from django.conf import settings
                auto_processes_disabled = getattr(settings, 'DISABLE_AUTO_PROCESSES', False)

                # Update the static file (this method already checks DISABLE_AUTO_PROCESSES)
                self._update_static_file()

                if auto_processes_disabled:
                    logger.info(f"Static file update deferred to scheduled task after learning from feedback")
                else:
                    logger.info(f"Static file update triggered after learning from feedback")

        try:
            # Queue the database operation
            self._queue_db_operation(_do_learn_from_feedback, callback=_on_learn_complete)

            # Return an immediate response
            return {
                'success': True,
                'message': f"Learning from feedback: {source_text} → {corrected_translation} (queued)",
                'word_pairs': []
            }

        except Exception as e:
            logger.error(f"Error queueing learn from feedback: {str(e)}")
            return {
                'success': False,
                'message': f"Error learning from feedback: {str(e)}"
            }

    def learn_from_rating(self, translation_id, rating):
        """
        Learn from user ratings of translations.

        Args:
            translation_id: ID of the translation being rated
            rating: Rating value (1-5)

        Returns:
            dict: Results of the learning process
        """
        logger.info(f"Learning from rating: Translation ID {translation_id}, Rating {rating}")

        # Define the actual database operation
        def _do_learn_from_rating():
            # Get the translation
            translation = ComprehensiveTranslation.objects.get(id=translation_id)

            # If this is a high rating, increase the confidence of this translation
            if rating >= 4:
                # Get the active version
                active_version = TranslationVersion.objects.filter(
                    comprehensive_translation=translation,
                    is_active=True
                ).first()

                if active_version:
                    # Increase confidence (max 0.95)
                    new_confidence = min(active_version.confidence_score + 0.05, 0.95)
                    active_version.confidence_score = new_confidence
                    active_version.save()

                    logger.info(f"Increased confidence for translation: {translation.base_word} → {translation.translation} to {new_confidence}")

            # Calculate the average rating
            avg_rating = TranslationRating.objects.filter(
                comprehensive_translation=translation
            ).aggregate(avg=Avg('rating'))['avg']

            # Count the number of ratings
            rating_count = TranslationRating.objects.filter(
                comprehensive_translation=translation
            ).count()

            # If we have enough ratings and the average is high, mark this as a reliable translation
            is_reliable = rating_count >= self.min_ratings and avg_rating >= self.min_rating_score

            return {
                'success': True,
                'message': f"Processed rating {rating} for translation (avg: {avg_rating:.2f}, count: {rating_count})",
                'reliable': is_reliable,
                'translation': translation,
                'avg_rating': avg_rating,
                'rating_count': rating_count
            }

        # Define the callback function
        def _on_rating_complete(result):
            if result and result.get('success'):
                # Check if automatic processes are disabled
                from django.conf import settings
                auto_processes_disabled = getattr(settings, 'DISABLE_AUTO_PROCESSES', False)

                # If the translation is reliable, update its priority
                if result.get('reliable'):
                    translation = result.get('translation')
                    if translation:
                        avg_rating = result.get('avg_rating', 0)
                        rating_count = result.get('rating_count', 0)

                        if auto_processes_disabled:
                            logger.info(f"Marked translation as reliable: {translation.base_word} → {translation.translation} "
                                      f"with average rating {avg_rating:.2f} from {rating_count} ratings. "
                                      f"Priority update deferred to scheduled task.")
                        else:
                            # Update the static file to prioritize this translation
                            self._update_translation_priority(translation, True)

                            logger.info(f"Marked translation as reliable: {translation.base_word} → {translation.translation} "
                                      f"with average rating {avg_rating:.2f} from {rating_count} ratings")

                # Update the static file (this method already checks DISABLE_AUTO_PROCESSES)
                self._update_static_file()

        try:
            # Queue the database operation
            self._queue_db_operation(_do_learn_from_rating, callback=_on_rating_complete)

            # Return an immediate response
            return {
                'success': True,
                'message': f"Learning from rating: Translation ID {translation_id}, Rating {rating} (queued)",
                'reliable': False
            }

        except Exception as e:
            logger.error(f"Error queueing learn from rating: {str(e)}")
            return {
                'success': False,
                'message': f"Error learning from rating: {str(e)}"
            }

    def _extract_word_pairs(self, source_text, target_text, source_lang, target_lang):
        """
        Extract word pairs from a sentence pair and add them to the database.

        Args:
            source_text: Source text
            target_text: Target text
            source_lang: Source language code
            target_lang: Target language code

        Returns:
            list: Extracted word pairs
        """
        # Clean up punctuation and normalize
        source_clean = re.sub(r'[^\w\s]', '', source_text.lower())
        target_clean = re.sub(r'[^\w\s]', '', target_text.lower())

        # Split into words
        source_words = source_clean.split()
        target_words = target_clean.split()

        # If the number of words is very different, we can't reliably extract word pairs
        if len(source_words) == 0 or len(target_words) == 0:
            return []

        extracted_pairs = []

        # Known word pairs for specific languages
        known_word_pairs = {
            'tgl_to_ted': {
                'umiiyak': 'këmërew',
                'hindi': 'ënda',
                'nanganak': 'mëgënga',
                'kababaihan': 'libun',
                'ilang': 'uwëni',
                'sila': 'ro',
                'kapag': 'amuk',
                'nagsisisi': 'gésénule',
                'tunay': 'toow',
                'inyong': 'tete',
                'kasalanan': 'brab',
                'patunayan': 'ténagak',
                'gawa': 'gom',
                'magsisimula': 'kuwede',
                'saan': 'hon'
            }
        }

        # Direction key
        direction = f"{source_lang}_to_{target_lang}"

        # First check for known words
        for source_word in source_words:
            clean_word = re.sub(r'[^\w\s]', '', source_word.lower())

            if direction in known_word_pairs and clean_word in known_word_pairs[direction]:
                target_word = known_word_pairs[direction][clean_word]

                # Queue adding to database
                self._queue_db_operation(
                    self._add_word_to_database_direct,
                    clean_word, target_word, source_lang, target_lang
                )

                extracted_pairs.append((clean_word, target_word))
                logger.info(f"Queued known word pair for database: {clean_word} → {target_word}")

        # If we have the same number of words, try direct mapping
        if len(source_words) == len(target_words) and not extracted_pairs:
            for i in range(len(source_words)):
                # Skip very short words
                if len(source_words[i]) < 3 or len(target_words[i]) < 3:
                    continue

                # Queue adding to database
                self._queue_db_operation(
                    self._add_word_to_database_direct,
                    source_words[i], target_words[i], source_lang, target_lang
                )

                extracted_pairs.append((source_words[i], target_words[i]))

        # If we still don't have any pairs, use a simple position-based approach
        if not extracted_pairs and len(source_words) > 0 and len(target_words) > 0:
            # Calculate the ratio between source and target lengths
            ratio = len(target_words) / len(source_words)

            for i, source_word in enumerate(source_words):
                # Skip very short words
                if len(source_word) < 3:
                    continue

                # Calculate the approximate position in the target text
                target_idx = min(int(i * ratio), len(target_words) - 1)
                target_word = target_words[target_idx]

                # Skip very short target words
                if len(target_word) < 3:
                    continue

                # Queue adding to database
                self._queue_db_operation(
                    self._add_word_to_database_direct,
                    source_word, target_word, source_lang, target_lang
                )

                extracted_pairs.append((source_word, target_word))

        # Also add the full sentence as an example
        self._queue_db_operation(
            self._add_sentence_to_database_direct,
            source_text, target_text, source_lang, target_lang
        )

        logger.info(f"Extracted {len(extracted_pairs)} word pairs from sentence")
        return extracted_pairs

    def _add_word_to_database_direct(self, source_word, target_word, source_lang, target_lang):
        """Add a word pair directly to the database (for use with queue)"""
        try:
            # Get language objects
            source_language = Language.objects.get(code=source_lang)
            target_language = Language.objects.get(code=target_lang)

            # Create or update the translation
            translation, created = ComprehensiveTranslation.objects.get_or_create(
                base_word=source_word,
                source_language=source_language,
                target_language=target_language,
                defaults={
                    'translation': target_word,
                    'part_of_speech': 'word',
                    'notes': 'Learned from sentence'
                }
            )

            if not created:
                # Update the translation if it's different
                if translation.translation != target_word:
                    # Create a new version
                    TranslationVersion.objects.create(
                        comprehensive_translation=translation,
                        translation=translation.translation,  # Save the old translation
                        created_by='system',
                        confidence_score=0.8,
                        is_active=False,
                        notes='Archived before update from sentence learning'
                    )

                    # Create a new version with the new translation
                    TranslationVersion.objects.create(
                        comprehensive_translation=translation,
                        translation=target_word,
                        created_by='system',
                        confidence_score=0.8,
                        is_active=True,
                        notes='Updated from sentence learning'
                    )

                    # Update the translation
                    translation.translation = target_word
                    translation.updated_at = timezone.now()
                    translation.save()

                    logger.info(f"Updated translation: {source_word} → {target_word}")
            else:
                # Create initial version
                TranslationVersion.objects.create(
                    comprehensive_translation=translation,
                    translation=target_word,
                    created_by='system',
                    confidence_score=0.8,
                    is_active=True,
                    notes='Initial version from sentence learning'
                )

                logger.info(f"Created new translation: {source_word} → {target_word}")

            return True

        except Exception as e:
            logger.error(f"Error adding word pair to database: {str(e)}")
            return False

    def _add_sentence_to_database_direct(self, source_text, target_text, source_lang, target_lang):
        """Add a sentence pair directly to the database (for use with queue)"""
        try:
            # Get language objects
            source_language = Language.objects.get(code=source_lang)
            target_language = Language.objects.get(code=target_lang)

            # Create or get the sentence translation
            sentence_trans, created = ComprehensiveTranslation.objects.get_or_create(
                base_word=source_text,
                source_language=source_language,
                target_language=target_language,
                defaults={
                    'translation': target_text,
                    'part_of_speech': 'sentence',
                    'notes': 'Added as sentence example'
                }
            )

            if not created and sentence_trans.translation != target_text:
                # Create a new version with the old translation
                TranslationVersion.objects.create(
                    comprehensive_translation=sentence_trans,
                    translation=sentence_trans.translation,
                    created_by='system',
                    confidence_score=0.8,
                    is_active=False,
                    notes='Archived before update from sentence learning'
                )

                # Create a new version with the new translation
                TranslationVersion.objects.create(
                    comprehensive_translation=sentence_trans,
                    translation=target_text,
                    created_by='system',
                    confidence_score=0.8,
                    is_active=True,
                    notes='Updated from sentence learning'
                )

                # Update the translation
                sentence_trans.translation = target_text
                sentence_trans.updated_at = timezone.now()
                sentence_trans.save()

            # Add as an example
            _, created = TranslationExample.objects.get_or_create(
                comprehensive_translation=sentence_trans,
                source_text=source_text,
                target_text=target_text
            )

            if created:
                logger.info(f"Added sentence example: {source_text} → {target_text}")

            return True

        except Exception as e:
            logger.error(f"Error adding sentence example: {str(e)}")
            return False

    def _add_word_to_database(self, source_word, target_word, source_language, target_language):
        """Add a word pair to the database"""
        try:
            # Create or update the translation
            translation, created = ComprehensiveTranslation.objects.get_or_create(
                base_word=source_word,
                source_language=source_language,
                target_language=target_language,
                defaults={
                    'translation': target_word,
                    'part_of_speech': 'word',
                    'notes': 'Learned from sentence'
                }
            )

            if not created:
                # Update the translation if it's different
                if translation.translation != target_word:
                    # Create a new version
                    TranslationVersion.objects.create(
                        comprehensive_translation=translation,
                        translation=translation.translation,  # Save the old translation
                        created_by='system',
                        confidence_score=0.8,
                        is_active=False,
                        notes='Archived before update from sentence learning'
                    )

                    # Create a new version with the new translation
                    TranslationVersion.objects.create(
                        comprehensive_translation=translation,
                        translation=target_word,
                        created_by='system',
                        confidence_score=0.8,
                        is_active=True,
                        notes='Updated from sentence learning'
                    )

                    # Update the translation
                    translation.translation = target_word
                    translation.updated_at = timezone.now()
                    translation.save()

                    logger.info(f"Updated translation: {source_word} → {target_word}")
            else:
                # Create initial version
                TranslationVersion.objects.create(
                    comprehensive_translation=translation,
                    translation=target_word,
                    created_by='system',
                    confidence_score=0.8,
                    is_active=True,
                    notes='Initial version from sentence learning'
                )

                logger.info(f"Created new translation: {source_word} → {target_word}")

        except Exception as e:
            logger.error(f"Error adding word pair to database: {str(e)}")

    def _add_sentence_to_database(self, source_text, target_text, source_language, target_language):
        """Add a sentence pair to the database"""
        try:
            # Create or get the sentence translation
            sentence_trans, created = ComprehensiveTranslation.objects.get_or_create(
                base_word=source_text,
                source_language=source_language,
                target_language=target_language,
                defaults={
                    'translation': target_text,
                    'part_of_speech': 'sentence',
                    'notes': 'Added as sentence example'
                }
            )

            if not created and sentence_trans.translation != target_text:
                # Create a new version with the old translation
                TranslationVersion.objects.create(
                    comprehensive_translation=sentence_trans,
                    translation=sentence_trans.translation,
                    created_by='system',
                    confidence_score=0.8,
                    is_active=False,
                    notes='Archived before update from sentence learning'
                )

                # Create a new version with the new translation
                TranslationVersion.objects.create(
                    comprehensive_translation=sentence_trans,
                    translation=target_text,
                    created_by='system',
                    confidence_score=0.8,
                    is_active=True,
                    notes='Updated from sentence learning'
                )

                # Update the translation
                sentence_trans.translation = target_text
                sentence_trans.updated_at = timezone.now()
                sentence_trans.save()

            # Add as an example
            _, created = TranslationExample.objects.get_or_create(
                comprehensive_translation=sentence_trans,
                source_text=source_text,
                target_text=target_text
            )

            if created:
                logger.info(f"Added sentence example: {source_text} → {target_text}")

        except Exception as e:
            logger.error(f"Error adding sentence example: {str(e)}")

    def _update_translation_priority(self, translation, is_priority):
        """
        Update the priority of a translation in the static file.

        Args:
            translation: The translation object
            is_priority: Whether this is a priority translation

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if automatic processes are disabled
            from django.conf import settings
            auto_processes_disabled = getattr(settings, 'DISABLE_AUTO_PROCESSES', False)

            if auto_processes_disabled:
                logger.info(f"Automatic processes are disabled. Deferring priority update for {translation.base_word} to scheduled task.")
                return True

            # Check if the static file exists
            if not os.path.exists(self.static_file_path):
                logger.error(f"Static file not found: {self.static_file_path}")
                return False

            # Read the current content
            with open(self.static_file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Find the start of the JSON object
            start_index = content.find('const ALL_TRANSLATIONS = ')
            if start_index == -1:
                logger.error("Could not find ALL_TRANSLATIONS variable in static file")
                return False

            # Find the start and end of the JSON object
            json_start = content.find('{', start_index)
            json_end = content.rfind('}', json_start) + 1

            if json_start == -1 or json_end == -1:
                logger.error("Could not find JSON object boundaries in static file")
                return False

            # Extract the JSON part
            json_str = content[json_start:json_end]

            try:
                # Parse the JSON
                translations_dict = json.loads(json_str)

                # Determine the direction
                if translation.source_language.code == 'tgl' and translation.target_language.code == 'ted':
                    direction = 'tgl_to_ted'
                elif translation.source_language.code == 'ted' and translation.target_language.code == 'tgl':
                    direction = 'ted_to_tgl'
                else:
                    logger.error(f"Unsupported language pair: {translation.source_language.code} to {translation.target_language.code}")
                    return False

                # Update the priority
                if direction in translations_dict and translation.base_word.lower() in translations_dict[direction]:
                    translations_dict[direction][translation.base_word.lower()]['priority'] = is_priority
                    translations_dict[direction][translation.base_word.lower()]['confidence'] = 0.95 if is_priority else 0.8

                    # Write the updated JSON back to the file
                    new_content = content[:json_start] + json.dumps(translations_dict, ensure_ascii=False, indent=2) + content[json_end:]

                    with open(self.static_file_path, 'w', encoding='utf-8') as f:
                        f.write(new_content)

                    logger.info(f"Updated priority for translation in static file: {translation.base_word} → {translation.translation} (priority: {is_priority})")
                    return True
                else:
                    logger.warning(f"Translation not found in static file: {translation.base_word}")
                    return False

            except json.JSONDecodeError:
                logger.error("Error parsing JSON from static file")
                return False

        except Exception as e:
            logger.error(f"Error updating translation priority: {str(e)}")
            return False

    def _update_static_file(self):
        """Update the static translations file"""
        try:
            # Check if automatic processes are disabled
            from django.conf import settings
            auto_processes_disabled = getattr(settings, 'DISABLE_AUTO_PROCESSES', False)

            if auto_processes_disabled:
                logger.info("Automatic processes are disabled. Deferring static file update to scheduled task.")
                return True

            from .utils.static_file_updater import update_static_translations_file
            update_static_translations_file(background=True, force=True)
            logger.info("Triggered static file update")
            return True
        except Exception as e:
            logger.error(f"Error triggering static file update: {str(e)}")
            return False

    def _db_worker(self):
        """
        Worker thread that processes database operations from the queue.
        This prevents database locking by ensuring only one operation happens at a time.
        """
        logger.info("Database worker thread started")

        while True:
            try:
                # Get the next operation from the queue
                operation = self.db_queue.get()

                if operation is None:
                    # None is a signal to stop the worker
                    logger.info("Database worker received stop signal")
                    break

                # Set the processing flag
                with self.db_lock:
                    self.is_processing = True

                # Unpack the operation
                func, args, kwargs, callback = operation

                # Execute the operation with retry logic
                max_retries = 3
                retry_delay = 1  # seconds

                for attempt in range(max_retries):
                    try:
                        # Use a transaction to ensure atomicity
                        with transaction.atomic():
                            # Close and reopen the connection to ensure a fresh state
                            connection.close()

                            # Execute the function
                            result = func(*args, **kwargs)

                            # If we get here, the operation succeeded
                            if callback:
                                callback(result)

                            # Log success
                            logger.info(f"Database operation completed successfully: {func.__name__}")

                            # Break out of the retry loop
                            break

                    except OperationalError as e:
                        # Database is locked, retry after a delay
                        if "database is locked" in str(e).lower() and attempt < max_retries - 1:
                            logger.warning(f"Database locked, retrying in {retry_delay} seconds (attempt {attempt + 1}/{max_retries})")
                            time.sleep(retry_delay)
                            # Increase the delay for the next retry
                            retry_delay *= 2
                        else:
                            # Last attempt or other error, log and continue
                            logger.error(f"Database operation failed after {attempt + 1} attempts: {str(e)}")
                            if callback:
                                callback(None)
                            break

                    except Exception as e:
                        # Other error, log and continue
                        logger.error(f"Error in database operation: {str(e)}")
                        if callback:
                            callback(None)
                        break

                # Clear the processing flag
                with self.db_lock:
                    self.is_processing = False

                # Mark the task as done
                self.db_queue.task_done()

            except Exception as e:
                logger.error(f"Error in database worker: {str(e)}")
                # Clear the processing flag
                with self.db_lock:
                    self.is_processing = False

    def _queue_db_operation(self, func, *args, callback=None, **kwargs):
        """
        Queue a database operation to be executed by the worker thread.
        If threading is disabled, execute the operation synchronously.

        Args:
            func: The function to execute
            *args: Arguments to pass to the function
            callback: Optional callback function to call with the result
            **kwargs: Keyword arguments to pass to the function

        Returns:
            bool: True if the operation was queued/executed successfully, False otherwise
        """
        try:
            # If threading is disabled, execute the operation synchronously
            if not self.use_threading:
                # Execute the function with retry logic
                max_retries = self.db_retry_count
                retry_delay = self.db_retry_delay

                for attempt in range(max_retries):
                    try:
                        # Use a transaction to ensure atomicity
                        with transaction.atomic():
                            # Execute the function
                            result = func(*args, **kwargs)

                            # If we get here, the operation succeeded
                            if callback:
                                callback(result)

                            # Log success
                            logger.info(f"Database operation completed successfully: {func.__name__}")

                            return True

                    except OperationalError as e:
                        # Database is locked, retry after a delay
                        if "database is locked" in str(e).lower() and attempt < max_retries - 1:
                            logger.warning(f"Database locked, retrying in {retry_delay} seconds (attempt {attempt + 1}/{max_retries})")
                            time.sleep(retry_delay)
                            # Increase the delay for the next retry
                            retry_delay *= 2
                        else:
                            # Last attempt or other error, log and continue
                            logger.error(f"Database operation failed after {attempt + 1} attempts: {str(e)}")
                            return False

                    except Exception as e:
                        # Other error, log and continue
                        logger.error(f"Error in database operation: {str(e)}")
                        return False

                return False
            else:
                # Add the operation to the queue
                self.db_queue.put((func, args, kwargs, callback))
                return True
        except Exception as e:
            logger.error(f"Error queueing database operation: {str(e)}")
            return False

# Create singleton instance
enhanced_learner = EnhancedLearner()
