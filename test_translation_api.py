#!/usr/bin/env python
"""
Direct API test script for translation functionality.
This script tests the translation API directly using requests.
"""

import requests
import json
import sys
import os
import re

# Configuration
SERVER_URL = "http://localhost:8000"
API_ENDPOINT = "/api/translate/"
RELOAD_ENDPOINT = "/api/reload_translations/"

# Colors for terminal output
class Colors:
    GREEN = '\033[0;32m'
    RED = '\033[0;31m'
    YELLOW = '\033[0;33m'
    BLUE = '\033[0;34m'
    NC = '\033[0m'  # No Color

def colored_print(color, message):
    """Print colored text to the console."""
    print(f"{color}{message}{Colors.NC}")

def get_csrf_token():
    """Get CSRF token from the server."""
    colored_print(Colors.BLUE, "Getting CSRF token...")
    
    try:
        response = requests.get(SERVER_URL)
        # Extract CSRF token from the response
        csrf_token = None
        match = re.search(r'name="csrfmiddlewaretoken" value="([^"]+)"', response.text)
        if match:
            csrf_token = match.group(1)
        
        if not csrf_token:
            colored_print(Colors.RED, "Failed to get CSRF token. Make sure the server is running.")
            sys.exit(1)
        
        colored_print(Colors.GREEN, f"Got CSRF token: {csrf_token}")
        return csrf_token, response.cookies
    except Exception as e:
        colored_print(Colors.RED, f"Error getting CSRF token: {str(e)}")
        sys.exit(1)

def test_translation(source_text, source_lang, target_lang, expected_translation="", csrf_token=None, cookies=None):
    """Test translation API."""
    colored_print(Colors.YELLOW, f"\nTesting translation of '{source_text}' from {source_lang} to {target_lang}")
    
    try:
        # Prepare the request
        headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrf_token
        }
        
        data = {
            'text': source_text,
            'source_lang': source_lang,
            'target_lang': target_lang
        }
        
        # Make the API request
        response = requests.post(
            f"{SERVER_URL}{API_ENDPOINT}",
            headers=headers,
            cookies=cookies,
            data=json.dumps(data)
        )
        
        # Parse the response
        result = response.json()
        colored_print(Colors.BLUE, f"Response: {json.dumps(result, indent=2)}")
        
        # Extract the translation
        if result.get('success') and 'result' in result:
            translation = result['result'].get('translation', '')
            source = result['result'].get('source', '')
            
            colored_print(Colors.BLUE, f"Extracted translation: {translation}")
            colored_print(Colors.BLUE, f"Source: {source}")
            
            # Check if the translation matches the expected result
            if expected_translation and translation == expected_translation:
                colored_print(Colors.GREEN, "SUCCESS: Translation matches expected result!")
                return True
            elif expected_translation:
                colored_print(Colors.RED, "FAILURE: Translation does not match expected result.")
                colored_print(Colors.RED, f"Expected: {expected_translation}")
                colored_print(Colors.RED, f"Got: {translation}")
                return False
            else:
                colored_print(Colors.YELLOW, "No expected translation provided. Manual verification required.")
                return True
        else:
            colored_print(Colors.RED, f"API request failed: {json.dumps(result, indent=2)}")
            return False
    except Exception as e:
        colored_print(Colors.RED, f"Error testing translation: {str(e)}")
        return False

def test_reload_translations(csrf_token=None, cookies=None):
    """Test reload translations API."""
    colored_print(Colors.YELLOW, "\nTesting reload translations API...")
    
    try:
        # Prepare the request
        headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrf_token
        }
        
        # Make the API request
        response = requests.post(
            f"{SERVER_URL}{RELOAD_ENDPOINT}",
            headers=headers,
            cookies=cookies
        )
        
        # Parse the response
        result = response.json()
        colored_print(Colors.BLUE, f"Response: {json.dumps(result, indent=2)}")
        
        # Check if the response indicates success
        if result.get('success'):
            colored_print(Colors.GREEN, "SUCCESS: Translations reloaded successfully!")
            return True
        else:
            colored_print(Colors.RED, "FAILURE: Failed to reload translations.")
            return False
    except Exception as e:
        colored_print(Colors.RED, f"Error testing reload translations: {str(e)}")
        return False

def main():
    """Main function to run the tests."""
    print("=====================================")
    print("Translation API Test")
    print("=====================================")
    print("Testing if the translation API correctly handles longer phrases")
    print("and if approved translations are properly loaded.")
    print()
    
    # Check if the server is running
    try:
        requests.get(SERVER_URL, timeout=5)
    except requests.exceptions.RequestException:
        colored_print(Colors.RED, f"Error: Server is not running at {SERVER_URL}")
        print("Please start the server before running this test.")
        sys.exit(1)
    
    # Get CSRF token
    csrf_token, cookies = get_csrf_token()
    
    # Test some basic translations
    test_translation("kumusta", "tgl", "ted", "", csrf_token, cookies)
    test_translation("salamat", "tgl", "ted", "", csrf_token, cookies)
    
    # Test the longer phrase that was having issues
    colored_print(Colors.YELLOW, "\nTesting the longer phrase that was having issues...")
    long_phrase_success = test_translation(
        "Pumunta ka sa bahay kahapon", "tgl", "ted", 
        "Mënangëy go diyo natëmëgëno", csrf_token, cookies
    )
    
    # Test the "baka" translation
    colored_print(Colors.YELLOW, "\nTesting 'baka' translation...")
    baka_success = test_translation("baka", "tgl", "ted", "beengk", csrf_token, cookies)
    
    # Test reloading translations
    colored_print(Colors.YELLOW, "\nTesting reload translations API...")
    reload_success = test_reload_translations(csrf_token, cookies)
    
    # Test translations again after reload
    colored_print(Colors.YELLOW, "\nTesting translations again after reload...")
    long_phrase_after_reload = test_translation(
        "Pumunta ka sa bahay kahapon", "tgl", "ted", 
        "Mënangëy go diyo natëmëgëno", csrf_token, cookies
    )
    baka_after_reload = test_translation("baka", "tgl", "ted", "beengk", csrf_token, cookies)
    
    # Print summary
    print()
    print("=====================================")
    print("Test Summary")
    print("=====================================")
    colored_print(Colors.YELLOW, f"Long phrase translation test: {'PASSED' if long_phrase_success else 'FAILED'}")
    colored_print(Colors.YELLOW, f"'baka' translation test: {'PASSED' if baka_success else 'FAILED'}")
    colored_print(Colors.YELLOW, f"Reload translations API test: {'PASSED' if reload_success else 'FAILED'}")
    colored_print(Colors.YELLOW, f"Long phrase after reload: {'PASSED' if long_phrase_after_reload else 'FAILED'}")
    colored_print(Colors.YELLOW, f"'baka' after reload: {'PASSED' if baka_after_reload else 'FAILED'}")
    
    # Overall result
    if all([long_phrase_success, baka_success, reload_success, long_phrase_after_reload, baka_after_reload]):
        colored_print(Colors.GREEN, "\nAll tests PASSED!")
    else:
        colored_print(Colors.RED, "\nSome tests FAILED!")
    
    print()
    print("=====================================")
    print("Test completed")
    print("=====================================")
    colored_print(Colors.GREEN, "Done!")

if __name__ == "__main__":
    main()
