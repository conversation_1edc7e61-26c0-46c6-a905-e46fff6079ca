import os
import django
import json

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.test import Client
from django.urls import reverse

# Create a test client
client = Client()

# Test the translation API
data = {
    'text': 'umiiyak',
    'source_lang': 'tgl',
    'target_lang': 'ted'
}

# Make the API request
response = client.post(
    '/api/translate/',
    data=json.dumps(data),
    content_type='application/json'
)

# Print the response
print(f"Status code: {response.status_code}")
print(f"Response: {response.json()}")

# Test with a sentence containing 'umiiyak'
data = {
    'text': 'Ang bata ay umiiyak',
    'source_lang': 'tgl',
    'target_lang': 'ted'
}

# Make the API request
response = client.post(
    '/api/translate/',
    data=json.dumps(data),
    content_type='application/json'
)

# Print the response
print(f"\nSentence test:")
print(f"Status code: {response.status_code}")
print(f"Response: {response.json()}")
