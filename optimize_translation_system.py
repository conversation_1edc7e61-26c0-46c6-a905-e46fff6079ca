"""
<PERSON><PERSON><PERSON> to optimize the translation system and process all pending feedback.
Run this script manually or as a scheduled task to avoid database locks.

This script:
1. Processes all pending feedback files
2. Updates the database with the suggested translations
3. Optimizes the database
4. Updates the translation metrics
5. Ensures the system uses database, attention mechanism, and Hugging Face models for translations
   instead of relying on static files
6. Uses attention mechanism to improve translations with missing or low-quality translations
7. Updates both static files and the database with improved translations
8. Calculates BLEU scores to evaluate translation quality
"""

import os
import sys
import subprocess
import time
import logging
import codecs
from datetime import datetime

# Fix encoding issues for console output
if sys.stdout.encoding != 'utf-8':
    try:
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
    except AttributeError:
        # For Python < 3.7
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'replace')
if sys.stderr.encoding != 'utf-8':
    try:
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')
    except AttributeError:
        # For Python < 3.7
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'replace')

# Configure logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('optimize_system.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('optimize_script')

def process_pending_feedback():
    """Process all pending feedback files."""
    logger.info("Processing pending feedback files")

    try:
        # Check if the new process_all_pending_feedback.py script exists
        if os.path.exists('process_all_pending_feedback.py'):
            # Run the script
            result = subprocess.run(
                [sys.executable, 'process_all_pending_feedback.py'],
                capture_output=True,
                text=True
            )

            # Log the output
            if result.stdout:
                logger.info(f"Feedback processing output:\n{result.stdout}")

            # Log stderr output - only treat as error if return code is non-zero
            if result.stderr:
                if result.returncode != 0:
                    logger.error(f"Feedback processing errors:\n{result.stderr}")
                else:
                    # This is likely just informational output sent to stderr
                    logger.info(f"Feedback processing additional output:\n{result.stderr}")

            return result.returncode == 0
        # Fall back to the old script if the new one doesn't exist
        elif os.path.exists('process_pending_feedback.py'):
            # Run the script
            result = subprocess.run(
                [sys.executable, 'process_pending_feedback.py'],
                capture_output=True,
                text=True
            )

            # Log the output
            if result.stdout:
                logger.info(f"Feedback processing output:\n{result.stdout}")

            # Log stderr output - only treat as error if return code is non-zero
            if result.stderr:
                if result.returncode != 0:
                    logger.error(f"Feedback processing errors:\n{result.stderr}")
                else:
                    # This is likely just informational output sent to stderr
                    logger.info(f"Feedback processing additional output:\n{result.stderr}")

            return result.returncode == 0
        else:
            logger.warning("No feedback processing script found")
            return False
    except Exception as e:
        logger.error(f"Error processing pending feedback: {str(e)}")
        return False

def run_optimization(enable_auto=False, disable_auto=True, enable_huggingface=True, enable_attention=True, skip_static_file=True, force_reload=True, use_bleu=True, load_translations=True):
    """
    Run the optimization command.

    Args:
        enable_auto: If True, enable automatic processes after optimization
        disable_auto: If True, disable automatic processes after optimization
        enable_huggingface: If True, enable Hugging Face integration for translations (default: True)
        enable_attention: If True, enable attention mechanism for translations (default: True)
        skip_static_file: If True, skip static file updates to prioritize database (default: True)
        force_reload: If True, force a complete reload of translations from the database (default: True)
        use_bleu: If True, use BLEU score for translation quality evaluation (default: True)
        load_translations: If True, explicitly load all translations (default: True)
    """
    start_time = time.time()
    logger.info(f"Starting optimization at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # First, clear the Django cache to ensure fresh data
        try:
            # Run a separate command to clear the cache
            cache_clear_cmd = [sys.executable, 'manage.py', 'clear_cache']
            subprocess.run(cache_clear_cmd, capture_output=True, text=True)
            logger.info("Cleared Django cache to ensure fresh data")
        except Exception as e:
            logger.warning(f"Error clearing cache: {str(e)}")

        # Build the command with focus on database operations
        cmd = [sys.executable, 'manage.py', 'optimize_system', '--optimize-db', '--update-metrics']

        # Add load translations flag to ensure database is used
        cmd.append('--load-translations')

        # Skip static file update if requested
        if skip_static_file:
            cmd.append('--skip-static-file')
            logger.info("Skipping static file updates to prioritize database operations")
        else:
            # If we're updating static files, make sure we're using the latest translations
            cmd.append('--update-static-file')
            logger.info("Will update static files with latest translations")

        # Add flags for Hugging Face and attention mechanism
        if enable_huggingface:
            cmd.append('--enable-huggingface')
            logger.info("Enabling Hugging Face integration for translations")

        if enable_attention:
            cmd.append('--enable-attention')
            logger.info("Enabling attention mechanism for translations")

        # Add flag for BLEU score
        if use_bleu:
            cmd.append('--use-bleu')
            logger.info("Enabling BLEU score for translation quality evaluation")

        # Add auto process flags if specified
        if enable_auto:
            cmd.append('--enable-auto-processes')
            logger.info("Will enable automatic processes after optimization")
        if disable_auto:
            cmd.append('--disable-auto-processes')
            logger.info("Will disable automatic processes after optimization")

        # Run the Django management command
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True
        )

        # Log the output
        if result.stdout:
            logger.info(f"Command output:\n{result.stdout}")

        # Log stderr output - only treat as error if return code is non-zero
        if result.stderr:
            if result.returncode != 0:
                logger.error(f"Command errors:\n{result.stderr}")
            else:
                # This is likely just informational output sent to stderr
                logger.info(f"Command additional output:\n{result.stderr}")

        # If force_reload is True, run a custom command to reload translations
        if force_reload and result.returncode == 0:
            logger.info("Forcing a complete reload of translations from the database")
            try:
                # Create a simple Python script to reload translations
                reload_script = """
from django.core.wsgi import get_wsgi_application
import os
import sys
import codecs
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
application = get_wsgi_application()

# Fix encoding issues for console output
if sys.stdout.encoding != 'utf-8':
    try:
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
    except AttributeError:
        # For Python < 3.7
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'replace')
if sys.stderr.encoding != 'utf-8':
    try:
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')
    except AttributeError:
        # For Python < 3.7
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'replace')

from django.core.cache import cache
from translation_app.services import get_translation_service

# Clear the cache
cache.clear()
print("Cache cleared")

# Get the translation service and force a reload
translation_service = get_translation_service(skip_loading_for_admin=False)
translation_service.reload_translations()
print("Translations reloaded successfully")
"""
                # Write the script to a temporary file with UTF-8 encoding
                with open('temp_reload.py', 'w', encoding='utf-8') as f:
                    f.write(reload_script)

                # Run the script
                reload_cmd = [sys.executable, 'temp_reload.py']
                reload_result = subprocess.run(reload_cmd, capture_output=True, text=True)

                if reload_result.stdout:
                    logger.info(f"Reload output:\n{reload_result.stdout}")

                if reload_result.stderr:
                    if reload_result.returncode != 0:
                        logger.error(f"Reload errors:\n{reload_result.stderr}")
                    else:
                        # This is likely just informational output sent to stderr
                        logger.info(f"Reload additional output:\n{reload_result.stderr}")

                if reload_result.returncode == 0:
                    logger.info("Successfully reloaded translations from the database")
                else:
                    logger.warning(f"Translation reload failed with return code {reload_result.returncode}")

                # Clean up the temporary file
                try:
                    os.remove('temp_reload.py')
                except:
                    pass
            except Exception as e:
                logger.error(f"Error reloading translations: {str(e)}")

        # Check return code
        if result.returncode == 0:
            elapsed_time = time.time() - start_time
            logger.info(f"Optimization completed successfully in {elapsed_time:.2f} seconds")
            return True
        else:
            logger.error(f"Optimization failed with return code {result.returncode}")
            return False

    except Exception as e:
        logger.error(f"Error running optimization: {str(e)}")
        return False

def optimize_translations_with_attention():
    """
    Optimize translations using the attention mechanism.
    This function finds translations with missing or low-quality translations
    and uses the attention mechanism to improve them.

    Returns:
        bool: True if successful, False otherwise
    """
    logger.info("Optimizing translations using attention mechanism")

    try:
        # Create a simple Python script to optimize translations
        optimize_script = """
from django.core.wsgi import get_wsgi_application
import os
import logging
import sys
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
application = get_wsgi_application()

# Configure logging with UTF-8 encoding
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('optimize_translations')

# Fix encoding issues for console output
if sys.stdout.encoding != 'utf-8':
    sys.stdout.reconfigure(encoding='utf-8', errors='replace')
if sys.stderr.encoding != 'utf-8':
    sys.stderr.reconfigure(encoding='utf-8', errors='replace')

from django.conf import settings
from django.db import transaction
from translation_app.models import ComprehensiveTranslation, Language, TranslationVersion
from translation_app.services import get_translation_service

# Enable attention mechanism
setattr(settings, 'ATTENTION_MECHANISM_ENABLED', True)
logger.info("Attention mechanism enabled")

# Import attention mechanism
try:
    from translation_app.attention_mechanism import AttentionMechanism
    attention = AttentionMechanism()
    logger.info("Attention mechanism imported successfully")
except ImportError:
    logger.error("Failed to import attention mechanism")
    exit(1)

# Get translation service
translation_service = get_translation_service(skip_loading_for_admin=False)

# Find translations with low confidence or missing translations
low_quality_translations = ComprehensiveTranslation.objects.filter(confidence_score__lt=0.7).order_by('confidence_score')[:100]
logger.info(f"Found {low_quality_translations.count()} low-quality translations to optimize")

optimized_count = 0

# Process each translation
for translation in low_quality_translations:
    try:
        source_text = translation.base_word
        current_translation = translation.translation
        source_lang_code = translation.source_language.code
        target_lang_code = translation.target_language.code

        logger.info(f"Optimizing translation: {source_text} → {current_translation}")

        # Calculate attention between source and target
        attention_data = attention.compute_attention(
            source_text,
            current_translation,
            source_lang_code,
            target_lang_code
        )

        # Find similar translations in the database
        similar_translations = ComprehensiveTranslation.objects.filter(
            source_language=translation.source_language,
            target_language=translation.target_language,
            confidence_score__gte=0.8
        ).exclude(id=translation.id)[:10]

        # Find the best alternative translation
        best_score = 0
        best_translation = None

        for similar in similar_translations:
            # Calculate similarity
            similarity = attention._compute_similarity(source_text, similar.base_word)

            if similarity > 0.3 and similarity > best_score:
                best_score = similarity
                best_translation = similar.translation

        # If we found a better translation
        if best_translation and best_translation != current_translation and best_score > 0.6:
            logger.info(f"Found better translation: {current_translation} → {best_translation} (score: {best_score:.2f})")

            # Create a new version
            with transaction.atomic():
                # Get active version if available
                active_version = translation.versions.filter(is_active=True).first()

                new_version = TranslationVersion(
                    comprehensive_translation=translation,
                    version_number=(active_version.version_number + 1 if active_version else 1),
                    translation=best_translation,
                    confidence_score=min(translation.confidence_score + 0.1, 0.9),
                    is_active=True,
                    notes=f"Optimized using attention mechanism (score: {best_score:.2f})"
                )
                new_version.save()

                # Deactivate previous version if it exists
                if active_version:
                    active_version.is_active = False
                    active_version.save()

                # Update the translation's confidence score
                translation.confidence_score = min(translation.confidence_score + 0.1, 0.9)
                translation.save()

                # Update the translation in the service
                translation_service.update_single_translation(
                    source_text,
                    best_translation,
                    source_lang_code,
                    target_lang_code
                )

                optimized_count += 1
    except Exception as e:
        logger.error(f"Error optimizing translation {translation.id}: {str(e)}")

logger.info(f"Optimized {optimized_count} translations using attention mechanism")

# Update static files with the optimized translations
try:
    translation_service.update_static_files()
    logger.info("Updated static files with optimized translations")
except Exception as e:
    logger.error(f"Error updating static files: {str(e)}")

print(f"Optimized {optimized_count} translations using attention mechanism")
"""
        # Write the script to a temporary file with UTF-8 encoding
        with open('temp_optimize_translations.py', 'w', encoding='utf-8') as f:
            f.write(optimize_script)

        # Run the script
        optimize_cmd = [sys.executable, 'temp_optimize_translations.py']
        optimize_result = subprocess.run(optimize_cmd, capture_output=True, text=True)

        if optimize_result.stdout:
            logger.info(f"Optimization output:\n{optimize_result.stdout}")

        if optimize_result.stderr:
            if optimize_result.returncode != 0:
                logger.error(f"Optimization errors:\n{optimize_result.stderr}")
            else:
                # This is likely just informational output sent to stderr
                logger.info(f"Optimization additional output:\n{optimize_result.stderr}")

        # Clean up the temporary file
        try:
            os.remove('temp_optimize_translations.py')
        except:
            pass

        return optimize_result.returncode == 0
    except Exception as e:
        logger.error(f"Error optimizing translations with attention: {str(e)}")
        return False

def update_static_translations(reduced=False):
    """
    Update the static translations file.

    Args:
        reduced (bool): Whether to create a reduced version with only essential translations

    Returns:
        bool: True if successful, False otherwise
    """
    logger.info("Updating static translations file")

    try:
        # Check if the update_static_translations.py script exists
        if os.path.exists('update_static_translations.py'):
            # Build the command
            cmd = [sys.executable, 'update_static_translations.py']

            # Add reduced flag if specified
            if reduced:
                cmd.append('--reduced')
                logger.info("Creating reduced version with only essential translations")

            # Run the script
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True
            )

            # Log the output
            if result.stdout:
                logger.info(f"Static file update output:\n{result.stdout}")

            # Log stderr output - only treat as error if return code is non-zero
            if result.stderr:
                if result.returncode != 0:
                    logger.error(f"Static file update errors:\n{result.stderr}")
                else:
                    # This is likely just informational output sent to stderr
                    logger.info(f"Static file update additional output:\n{result.stderr}")

            if result.returncode == 0:
                logger.info("Static translations file updated successfully")
                return True
            else:
                logger.error(f"Static translations file update failed with return code {result.returncode}")
                return False
        else:
            # Fall back to the Django management command
            cmd = [sys.executable, 'manage.py', 'update_static_translations']

            # Add reduced flag if specified
            if reduced:
                cmd.append('--reduced')

            # Run the command
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True
            )

            # Log the output
            if result.stdout:
                logger.info(f"Static file update output:\n{result.stdout}")

            # Log stderr output - only treat as error if return code is non-zero
            if result.stderr:
                if result.returncode != 0:
                    logger.error(f"Static file update errors:\n{result.stderr}")
                else:
                    # This is likely just informational output sent to stderr
                    logger.info(f"Static file update additional output:\n{result.stderr}")

            if result.returncode == 0:
                logger.info("Static translations file updated successfully")
                return True
            else:
                logger.error(f"Static translations file update failed with return code {result.returncode}")
                return False
    except Exception as e:
        logger.error(f"Error updating static translations file: {str(e)}")
        return False

if __name__ == '__main__':
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Optimize the translation system and process pending feedback')
    parser.add_argument('--enable-auto', action='store_true', help='Enable automatic processes after optimization')
    parser.add_argument('--disable-auto', action='store_true', help='Disable automatic processes after optimization')
    parser.add_argument('--process-improved', action='store_true', help='Process improved translations and update static file')
    parser.add_argument('--skip-feedback', action='store_true', help='Skip processing pending feedback files')
    parser.add_argument('--disable-huggingface', action='store_true', help='Disable Hugging Face integration for translations (not recommended)')
    parser.add_argument('--disable-attention', action='store_true', help='Disable attention mechanism for translations (not recommended)')
    parser.add_argument('--disable-bleu', action='store_true', help='Disable BLEU score for translation quality evaluation (not recommended)')
    parser.add_argument('--update-static', action='store_true', help='Update static translations file')
    parser.add_argument('--reduced-static', action='store_true', help='Create a reduced version of the static translations file')
    parser.add_argument('--optimize-attention', action='store_true', help='Optimize translations using attention mechanism')
    parser.add_argument('--process-all', action='store_true', help='Process all translations, feedback, and improvements')

    args = parser.parse_args()

    # Check if we should only update the static translations file
    if args.update_static:
        logger.info("Updating static translations file only")
        static_success = update_static_translations(reduced=args.reduced_static)
        if static_success:
            logger.info("Static translations file updated successfully")
        else:
            logger.warning("Static translations file update failed")
        sys.exit(0 if static_success else 1)

    # Check if we should optimize translations using attention mechanism
    if args.optimize_attention:
        logger.info("Optimizing translations using attention mechanism")
        attention_success = optimize_translations_with_attention()
        if attention_success:
            logger.info("Successfully optimized translations using attention mechanism")
        else:
            logger.warning("Failed to optimize translations using attention mechanism")

        # If this is the only operation requested, exit
        if not args.process_all and not args.process_improved and args.skip_feedback:
            sys.exit(0 if attention_success else 1)

    # Check if we should process all translations, feedback, and improvements
    if args.process_all:
        logger.info("Processing all translations, feedback, and improvements")

        # First, process pending feedback
        logger.info("Starting feedback processing")
        feedback_success = process_pending_feedback()
        if feedback_success:
            logger.info("Successfully processed pending feedback")
        else:
            logger.warning("Failed to process some pending feedback")

        # Then optimize translations using attention mechanism
        logger.info("Optimizing translations using attention mechanism")
        attention_success = optimize_translations_with_attention()
        if attention_success:
            logger.info("Successfully optimized translations using attention mechanism")
        else:
            logger.warning("Failed to optimize translations using attention mechanism")

        # Run full optimization
        logger.info("Running full system optimization")
        optimization_success = run_optimization(
            enable_auto=args.enable_auto,
            disable_auto=args.disable_auto,
            enable_huggingface=not args.disable_huggingface,
            enable_attention=not args.disable_attention,
            use_bleu=not args.disable_bleu,
            skip_static_file=False,  # Update static files when processing all
            load_translations=True  # Explicitly load all translations
        )

        if optimization_success:
            logger.info("System optimization completed successfully")
        else:
            logger.warning("System optimization completed with errors")

        # Finally, update static translations
        logger.info("Updating static translations file")
        static_success = update_static_translations(reduced=args.reduced_static)
        if static_success:
            logger.info("Static translations file updated successfully")
        else:
            logger.warning("Static translations file update failed")

        logger.info("All processing tasks completed")
        sys.exit(0 if (feedback_success and attention_success and optimization_success and static_success) else 1)

    # First, process pending feedback unless skipped
    if not args.skip_feedback:
        logger.info("Starting feedback processing")
        feedback_success = process_pending_feedback()
        if feedback_success:
            logger.info("Successfully processed pending feedback")
        else:
            logger.warning("Failed to process some pending feedback")
    else:
        logger.info("Skipping feedback processing as requested")

    # Run optimization with the specified options
    if args.process_improved:
        # Run only the improved translations processing
        cmd = [sys.executable, 'manage.py', 'optimize_system', '--process-improved-translations']
        logger.info("Running improved translations processing and loading translations")
        result = subprocess.run(cmd, capture_output=True, text=True)

        # Log the output
        if result.stdout:
            logger.info(f"Command output:\n{result.stdout}")

        # Log stderr output - only treat as error if return code is non-zero
        if result.stderr:
            if result.returncode != 0:
                logger.error(f"Command errors:\n{result.stderr}")
            else:
                # This is likely just informational output sent to stderr
                logger.info(f"Command additional output:\n{result.stderr}")
    else:
        # Run full optimization
        logger.info("Running full system optimization")
        optimization_success = run_optimization(
            enable_auto=args.enable_auto,
            disable_auto=args.disable_auto,
            enable_huggingface=not args.disable_huggingface,
            enable_attention=not args.disable_attention,
            use_bleu=not args.disable_bleu,
            skip_static_file=True,  # Always skip static file updates during optimization
            load_translations=True  # Explicitly load all translations
        )

        if optimization_success:
            logger.info("System optimization completed successfully")
        else:
            logger.warning("System optimization completed with errors")

    logger.info("All tasks completed")
