"""
Sentence learning module for the translation system.
This module extracts word pairs from sentences and learns from them.
"""

import re
import logging
from django.utils import timezone
from .models import Language, ComprehensiveTranslation, TranslationExample

logger = logging.getLogger(__name__)

class SentenceLearner:
    """
    Learns word pairs and patterns from sentences.
    """

    def align_sentence_pair(self, source_text, target_text, source_lang, target_lang):
        """
        Align words in a sentence pair and return word-by-word mapping.

        Args:
            source_text: The source text
            target_text: The target text
            source_lang: Source language code
            target_lang: Target language code

        Returns:
            list: List of aligned word pairs with source and target words
        """
        if not source_text or not target_text:
            return []

        # Clean up punctuation and normalize
        import re
        source_clean = re.sub(r'[^\w\s]', ' ', source_text.lower())
        target_clean = re.sub(r'[^\w\s]', ' ', target_text.lower())

        # Split the texts into words
        source_words = source_clean.split()
        target_words = target_clean.split()

        # If either text is empty after cleaning, return empty list
        if not source_words or not target_words:
            return []

        # Create the alignment
        aligned_pairs = []

        # If the number of words is the same, do a direct mapping
        if len(source_words) == len(target_words):
            for i in range(len(source_words)):
                aligned_pairs.append({
                    'source_word': source_words[i],
                    'target_word': target_words[i],
                    'confidence': 0.9,
                    'source_idx': i,
                    'target_idx': i
                })
        else:
            # For different lengths, use a more sophisticated approach
            # Calculate the ratio between source and target lengths
            ratio = len(target_words) / max(1, len(source_words))

            for i, source_word in enumerate(source_words):
                # Calculate the approximate position in the target text
                target_idx = min(int(i * ratio), len(target_words) - 1)
                target_word = target_words[target_idx]

                aligned_pairs.append({
                    'source_word': source_word,
                    'target_word': target_word,
                    'confidence': 0.7,  # Lower confidence for this approach
                    'source_idx': i,
                    'target_idx': target_idx
                })

        return aligned_pairs

    def learn_from_sentence_pair(self, source_text, target_text, source_lang, target_lang):
        """
        Extract word pairs from a sentence pair and add them to the database.

        Args:
            source_text: The source text
            target_text: The target text
            source_lang: Source language code
            target_lang: Target language code

        Returns:
            list: List of extracted word pairs
        """
        if not source_text or not target_text:
            return []

        # Clean up punctuation and normalize
        source_clean = re.sub(r'[^\w\s]', '', source_text.lower())
        target_clean = re.sub(r'[^\w\s]', '', target_text.lower())

        # Split into words
        source_words = source_clean.split()
        target_words = target_clean.split()

        # If the number of words is very different, we can't reliably extract word pairs
        if len(source_words) == 0 or len(target_words) == 0:
            return []

        # Known word pairs for specific languages
        known_word_pairs = {
            'tgl_to_ted': {
                'umiiyak': 'këmërew',
                'hindi': 'ënda',
                'nanganak': 'mëgënga',
                'kababaihan': 'libun',
                'ilang': 'uwëni',
                'sila': 'ro',
                'kapag': 'amuk',
                'ang': 'i',
                'mga': 'do',
                'ay': 'i',
                'na': 'ro',
                'sa': 'dob',
                'ng': 'i',
                'ko': 'gu',
                'mo': 'mu',
                'niya': 'no',
                'namin': 'gey',
                'ninyo': 'gom',
                'nila': 'ro',
                'ito': 'ni',
                'iyon': 'nan',
                'dito': 'dini',
                'diyan': 'diyaan',
                'doon': 'diyo',
                'tao': 'étéw',
                'babae': 'libun',
                'lalaki': 'lagéy',
                'bata': 'nga',
                'matanda': 'lukés',
                'maganda': 'fiyo',
                'mabuti': 'fiyo',
                'masama': 'tete',
                'malaki': 'dakél',
                'maliit': 'kloh',
                'mahal': 'kégédaw',
                'gabi': 'kékélungon',
                'umaga': 'kélungonon',
                'hapon': 'kérara',
                'araw': 'fuweh',
                'buwan': 'térésang',
                'taon': 'rahun',
                'tubig': 'wayég',
                'pagkain': 'amaén',
                'bahay': 'laawi',
                'pinto': 'béngaway',
                'bintana': 'falilungan',
                'mesa': 'ahayay',
                'upuan': 'saray',
                'kama': 'katri',
                'kumain': 'mama',
                'uminom': 'miném',
                'matulog': 'fidong',
                'gumising': 'tek',
                'maglakad': 'magéw',
                'tumakbo': 'léméntu',
                'magsalita': 'béréh',
                'makinig': 'fégélingo',
                'magbasa': 'masa',
                'magsulat': 'sémulat'
            }
        }

        # Direction key
        direction = f"{source_lang}_to_{target_lang}"

        # Extract word pairs
        extracted_pairs = []

        # First check for known words
        for source_word in source_words:
            clean_word = re.sub(r'[^\w\s]', '', source_word.lower())

            if direction in known_word_pairs and clean_word in known_word_pairs[direction]:
                target_word = known_word_pairs[direction][clean_word]

                # Add to database
                self._add_to_database(clean_word, target_word, source_lang, target_lang)
                extracted_pairs.append((clean_word, target_word))
                logger.info(f"Added known word pair to database: {clean_word} → {target_word}")

        # If we have the same number of words, try direct mapping
        if len(source_words) == len(target_words) and not extracted_pairs:
            for i in range(len(source_words)):
                # Skip very short words
                if len(source_words[i]) < 3 or len(target_words[i]) < 3:
                    continue

                # Add to database
                self._add_to_database(source_words[i], target_words[i], source_lang, target_lang)
                extracted_pairs.append((source_words[i], target_words[i]))

        # If we still don't have any pairs, use a simple position-based approach
        if not extracted_pairs and len(source_words) > 0 and len(target_words) > 0:
            # Calculate the ratio between source and target lengths
            ratio = len(target_words) / len(source_words)

            for i, source_word in enumerate(source_words):
                # Skip very short words
                if len(source_word) < 3:
                    continue

                # Calculate the approximate position in the target text
                target_idx = min(int(i * ratio), len(target_words) - 1)
                target_word = target_words[target_idx]

                # Skip very short target words
                if len(target_word) < 3:
                    continue

                # Add to database
                self._add_to_database(source_word, target_word, source_lang, target_lang)
                extracted_pairs.append((source_word, target_word))

        # Also add the full sentence as an example
        self._add_sentence_example(source_text, target_text, source_lang, target_lang)

        logger.info(f"Extracted {len(extracted_pairs)} word pairs from sentence")
        return extracted_pairs

    def _add_to_database(self, source_word, target_word, source_lang, target_lang):
        """Add a word pair to the database"""
        try:
            # Get language objects
            source_language = Language.objects.get(code=source_lang)
            target_language = Language.objects.get(code=target_lang)

            # Create or update the translation
            translation, created = ComprehensiveTranslation.objects.get_or_create(
                base_word=source_word,
                source_language=source_language,
                target_language=target_language,
                defaults={
                    'translation': target_word,
                    'part_of_speech': 'word',
                    'notes': 'Learned from sentence'
                }
            )

            if not created:
                # Update the translation if it's different
                if translation.translation != target_word:
                    # Create a new version
                    from .models import TranslationVersion
                    TranslationVersion.objects.create(
                        comprehensive_translation=translation,
                        translation=translation.translation,  # Save the old translation
                        created_by='system',
                        confidence_score=0.8,
                        is_active=False,
                        notes='Archived before update from sentence learning'
                    )

                    # Update the translation
                    translation.translation = target_word
                    translation.updated_at = timezone.now()
                    translation.save()

                    logger.info(f"Updated translation: {source_word} → {target_word}")
            else:
                logger.info(f"Created new translation: {source_word} → {target_word}")

        except Exception as e:
            logger.error(f"Error adding word pair to database: {str(e)}")

    def _add_sentence_example(self, source_text, target_text, source_lang, target_lang):
        """Add a sentence pair as an example"""
        try:
            # Get language objects
            source_language = Language.objects.get(code=source_lang)
            target_language = Language.objects.get(code=target_lang)

            # Create or get the sentence translation
            sentence_trans, created = ComprehensiveTranslation.objects.get_or_create(
                base_word=source_text,
                source_language=source_language,
                target_language=target_language,
                defaults={
                    'translation': target_text,
                    'part_of_speech': 'sentence',
                    'notes': 'Added as sentence example'
                }
            )

            if not created and sentence_trans.translation != target_text:
                # Update the translation
                sentence_trans.translation = target_text
                sentence_trans.updated_at = timezone.now()
                sentence_trans.save()

            # Add as an example
            example, created = TranslationExample.objects.get_or_create(
                comprehensive_translation=sentence_trans,
                source_text=source_text,
                target_text=target_text
            )

            if created:
                logger.info(f"Added sentence example: {source_text} → {target_text}")

        except Exception as e:
            logger.error(f"Error adding sentence example: {str(e)}")

# Create singleton instance
sentence_learner = SentenceLearner()
