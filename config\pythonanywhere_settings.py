"""
PythonAnywhere-specific settings for the translation project.
This file is imported by settings.py when running on PythonAnywhere.
"""

def apply_pythonanywhere_settings(settings_obj):
    """
    Apply PythonAnywhere-specific settings to the settings object.

    Args:
        settings_obj: The settings object to modify (can be a dict or an object with attributes)
    """
    # Handle both dictionary and object with attributes
    is_dict = isinstance(settings_obj, dict)

    # Helper function to get and set attributes/items
    def get_setting(name, default=None):
        if is_dict:
            return settings_obj.get(name, default)
        return getattr(settings_obj, name, default)

    def set_setting(name, value):
        if is_dict:
            settings_obj[name] = value
        else:
            setattr(settings_obj, name, value)

    # Database settings
    databases = get_setting('DATABASES')
    if databases and 'default' in databases:
        databases['default']['TIMEOUT'] = 300  # Increase database timeout
        databases['default']['OPTIONS'] = {
            'timeout': 300,  # SQLite timeout
            'isolation_level': None,  # Use autocommit mode
            'check_same_thread': False,  # Allow access from multiple threads
            'cached_statements': 500,  # Increase statement cache size
        }
        databases['default']['CONN_MAX_AGE'] = 300  # Keep connections open for 5 minutes

    # Cache settings
    caches = get_setting('CACHES')
    if caches and 'default' in caches:
        caches['default']['TIMEOUT'] = 3600  # Increase cache timeout

    # Media files (but not static files - those are handled in settings.py)
    set_setting('MEDIA_ROOT', '/home/<USER>/teduray/media')

    # Debug settings
    set_setting('DEBUG', False)  # Disable debug mode

    # Logging settings
    logging_config = get_setting('LOGGING')
    if logging_config and 'handlers' in logging_config and 'file' in logging_config['handlers']:
        logging_config['handlers']['file']['filename'] = '/home/<USER>/teduray/translation.log'

    # Add PythonAnywhere-specific allowed hosts
    allowed_hosts = get_setting('ALLOWED_HOSTS', [])
    if not allowed_hosts:
        allowed_hosts = []
        set_setting('ALLOWED_HOSTS', allowed_hosts)

    # Add PythonAnywhere domains if not already present
    pythonanywhere_hosts = [
        'teduraytranslator.online',
        'www.teduraytranslator.online',
        'webapp-2551651.pythonanywhere.com',
    ]

    for host in pythonanywhere_hosts:
        if host not in allowed_hosts:
            allowed_hosts.append(host)

    return settings_obj
