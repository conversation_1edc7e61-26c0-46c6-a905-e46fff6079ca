"""
Management command to analyze translations and extract patterns.
"""

from django.core.management.base import BaseCommand
from translation_app.pattern_learning import analyze_all_translations
from translation_app.services import get_translation_service

class Command(BaseCommand):
    help = 'Analyze all translations in the database to extract patterns'
    
    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting translation analysis...'))
        
        # Analyze all translations
        results = analyze_all_translations()
        
        # Print results
        self.stdout.write(self.style.SUCCESS('Analysis complete!'))
        self.stdout.write(self.style.SUCCESS('Results:'))
        
        # Tagalog to Teduray
        self.stdout.write(self.style.SUCCESS('Tagalog to Teduray:'))
        if 'error' in results.get('tgl_to_ted', {}):
            self.stdout.write(self.style.ERROR(f"Error: {results['tgl_to_ted']['error']}"))
        else:
            self.stdout.write(self.style.SUCCESS(f"Word patterns: {results['tgl_to_ted'].get('word_patterns', 0)}"))
            self.stdout.write(self.style.SUCCESS(f"Phrase patterns: {results['tgl_to_ted'].get('phrase_patterns', 0)}"))
            self.stdout.write(self.style.SUCCESS(f"Grammar patterns: {results['tgl_to_ted'].get('grammar_patterns', 0)}"))
            self.stdout.write(self.style.SUCCESS(f"Substitution patterns: {results['tgl_to_ted'].get('substitution_patterns', 0)}"))
        
        # Teduray to Tagalog
        self.stdout.write(self.style.SUCCESS('Teduray to Tagalog:'))
        if 'error' in results.get('ted_to_tgl', {}):
            self.stdout.write(self.style.ERROR(f"Error: {results['ted_to_tgl']['error']}"))
        else:
            self.stdout.write(self.style.SUCCESS(f"Word patterns: {results['ted_to_tgl'].get('word_patterns', 0)}"))
            self.stdout.write(self.style.SUCCESS(f"Phrase patterns: {results['ted_to_tgl'].get('phrase_patterns', 0)}"))
            self.stdout.write(self.style.SUCCESS(f"Grammar patterns: {results['ted_to_tgl'].get('grammar_patterns', 0)}"))
            self.stdout.write(self.style.SUCCESS(f"Substitution patterns: {results['ted_to_tgl'].get('substitution_patterns', 0)}"))
        
        # Reload the translation service
        self.stdout.write(self.style.SUCCESS('Reloading translation service...'))
        translation_service = get_translation_service()
        translation_service.load_translations(clear_cache=True)
        
        self.stdout.write(self.style.SUCCESS('Done!'))
