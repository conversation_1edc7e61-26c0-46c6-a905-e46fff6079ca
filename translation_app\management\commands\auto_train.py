"""
Management command to automatically trigger model training.
"""

from django.core.management.base import BaseCommand
from translation_app.auto_training import check_and_trigger_training, run_scheduled_training


class Command(BaseCommand):
    help = 'Automatically trigger model training based on new translations or schedule'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force training regardless of new data or cooldown period',
        )

    def handle(self, *args, **options):
        force = options.get('force', False)

        if force:
            self.stdout.write('Forcing scheduled training...')
            success = run_scheduled_training(force=True)
        else:
            self.stdout.write('Checking for new translations to trigger training...')
            # Temporarily enable automatic training for this command
            from translation_app.auto_training import AUTOMATIC_TRAINING_ENABLED
            old_value = AUTOMATIC_TRAINING_ENABLED
            import translation_app.auto_training
            translation_app.auto_training.AUTOMATIC_TRAINING_ENABLED = True

            try:
                success = check_and_trigger_training()
            finally:
                # Restore the original value
                translation_app.auto_training.AUTOMATIC_TRAINING_ENABLED = old_value

        if success:
            self.stdout.write(self.style.SUCCESS('Training completed successfully'))
        else:
            self.stdout.write(self.style.WARNING('No training was performed'))
