from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('translation_app', '0009_translationrating'),
    ]

    operations = [
        migrations.AddField(
            model_name='attentiondata',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='attentiondata',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='attentiondata',
            name='word_level_improvement',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='attentiondata',
            name='sentence_level_improvement',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='attentiondata',
            name='phrase_level_improvement',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='attentiondata',
            name='attention_improvement',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='attentiondata',
            name='confidence_score',
            field=models.FloatField(default=0.7),
        ),
        migrations.AddField(
            model_name='attentiondata',
            name='bleu_score',
            field=models.FloatField(default=0.7),
        ),
        migrations.AddField(
            model_name='attentiondata',
            name='processing_time',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='attentiondata',
            name='sentence_type',
            field=models.CharField(choices=[('declarative', 'Declarative'), ('interrogative', 'Interrogative'), ('imperative', 'Imperative'), ('exclamatory', 'Exclamatory'), ('other', 'Other')], default='declarative', max_length=20),
        ),
    ]
