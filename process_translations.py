#!/usr/bin/env python
"""
Process Translations Script

This script processes all pending translations, improvements, feedback, and likes.
It updates both the static files and the database.

Usage:
    python process_translations.py [--force] [--verbose] [--dry-run]

Options:
    --force     Force processing of all translations, even if they've been processed before
    --verbose   Show detailed output
    --dry-run   Don't make any changes, just show what would be done
"""

import os
import sys
import json
import logging
import argparse
import datetime
import django
from django.db import transaction
from django.utils import timezone

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('process_translations.log')
    ]
)
logger = logging.getLogger('process_translations')

# Import Django models
from django.conf import settings
from translation_app.models import (
    Language, ComprehensiveTranslation, TranslationFeedback,
    TranslationVersion, TranslationSuggestion, TranslationRating
)
from translation_app.services import get_translation_service

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Process translations and update static files')
    parser.add_argument('--force', action='store_true', help='Force processing of all translations')
    parser.add_argument('--verbose', action='store_true', help='Show detailed output')
    parser.add_argument('--dry-run', action='store_true', help='Don\'t make any changes, just show what would be done')
    return parser.parse_args()

def process_improvements():
    """Process all pending translation improvements from suggestions."""
    logger.info("Processing translation improvements from suggestions...")

    # Get all unprocessed suggestions
    suggestions = TranslationSuggestion.objects.filter(status='pending')
    logger.info(f"Found {suggestions.count()} unprocessed suggestions")

    # Get language objects
    try:
        tagalog = Language.objects.get(code='tgl')
        teduray = Language.objects.get(code='ted')
    except Language.DoesNotExist:
        logger.error("Required languages not found in database")
        return 0

    processed_count = 0

    for suggestion in suggestions:
        try:
            logger.info(f"Processing suggestion: {suggestion.original_text} → {suggestion.suggested_translation}")

            # Check if a translation already exists
            existing = ComprehensiveTranslation.objects.filter(
                base_word=suggestion.original_text,
                source_language=suggestion.source_language,
                target_language=suggestion.target_language
            ).first()

            if existing:
                # Create a new version for the existing translation
                new_version = TranslationVersion(
                    comprehensive_translation=existing,
                    translation=suggestion.suggested_translation,
                    confidence_score=min(existing.confidence_score + 0.1, 1.0),  # Increase confidence
                    is_active=True,
                    notes='Added from user suggestion',
                    created_by='user_suggestion'
                )

                # Deactivate previous active versions
                TranslationVersion.objects.filter(
                    comprehensive_translation=existing,
                    is_active=True
                ).update(is_active=False)

                # Save the new version
                new_version.save()

                # Update the existing translation's updated_at timestamp
                existing.updated_at = timezone.now()
                existing.save()

                logger.info(f"Added new version to existing translation: {existing.id}")
            else:
                # Create new translation
                new_translation = ComprehensiveTranslation(
                    base_word=suggestion.original_text,
                    translation=suggestion.suggested_translation,
                    source_language=suggestion.source_language,
                    target_language=suggestion.target_language,
                    part_of_speech='',  # Default empty
                    confidence_score=0.9,  # High confidence for user-provided improvements
                    notes='Added from user suggestion',
                    cultural_notes='',
                    created_at=timezone.now(),
                    updated_at=timezone.now()
                )
                new_translation.save()

                # Create initial version
                TranslationVersion.objects.create(
                    comprehensive_translation=new_translation,
                    translation=suggestion.suggested_translation,
                    confidence_score=0.9,
                    is_active=True,
                    notes='Initial version from user suggestion',
                    created_by='user_suggestion'
                )

                logger.info(f"Created new translation: {new_translation.id}")

            # Mark suggestion as processed
            suggestion.status = 'approved'
            suggestion.reviewed_at = timezone.now()
            suggestion.save()

            processed_count += 1

        except Exception as e:
            logger.error(f"Error processing suggestion {suggestion.id}: {str(e)}")

    logger.info(f"Processed {processed_count} suggestions")
    return processed_count

def process_feedback():
    """Process all pending translation feedback."""
    logger.info("Processing translation feedback...")

    # Get all unprocessed feedback
    feedback = TranslationFeedback.objects.filter(processed=False)
    logger.info(f"Found {feedback.count()} unprocessed feedback items")

    processed_count = 0

    for item in feedback:
        try:
            logger.info(f"Processing feedback: {item.original_text} → {item.translated_text} (Rating: {item.rating})")

            # Only process high ratings (4-5) or feedback with suggested translations
            if item.rating >= 4 or item.suggested_translation:
                # Check if a translation already exists
                existing = ComprehensiveTranslation.objects.filter(
                    base_word=item.original_text,
                    source_language=item.source_language,
                    target_language=item.target_language
                ).first()

                if existing:
                    # If there's a suggested translation, create a new version
                    if item.suggested_translation and item.suggested_translation.strip():
                        # Create a new version with the suggested translation
                        new_version = TranslationVersion(
                            comprehensive_translation=existing,
                            translation=item.suggested_translation,
                            confidence_score=min(existing.confidence_score + 0.1, 1.0),  # Increase confidence
                            is_active=True,
                            notes=f'Added from user feedback (rating: {item.rating})',
                            created_by='user_feedback'
                        )

                        # Deactivate previous active versions
                        TranslationVersion.objects.filter(
                            comprehensive_translation=existing,
                            is_active=True
                        ).update(is_active=False)

                        # Save the new version
                        new_version.save()

                        logger.info(f"Added new version to existing translation: {existing.id}")
                    else:
                        # Just update confidence score based on rating
                        confidence_boost = (item.rating - 3) * 0.05  # 0.05 for rating 4, 0.1 for rating 5
                        existing.confidence_score = min(existing.confidence_score + confidence_boost, 1.0)
                        existing.updated_at = timezone.now()
                        existing.save()
                        logger.info(f"Updated confidence for translation: {existing.id} to {existing.confidence_score}")
                elif item.suggested_translation and item.suggested_translation.strip():
                    # Create new translation if it doesn't exist but we have a suggested translation
                    new_translation = ComprehensiveTranslation(
                        base_word=item.original_text,
                        translation=item.suggested_translation,
                        source_language=item.source_language,
                        target_language=item.target_language,
                        part_of_speech='',  # Default empty
                        confidence_score=0.8 + (item.rating - 3 if item.rating else 0) * 0.05,  # Base confidence + rating boost
                        notes='Added from user feedback',
                        cultural_notes='',
                        created_at=timezone.now(),
                        updated_at=timezone.now()
                    )
                    new_translation.save()

                    # Create initial version
                    TranslationVersion.objects.create(
                        comprehensive_translation=new_translation,
                        translation=item.suggested_translation,
                        confidence_score=new_translation.confidence_score,
                        is_active=True,
                        notes=f'Initial version from user feedback (rating: {item.rating})',
                        created_by='user_feedback'
                    )

                    logger.info(f"Created new translation from feedback: {new_translation.id}")

            # Mark feedback as processed
            item.processed = True
            item.processed_at = timezone.now()
            item.save()

            processed_count += 1

        except Exception as e:
            logger.error(f"Error processing feedback {item.id}: {str(e)}")

    logger.info(f"Processed {processed_count} feedback items")
    return processed_count

def process_likes():
    """Process all pending translation ratings (likes)."""
    logger.info("Processing translation ratings...")

    # Get all unprocessed ratings with high scores (4-5)
    ratings = TranslationRating.objects.filter(processed=False, rating__gte=4)
    logger.info(f"Found {ratings.count()} unprocessed high ratings")

    processed_count = 0

    for rating in ratings:
        try:
            logger.info(f"Processing rating: {rating.comprehensive_translation.base_word} → {rating.comprehensive_translation.translation} (Rating: {rating.rating})")

            # Get the translation
            translation = rating.comprehensive_translation

            # Update confidence score
            confidence_boost = (rating.rating - 3) * 0.05  # 0.05 for rating 4, 0.1 for rating 5
            translation.confidence_score = min(translation.confidence_score + confidence_boost, 1.0)
            translation.updated_at = timezone.now()
            translation.save()

            logger.info(f"Updated confidence for translation: {translation.id} to {translation.confidence_score}")

            # Mark rating as processed
            rating.processed = True
            rating.processed_at = timezone.now()
            rating.save()

            processed_count += 1

        except Exception as e:
            logger.error(f"Error processing rating {rating.id}: {str(e)}")

    logger.info(f"Processed {processed_count} ratings")
    return processed_count

def process_pending_translations():
    """Process all pending AI translation reviews."""
    logger.info("Processing pending AI translation reviews...")

    # Import AITranslationReview if available
    try:
        from translation_app.models import AITranslationReview
    except ImportError:
        logger.warning("AITranslationReview model not available, skipping pending translations processing")
        return 0

    # Get all pending AI translation reviews
    pending = AITranslationReview.objects.filter(status='pending')
    logger.info(f"Found {pending.count()} pending AI translation reviews")

    processed_count = 0

    for item in pending:
        try:
            logger.info(f"Processing AI translation review: {item.source_text} → {item.ai_translation}")

            # Check if a translation already exists
            existing = ComprehensiveTranslation.objects.filter(
                base_word=item.source_text,
                source_language=item.source_language,
                target_language=item.target_language
            ).first()

            if existing:
                # Only update if AI confidence is higher
                if item.confidence > existing.confidence_score:
                    # Create a new version
                    new_version = TranslationVersion(
                        comprehensive_translation=existing,
                        translation=item.ai_translation,
                        confidence_score=item.confidence,
                        is_active=True,
                        notes='Added from AI translation review',
                        created_by='ai'
                    )

                    # Deactivate previous active versions
                    TranslationVersion.objects.filter(
                        comprehensive_translation=existing,
                        is_active=True
                    ).update(is_active=False)

                    # Save the new version
                    new_version.save()

                    # Update the existing translation's updated_at timestamp
                    existing.updated_at = timezone.now()
                    existing.save()

                    logger.info(f"Added new version to existing translation: {existing.id}")
            else:
                # Create new translation
                new_translation = ComprehensiveTranslation(
                    base_word=item.source_text,
                    translation=item.ai_translation,
                    source_language=item.source_language,
                    target_language=item.target_language,
                    part_of_speech='phrase' if ' ' in item.source_text else 'word',
                    confidence_score=item.confidence,
                    notes='Added from AI translation review',
                    cultural_notes='',
                    created_at=timezone.now(),
                    updated_at=timezone.now()
                )
                new_translation.save()

                # Create initial version
                TranslationVersion.objects.create(
                    comprehensive_translation=new_translation,
                    translation=item.ai_translation,
                    confidence_score=item.confidence,
                    is_active=True,
                    notes='Initial version from AI translation',
                    created_by='ai'
                )

                logger.info(f"Created new translation: {new_translation.id}")

            # Mark AI translation review as processed
            item.status = 'auto_approved'
            item.reviewed_at = timezone.now()
            item.save()

            processed_count += 1

        except Exception as e:
            logger.error(f"Error processing AI translation review {item.id}: {str(e)}")

    logger.info(f"Processed {processed_count} AI translation reviews")
    return processed_count

def update_static_files():
    """Update static files with the latest translations."""
    logger.info("Updating static files...")

    try:
        # Get the translation service
        translation_service = get_translation_service()

        # Update static files
        translation_service.update_static_files()

        logger.info("Static files updated successfully")
        return True
    except Exception as e:
        logger.error(f"Error updating static files: {str(e)}")
        return False

def main():
    """Main function to process all translations and update static files."""
    args = parse_args()

    if args.verbose:
        logger.setLevel(logging.DEBUG)

    logger.info("Starting translation processing script")
    logger.info(f"Options: force={args.force}, verbose={args.verbose}, dry_run={args.dry_run}")

    if args.dry_run:
        logger.info("DRY RUN MODE - No changes will be made")

    try:
        # Process all types of translations
        with transaction.atomic():
            if args.dry_run:
                # In dry run mode, just count but don't make changes
                transaction.set_rollback(True)

            improvements_count = process_improvements()
            feedback_count = process_feedback()
            likes_count = process_likes()
            pending_count = process_pending_translations()

            total_processed = improvements_count + feedback_count + likes_count + pending_count

            if args.dry_run:
                logger.info(f"DRY RUN: Would process {total_processed} items")
            else:
                logger.info(f"Processed {total_processed} items")

        # Update static files (outside transaction to avoid locking issues)
        if not args.dry_run and total_processed > 0:
            update_static_files()

        logger.info("Translation processing completed successfully")
        return 0
    except Exception as e:
        logger.error(f"Error in main process: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
