"""
Test script to verify that the static file update system works correctly.
This script checks paths and configurations to ensure compatibility with both
local environment and PythonAnywhere.
"""

import os
import sys
import django
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

def test_paths():
    """Test that all paths are correctly configured and accessible."""
    from django.conf import settings
    
    # Check BASE_DIR
    logger.info(f"BASE_DIR: {settings.BASE_DIR}")
    if not os.path.exists(settings.BASE_DIR):
        logger.error(f"BASE_DIR does not exist: {settings.BASE_DIR}")
        return False
    
    # Check logs directory
    logs_dir = os.path.join(settings.BASE_DIR, 'logs')
    os.makedirs(logs_dir, exist_ok=True)
    logger.info(f"Logs directory: {logs_dir}")
    if not os.path.exists(logs_dir):
        logger.error(f"Logs directory does not exist: {logs_dir}")
        return False
    
    # Check static directory
    static_dir = os.path.join(settings.BASE_DIR, 'static')
    logger.info(f"Static directory: {static_dir}")
    if not os.path.exists(static_dir):
        logger.error(f"Static directory does not exist: {static_dir}")
        return False
    
    # Check static translations directory
    static_translations_dir = os.path.join(static_dir, 'translation_app', 'js')
    os.makedirs(static_translations_dir, exist_ok=True)
    logger.info(f"Static translations directory: {static_translations_dir}")
    if not os.path.exists(static_translations_dir):
        logger.error(f"Static translations directory does not exist: {static_translations_dir}")
        return False
    
    # Check if we can create and write to files
    test_file = os.path.join(logs_dir, 'test_file.txt')
    try:
        with open(test_file, 'w') as f:
            f.write('Test file')
        logger.info(f"Successfully created test file: {test_file}")
        os.remove(test_file)
        logger.info(f"Successfully removed test file: {test_file}")
    except Exception as e:
        logger.error(f"Error creating/removing test file: {str(e)}")
        return False
    
    return True

def test_platform_detection():
    """Test that platform detection works correctly."""
    from translation_app.utils.platform_detection import is_pythonanywhere, PLATFORM_SETTINGS
    
    is_pa = is_pythonanywhere()
    logger.info(f"Is PythonAnywhere: {is_pa}")
    logger.info(f"Platform settings: {PLATFORM_SETTINGS}")
    
    return True

def test_static_file_updater():
    """Test that the static file updater works correctly."""
    from translation_app.utils.static_file_updater import update_static_translations_file
    
    # Create a marker file
    result = update_static_translations_file(force=True)
    logger.info(f"Static file update result: {result}")
    
    return result

def main():
    """Run all tests."""
    logger.info("Starting tests...")
    
    # Test paths
    logger.info("Testing paths...")
    if not test_paths():
        logger.error("Path tests failed")
        return False
    
    # Test platform detection
    logger.info("Testing platform detection...")
    if not test_platform_detection():
        logger.error("Platform detection tests failed")
        return False
    
    # Test static file updater
    logger.info("Testing static file updater...")
    if not test_static_file_updater():
        logger.error("Static file updater tests failed")
        return False
    
    logger.info("All tests passed!")
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
