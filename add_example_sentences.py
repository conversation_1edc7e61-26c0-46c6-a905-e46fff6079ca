import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from translation_app.models import Language, ComprehensiveTranslation, TranslationExample
from translation_app.sentence_learner import sentence_learner

# Example sentences with translations
example_sentences = [
    {
        'source': 'Ang ilang mga kababaihan, kapag sila ay nanganak, ay hindi umiiyak.',
        'target': 'Uwëni do dumo libun, amuk mëg<PERSON>nga ro, ënda këmërew ro.',
        'source_lang': 'tgl',
        'target_lang': 'ted'
    },
    {
        'source': 'Ang bata ay umiiyak dahil sa gutom.',
        'target': 'I nga këmërew sabaf bé bitil.',
        'source_lang': 'tgl',
        'target_lang': 'ted'
    },
    {
        'source': 'Hindi siya umiiyak kahit na nasasaktan siya.',
        'target': 'Ënda këmërew ro fiyon fo mérasay ro.',
        'source_lang': 'tgl',
        'target_lang': 'ted'
    },
    {
        'source': 'Umiiyak ang sanggol kapag gutom siya.',
        'target': 'Këmërew i nga amuk bitil ro.',
        'source_lang': 'tgl',
        'target_lang': 'ted'
    },
    {
        'source': 'Kapag umiiyak ang bata, kailangan nating alamin kung bakit.',
        'target': 'Amuk këmërew i nga, kailangan tom alamin ati sedek.',
        'source_lang': 'tgl',
        'target_lang': 'ted'
    }
]

# Add the examples to the database
for example in example_sentences:
    # First add to the database directly
    try:
        # Get language objects
        source_language = Language.objects.get(code=example['source_lang'])
        target_language = Language.objects.get(code=example['target_lang'])
        
        # Add the translation
        translation, created = ComprehensiveTranslation.objects.get_or_create(
            base_word=example['source'],
            source_language=source_language,
            target_language=target_language,
            defaults={
                'translation': example['target'],
                'part_of_speech': 'sentence',
                'notes': 'Example sentence'
            }
        )
        
        if created:
            print(f"Added new translation: {example['source']} → {example['target']}")
        else:
            print(f"Updated existing translation: {example['source']} → {example['target']}")
        
        # Add an example
        example_obj, created = TranslationExample.objects.get_or_create(
            comprehensive_translation=translation,
            source_text=example['source'],
            target_text=example['target']
        )
        
        if created:
            print("Added example")
        else:
            print("Example already exists")
            
        # Now use the sentence learner to extract word pairs
        pairs = sentence_learner.learn_from_sentence_pair(
            example['source'], 
            example['target'], 
            example['source_lang'], 
            example['target_lang']
        )
        
        print(f"Extracted {len(pairs)} word pairs from sentence")
        for source, target in pairs:
            print(f"  {source} → {target}")
        
        print()
        
    except Exception as e:
        print(f"Error: {str(e)}")
