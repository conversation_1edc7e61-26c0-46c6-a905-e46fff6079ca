"""
WSGI config for translation project.

This file is used by PythonAnywhere to serve the application.
It includes optimizations for faster startup time and database initialization.
"""

import os
import sys
import logging
import time

# Add the project directory to the Python path
project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_dir not in sys.path:
    sys.path.insert(0, project_dir)

# Define a function to check if we're running on PythonAnywhere
def is_running_on_pythonanywhere():
    """
    Detect if the app is running on PythonAnywhere.
    """
    # Check for PythonAnywhere-specific environment variables or paths
    if 'PYTHONANYWHERE_DOMAIN' in os.environ or 'PYTHONANYWHERE_SITE' in os.environ:
        return True

    # Check for PythonAnywhere in the hostname
    try:
        import socket
        if 'pythonanywhere' in socket.gethostname().lower():
            return True
    except:
        pass

    return False

# Log the environment
if is_running_on_pythonanywhere():
    print("Running on PythonAnywhere environment")
else:
    print("Running on local environment")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('wsgi')

# Set environment variables for optimization
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
os.environ['PYTHONOPTIMIZE'] = '1'  # Enable Python optimization
os.environ['USE_ULTRA_LAZY_LOADING'] = 'True'  # Enable ultra lazy loading
os.environ['PYTHONANYWHERE_OPTIMIZATION'] = 'True'  # Enable PythonAnywhere optimization

# Set MySQL password - replace with your actual MySQL password
# This should be set in the PythonAnywhere web app configuration
# Go to the Web tab, click on your web app, then click on "Environment variables"
# Add MYSQL_PASSWORD as a key and your MySQL password as the value
if 'MYSQL_PASSWORD' not in os.environ:
    # Check if we're on PythonAnywhere
    if 'PYTHONANYWHERE_SITE' in os.environ:
        # We're on PythonAnywhere, so we need to set the password
        # Using the actual MySQL password
        logger.info("Setting MySQL password from configuration")
        os.environ['MYSQL_PASSWORD'] = '@Verg@25!'  # Actual MySQL password
    else:
        # We're not on PythonAnywhere, so we can use an empty password for local development
        os.environ['MYSQL_PASSWORD'] = ''
os.environ['SQLITE_BUSY_TIMEOUT'] = '60000'  # Set SQLite busy timeout to 60 seconds

logger.info("Starting WSGI application with optimizations")

# Import the Django WSGI application
from django.core.wsgi import get_wsgi_application

# Create the application
application = get_wsgi_application()

# Initialize the database
try:
    logger.info("Initializing database...")

    # Import the database initialization module
    try:
        # First try absolute import
        from translation_app.db_init import ensure_database_ready, initialize_database_in_background
        logger.info("Successfully imported db_init module using absolute import")
    except ImportError:
        # If that fails, try relative import
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from translation_app.db_init import ensure_database_ready, initialize_database_in_background
        logger.info("Successfully imported db_init module using relative import")

    # Start database initialization in the background
    # This allows the application to start serving requests while the database is being initialized
    initialize_database_in_background()

    logger.info("Database initialization started in background")
except Exception as e:
    logger.error(f"Error initializing database: {str(e)}")
    import traceback
    logger.error(f"Traceback: {traceback.format_exc()}")

logger.info("WSGI application initialized")
