/* Custom CSS for Jazzmin admin theme */

/* Ensure sidebar doesn't overlap content */
.main-sidebar {
    z-index: 1000;
    width: 250px;
}

.content-wrapper {
    margin-left: 250px !important;
    transition: none !important;
}

/* Hide the burger menu icon */
.nav-item .nav-link[data-widget="pushmenu"] {
    display: none !important;
}

/* Fix header alignment */
.main-header {
    margin-left: 250px !important;
    padding-left: 0 !important;
    transition: none !important;
}

/* Ensure the navbar is properly aligned */
.navbar {
    padding-left: 1rem !important;
}

/* Disable sidebar toggle functionality */
body:not(.sidebar-mini-md):not(.sidebar-mini-xs):not(.layout-top-nav) .content-wrapper,
body:not(.sidebar-mini-md):not(.sidebar-mini-xs):not(.layout-top-nav) .main-footer,
body:not(.sidebar-mini-md):not(.sidebar-mini-xs):not(.layout-top-nav) .main-header {
    transition: none !important;
}

/* Prevent sidebar from collapsing */
body.sidebar-collapse .main-sidebar,
body.sidebar-collapse .main-sidebar::before {
    margin-left: 0 !important;
    width: 250px !important;
}

body.sidebar-collapse .content-wrapper,
body.sidebar-collapse .main-footer,
body.sidebar-collapse .main-header {
    margin-left: 250px !important;
}

/* Improve sidebar appearance */
.nav-sidebar .nav-item > .nav-link {
    padding: 0.75rem 1rem;
}

.nav-sidebar .nav-link p {
    font-size: 14px;
}

.nav-sidebar .nav-icon {
    margin-right: 0.5rem;
}

/* Ensure sidebar icons are visible */
.nav-sidebar .nav-link .nav-icon {
    color: #495057;
}

/* Improve active item visibility */
.nav-sidebar .nav-link.active {
    background-color: #007bff;
    color: #fff;
}

.nav-sidebar .nav-link.active .nav-icon {
    color: #fff;
}

/* Improve hover effects */
.nav-sidebar .nav-link:hover {
    background-color: #f8f9fa;
}

.nav-sidebar .nav-link.active:hover {
    background-color: #0069d9;
}

/* Ensure content has enough padding */
.content-wrapper > .content {
    padding: 1rem;
}

/* Improve form appearance */
.form-group {
    margin-bottom: 1rem;
}

/* Ensure tables are readable */
.table th {
    background-color: #f8f9fa;
}

/* Improve card appearance */
.card {
    box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
    margin-bottom: 1rem;
}

/* Hide Jazzmin version in footer */
.main-footer small,
.main-footer .small,
.main-footer .float-right {
    display: none !important;
}

/* Clean up footer appearance and hide version */
.main-footer {
    padding: 0.5rem;
    text-align: center;
    border-top: 1px solid #dee2e6;
    background-color: #fff;
    font-size: 0 !important;
}

/* Make copyright text visible again */
.main-footer strong {
    font-size: 14px !important;
}

/* Fix for small screens */
@media (max-width: 992px) {
    .main-sidebar {
        width: 250px;
        transform: none !important;
        display: block !important;
    }

    .content-wrapper {
        margin-left: 250px !important;
    }

    .main-header {
        margin-left: 250px !important;
    }

    /* Ensure the body doesn't shift when sidebar would normally toggle */
    body:not(.sidebar-mini-md):not(.sidebar-mini-xs):not(.layout-top-nav) .content-wrapper,
    body:not(.sidebar-mini-md):not(.sidebar-mini-xs):not(.layout-top-nav) .main-footer,
    body:not(.sidebar-mini-md):not(.sidebar-mini-xs):not(.layout-top-nav) .main-header {
        margin-left: 250px !important;
        transition: none !important;
    }
}
