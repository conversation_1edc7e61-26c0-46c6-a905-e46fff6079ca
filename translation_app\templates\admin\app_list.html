{% extends "admin/app_list.html" %}
{% load i18n %}

{% block content %}
<div class="app-translation_app module">
    <table>
        <caption>
            <a href="{% url 'admin:translation_dashboard' %}" class="section" title="{% trans 'Translation Management' %}">
                {% trans 'Translation Management' %}
            </a>
        </caption>
        <tbody>
            <tr>
                <th scope="row">
                    <a href="{% url 'admin:update_attention' %}">
                        <i class="fas fa-brain"></i> {% trans 'Update Attention' %}
                    </a>
                </th>
                <td><a href="{% url 'admin:update_attention' %}" class="addlink">{% trans 'Run' %}</a></td>
            </tr>
            <tr>
                <th scope="row">
                    <a href="{% url 'admin:update_static' %}">
                        <i class="fas fa-file-code"></i> {% trans 'Update Static Files' %}
                    </a>
                </th>
                <td><a href="{% url 'admin:update_static' %}" class="addlink">{% trans 'Run' %}</a></td>
            </tr>
            <tr>
                <th scope="row">
                    <a href="{% url 'admin:update_feedback' %}">
                        <i class="fas fa-comment-dots"></i> {% trans 'Process Feedback' %}
                    </a>
                </th>
                <td><a href="{% url 'admin:update_feedback' %}" class="addlink">{% trans 'Run' %}</a></td>
            </tr>
            <tr>
                <th scope="row">
                    <a href="{% url 'admin:update_translations' %}">
                        <i class="fas fa-language"></i> {% trans 'Update System' %}
                    </a>
                </th>
                <td><a href="{% url 'admin:update_translations' %}" class="addlink">{% trans 'Run' %}</a></td>
            </tr>
        </tbody>
    </table>
</div>

{{ block.super }}
{% endblock %}
