#!/usr/bin/env python
"""
Simple test script for translation API.
You can copy and paste these functions directly into the Python console.
"""

import requests
import json

# Configuration
SERVER_URL = "http://localhost:8000"
API_ENDPOINT = "/api/translate/"
RELOAD_ENDPOINT = "/api/reload_translations/"

def test_translation(source_text, source_lang, target_lang, expected_translation=""):
    """Test translation API."""
    print(f"\nTesting translation of '{source_text}' from {source_lang} to {target_lang}")
    
    try:
        # Prepare the request
        headers = {
            'Content-Type': 'application/json'
        }
        
        data = {
            'text': source_text,
            'source_lang': source_lang,
            'target_lang': target_lang
        }
        
        # Make the API request
        response = requests.post(
            f"{SERVER_URL}{API_ENDPOINT}",
            headers=headers,
            data=json.dumps(data)
        )
        
        # Parse the response
        result = response.json()
        print(f"Response: {json.dumps(result, indent=2)}")
        
        # Extract the translation
        if result.get('success') and 'result' in result:
            translation = result['result'].get('translation', '')
            source = result['result'].get('source', '')
            
            print(f"Extracted translation: {translation}")
            print(f"Source: {source}")
            
            # Check if the translation matches the expected result
            if expected_translation and translation == expected_translation:
                print("SUCCESS: Translation matches expected result!")
                return True
            elif expected_translation:
                print("FAILURE: Translation does not match expected result.")
                print(f"Expected: {expected_translation}")
                print(f"Got: {translation}")
                return False
            else:
                print("No expected translation provided. Manual verification required.")
                return True
        else:
            print(f"API request failed: {json.dumps(result, indent=2)}")
            return False
    except Exception as e:
        print(f"Error testing translation: {str(e)}")
        return False

def test_reload_translations():
    """Test reload translations API."""
    print("\nTesting reload translations API...")
    
    try:
        # Prepare the request
        headers = {
            'Content-Type': 'application/json'
        }
        
        # Make the API request
        response = requests.post(
            f"{SERVER_URL}{RELOAD_ENDPOINT}",
            headers=headers
        )
        
        # Parse the response
        result = response.json()
        print(f"Response: {json.dumps(result, indent=2)}")
        
        # Check if the response indicates success
        if result.get('success'):
            print("SUCCESS: Translations reloaded successfully!")
            return True
        else:
            print("FAILURE: Failed to reload translations.")
            return False
    except Exception as e:
        print(f"Error testing reload translations: {str(e)}")
        return False

def run_tests():
    """Run all tests."""
    print("Running translation tests...")
    
    # Test some basic translations
    test_translation("kumusta", "tgl", "ted")
    test_translation("salamat", "tgl", "ted")
    
    # Test the longer phrase that was having issues
    print("\nTesting the longer phrase that was having issues...")
    test_translation("Pumunta ka sa bahay kahapon", "tgl", "ted", "Mënangëy go diyo natëmëgëno")
    
    # Test the "baka" translation
    print("\nTesting 'baka' translation...")
    test_translation("baka", "tgl", "ted", "beengk")
    
    # Test reloading translations
    print("\nTesting reload translations API...")
    test_reload_translations()
    
    # Test translations again after reload
    print("\nTesting translations again after reload...")
    test_translation("Pumunta ka sa bahay kahapon", "tgl", "ted", "Mënangëy go diyo natëmëgëno")
    test_translation("baka", "tgl", "ted", "beengk")
    
    print("\nTests completed!")

# You can run this directly or copy and paste individual functions into the console
if __name__ == "__main__":
    run_tests()
