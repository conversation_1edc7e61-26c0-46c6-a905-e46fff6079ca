import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from translation_app.services import get_translation_service

# Get the translation service
translation_service = get_translation_service()

# Test the word extraction function
source_text = 'Ang ilang mga kababaihan, kapag sila ay nanga<PERSON>k, ay hindi umiiyak.'
translated_text = 'Uwëni do dumo libun, amuk mëg<PERSON> ro, ënda këm<PERSON> ro.'
source_lang = 'tgl'
target_lang = 'ted'

print(f"Extracting words from sentence:")
print(f"Source: {source_text}")
print(f"Target: {translated_text}")

extracted_pairs = translation_service.extract_words_from_sentence(
    source_text, translated_text, source_lang, target_lang
)

print(f"\nExtracted {len(extracted_pairs)} word pairs:")
for source_word, target_word in extracted_pairs:
    print(f"  {source_word} → {target_word}")

# Test translation of 'umiiyak'
print("\nTesting translation of 'umiiyak':")
result = translation_service.translate_text('umiiyak', source_lang, target_lang)
print(f"Translation: {result['translation']}")
print(f"Confidence: {result['confidence']}")
print(f"Source: {result['source']}")
