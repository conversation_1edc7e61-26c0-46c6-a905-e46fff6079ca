"""
Migration for AI integration models.
"""

from django.db import migrations, models
import django.db.models.deletion
from django.conf import settings


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('translation_app', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AITranslationReview',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('source_text', models.TextField()),
                ('ai_translation', models.TextField()),
                ('modified_translation', models.TextField(blank=True, null=True)),
                ('confidence', models.FloatField(default=0.0)),
                ('status', models.CharField(choices=[('pending', 'Pending Review'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('modified', 'Modified and Approved')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('ai_model', models.CharField(default='huggingface', max_length=100)),
                ('reviewed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('source_language', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ai_review_source', to='translation_app.language')),
                ('target_language', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ai_review_target', to='translation_app.language')),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['status'], name='ai_review_status_idx'), models.Index(fields=['source_language', 'target_language'], name='ai_review_lang_idx')],
            },
        ),
        migrations.CreateModel(
            name='AITranslationLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('source_text', models.TextField()),
                ('source_lang', models.CharField(max_length=10)),
                ('target_lang', models.CharField(max_length=10)),
                ('translation', models.TextField()),
                ('confidence', models.FloatField()),
                ('model', models.CharField(max_length=100)),
                ('status', models.CharField(max_length=20)),
                ('notes', models.TextField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['status'], name='ai_log_status_idx'), models.Index(fields=['source_lang', 'target_lang'], name='ai_log_lang_idx'), models.Index(fields=['timestamp'], name='ai_log_time_idx')],
            },
        ),
    ]
