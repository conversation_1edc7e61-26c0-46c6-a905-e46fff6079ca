"""
Automatic Training System for Translation Models

This module provides functionality to automatically trigger model training
based on new translations or scheduled intervals.
"""

import logging
import os
import time
import random
from datetime import datetime, timedelta
from django.utils import timezone
from django.conf import settings
from django.db.models import Count
from django.db import connection, OperationalError

from translation_app.models import ComprehensiveTranslation, Language
from translation_app.model_training import ModelTrainer
from translation_app.db_utils import with_db_retry, optimize_connection

# Configure logging
logger = logging.getLogger('auto_training')

# Initialize model trainer
model_trainer = ModelTrainer()

# Constants
MIN_TRANSLATIONS_FOR_TRAINING = 100  # Minimum number of new translations to trigger training
TRAINING_COOLDOWN_DAYS = 7  # Minimum days between training sessions

# Global flag to disable automatic training (used for debugging or during high load)
AUTOMATIC_TRAINING_ENABLED = False  # Set to False to disable automatic training from signals

# Lock file path to prevent multiple training processes
LOCK_FILE = os.path.join(settings.BASE_DIR, 'training.lock')


def get_last_training_date(source_lang=None, target_lang=None):
    """
    Get the date of the last training session for a language pair.

    Args:
        source_lang: Source language code (optional)
        target_lang: Target language code (optional)

    Returns:
        datetime: Date of last training, or None if no record exists
    """
    # Check for training record file
    training_dir = os.path.join(settings.BASE_DIR, 'training_records')
    os.makedirs(training_dir, exist_ok=True)

    if source_lang and target_lang:
        # Specific language pair
        record_file = os.path.join(training_dir, f'last_training_{source_lang}_{target_lang}.txt')
    else:
        # Any language pair
        record_file = os.path.join(training_dir, 'last_training_any.txt')

    if os.path.exists(record_file):
        with open(record_file, 'r') as f:
            date_str = f.read().strip()
            try:
                return datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                logger.error(f"Invalid date format in {record_file}")
                return None
    return None


def update_training_record(source_lang=None, target_lang=None):
    """
    Update the record of the last training session.

    Args:
        source_lang: Source language code (optional)
        target_lang: Target language code (optional)
    """
    training_dir = os.path.join(settings.BASE_DIR, 'training_records')
    os.makedirs(training_dir, exist_ok=True)

    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # Update specific language pair record
    if source_lang and target_lang:
        record_file = os.path.join(training_dir, f'last_training_{source_lang}_{target_lang}.txt')
        with open(record_file, 'w') as f:
            f.write(now)

    # Also update the "any" record
    record_file = os.path.join(training_dir, 'last_training_any.txt')
    with open(record_file, 'w') as f:
        f.write(now)


def get_language_pairs_with_sufficient_data(last_training=None):
    """
    Get language pairs that have sufficient new data for training.
    Uses database retry logic to handle locking issues.

    Args:
        last_training: Datetime of last training session (optional)

    Returns:
        list: List of (source_lang, target_lang) tuples
    """
    def _get_pairs():
        # Get all language pairs with translations
        pairs = ComprehensiveTranslation.objects.values(
            'source_language__code', 'target_language__code'
        ).annotate(
            count=Count('id')
        ).filter(count__gte=MIN_TRANSLATIONS_FOR_TRAINING)

        # Filter by last training date if provided
        if last_training:
            pairs = pairs.filter(created_at__gt=last_training)

        return [(pair['source_language__code'], pair['target_language__code']) for pair in pairs]

    # Use our retry mechanism to handle database locking
    try:
        return with_db_retry(_get_pairs)
    except Exception as e:
        logger.error(f"Error getting language pairs: {str(e)}")
        return []


def check_and_trigger_training():
    """
    Check if training should be triggered based on new translations.
    Uses a lock file to prevent multiple training processes.

    Returns:
        bool: True if training was triggered, False otherwise
    """
    # Check if automatic training is enabled
    if not AUTOMATIC_TRAINING_ENABLED:
        logger.info("Automatic training is disabled. Skipping training check.")
        return False

    # Check if another training process is running
    if os.path.exists(LOCK_FILE):
        # Check if the lock file is stale (older than 2 hours)
        lock_time = os.path.getmtime(LOCK_FILE)
        current_time = time.time()
        if current_time - lock_time < 7200:  # 2 hours in seconds
            logger.info("Another training process is running. Skipping.")
            return False
        else:
            logger.warning("Found stale lock file. Removing it.")
            try:
                os.remove(LOCK_FILE)
            except Exception as e:
                logger.error(f"Error removing stale lock file: {str(e)}")
                return False

    # Create lock file
    try:
        with open(LOCK_FILE, 'w') as f:
            f.write(f"Training started at {datetime.now().isoformat()}")
    except Exception as e:
        logger.error(f"Error creating lock file: {str(e)}")
        return False

    try:
        # Get last training date
        last_training = get_last_training_date()

        # If we trained recently, don't train again
        if last_training and (datetime.now() - last_training).days < TRAINING_COOLDOWN_DAYS:
            logger.info(f"Last training was {(datetime.now() - last_training).days} days ago. "
                      f"Waiting until cooldown period ({TRAINING_COOLDOWN_DAYS} days) is over.")
            return False

        # Get language pairs with sufficient data
        if last_training:
            pairs = get_language_pairs_with_sufficient_data(last_training)
        else:
            pairs = get_language_pairs_with_sufficient_data()

        if not pairs:
            logger.info("No language pairs with sufficient new data for training.")
            return False

        # Optimize database connection before training
        optimize_connection()

        # Trigger training for each pair
        success = False
        for source_lang, target_lang in pairs:
            logger.info(f"Triggering training for {source_lang} to {target_lang}")

            try:
                # Fine-tune model
                result = model_trainer.fine_tune_model(source_lang, target_lang)

                if result:
                    logger.info(f"Successfully trained model for {source_lang} to {target_lang}")
                    update_training_record(source_lang, target_lang)
                    success = True
                else:
                    logger.error(f"Failed to train model for {source_lang} to {target_lang}")
            except Exception as e:
                logger.exception(f"Error training model for {source_lang} to {target_lang}: {str(e)}")

        # Update the "any" record if any training succeeded
        if success:
            update_training_record()

        return success

    finally:
        # Always remove the lock file when done
        try:
            if os.path.exists(LOCK_FILE):
                os.remove(LOCK_FILE)
        except Exception as e:
            logger.error(f"Error removing lock file: {str(e)}")
            # Don't return False here, as the training might have succeeded


def run_scheduled_training(force=False):
    """
    Run scheduled training regardless of new data.
    Uses a lock file to prevent multiple training processes.

    Args:
        force: If True, run training even if automatic training is disabled

    Returns:
        bool: True if training was triggered, False otherwise
    """
    # Check if automatic training is enabled, unless forced
    if not AUTOMATIC_TRAINING_ENABLED and not force:
        logger.info("Automatic training is disabled. Skipping scheduled training.")
        return False

    # Check if another training process is running
    if os.path.exists(LOCK_FILE):
        # Check if the lock file is stale (older than 2 hours)
        lock_time = os.path.getmtime(LOCK_FILE)
        current_time = time.time()
        if current_time - lock_time < 7200:  # 2 hours in seconds
            logger.info("Another training process is running. Skipping scheduled training.")
            return False
        else:
            logger.warning("Found stale lock file. Removing it.")
            try:
                os.remove(LOCK_FILE)
            except Exception as e:
                logger.error(f"Error removing stale lock file: {str(e)}")
                return False

    # Create lock file
    try:
        with open(LOCK_FILE, 'w') as f:
            f.write(f"Scheduled training started at {datetime.now().isoformat()}")
    except Exception as e:
        logger.error(f"Error creating lock file: {str(e)}")
        return False

    try:
        # Get all language pairs using our retry mechanism
        def _get_pairs():
            return list(ComprehensiveTranslation.objects.values(
                'source_language__code', 'target_language__code'
            ).annotate(
                count=Count('id')
            ).filter(count__gte=MIN_TRANSLATIONS_FOR_TRAINING))

        try:
            pairs = with_db_retry(_get_pairs)
            pairs = [(pair['source_language__code'], pair['target_language__code']) for pair in pairs]
        except Exception as e:
            logger.error(f"Error getting language pairs: {str(e)}")
            return False

        if not pairs:
            logger.info("No language pairs with sufficient data for training.")
            return False

        # Optimize database connection before training
        optimize_connection()

        # Trigger training for each pair
        success = False
        for source_lang, target_lang in pairs:
            logger.info(f"Running scheduled training for {source_lang} to {target_lang}")

            try:
                # Fine-tune model
                result = model_trainer.fine_tune_model(source_lang, target_lang)

                if result:
                    logger.info(f"Successfully trained model for {source_lang} to {target_lang}")
                    update_training_record(source_lang, target_lang)
                    success = True
                else:
                    logger.error(f"Failed to train model for {source_lang} to {target_lang}")
            except Exception as e:
                logger.exception(f"Error training model for {source_lang} to {target_lang}: {str(e)}")

        # Update the "any" record if any training succeeded
        if success:
            update_training_record()

        return success

    finally:
        # Always remove the lock file when done
        try:
            if os.path.exists(LOCK_FILE):
                os.remove(LOCK_FILE)
        except Exception as e:
            logger.error(f"Error removing lock file: {str(e)}")
            # Don't return False here, as the training might have succeeded
