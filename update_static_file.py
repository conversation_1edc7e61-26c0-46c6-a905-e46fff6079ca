import os
import django
import json

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from translation_app.models import Language, ComprehensiveTranslation

def update_static_file():
    """Update the static translations file with database entries"""
    print("Updating static translations file...")
    
    try:
        # Get language objects
        tagalog = Language.objects.get(code='tgl')
        teduray = Language.objects.get(code='ted')
        
        # Get all translations
        tgl_to_ted_translations = ComprehensiveTranslation.objects.filter(
            source_language=tagalog, 
            target_language=teduray
        )
        
        ted_to_tgl_translations = ComprehensiveTranslation.objects.filter(
            source_language=teduray, 
            target_language=tagalog
        )
        
        # Create dictionaries for the JSON file
        tgl_to_ted_dict = {}
        ted_to_tgl_dict = {}
        
        # Process Tagalog to Teduray translations
        for trans in tgl_to_ted_translations:
            tgl_to_ted_dict[trans.base_word.lower()] = {
                'translation': trans.translation,
                'part_of_speech': trans.part_of_speech,
                'notes': trans.notes,
                'confidence': 0.95,
                'source': 'database'
            }
        
        # Process Teduray to Tagalog translations
        for trans in ted_to_tgl_translations:
            ted_to_tgl_dict[trans.base_word.lower()] = {
                'translation': trans.translation,
                'part_of_speech': trans.part_of_speech,
                'notes': trans.notes,
                'confidence': 0.95,
                'source': 'database'
            }
        
        # Add our known word pairs
        known_word_pairs = {
            'umiiyak': 'këmërew',
            'hindi': 'ënda',
            'nanganak': 'mëgënga',
            'kababaihan': 'libun',
            'ilang': 'uwëni',
            'sila': 'ro',
            'kapag': 'amuk'
        }
        
        # Add known word pairs to the dictionaries with high confidence
        for source, target in known_word_pairs.items():
            tgl_to_ted_dict[source] = {
                'translation': target,
                'part_of_speech': 'word',
                'notes': 'Known word pair',
                'confidence': 0.99,
                'source': 'known_pair'
            }
            
            ted_to_tgl_dict[target] = {
                'translation': source,
                'part_of_speech': 'word',
                'notes': 'Known word pair',
                'confidence': 0.99,
                'source': 'known_pair'
            }
        
        # Create the final dictionary
        translations_dict = {
            'tgl_to_ted': tgl_to_ted_dict,
            'ted_to_tgl': ted_to_tgl_dict
        }
        
        # Save to static file
        static_dir = os.path.join('static', 'translation_app', 'js')
        os.makedirs(static_dir, exist_ok=True)
        
        static_file_path = os.path.join(static_dir, 'all_translations.js')
        
        with open(static_file_path, 'w', encoding='utf-8') as f:
            f.write('const ALL_TRANSLATIONS = ')
            json.dump(translations_dict, f, ensure_ascii=False, indent=2)
            f.write(';')
        
        print(f"Updated static file with {len(tgl_to_ted_dict)} Tagalog to Teduray translations")
        print(f"Updated static file with {len(ted_to_tgl_dict)} Teduray to Tagalog translations")
        
        return True
    except Exception as e:
        print(f"Error updating static file: {str(e)}")
        return False

if __name__ == '__main__':
    update_static_file()
