"""
Management command to optimize the translation system.
This command runs all optimization tasks at once to avoid database locks.
"""

import os
import time
import logging
import sqlite3
from django.core.management.base import BaseCommand
from django.conf import settings
from django.db import connection
from django.core.cache import cache

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Optimize the translation system by running all optimization tasks at once'

    def add_arguments(self, parser):
        parser.add_argument(
            '--update-metrics',
            action='store_true',
            help='Update metrics',
        )
        parser.add_argument(
            '--update-static-file',
            action='store_true',
            help='Update static translations file',
        )
        parser.add_argument(
            '--skip-static-file',
            action='store_true',
            help='Skip static file updates to prioritize database operations',
        )
        parser.add_argument(
            '--load-translations',
            action='store_true',
            help='Load translations into memory',
        )
        parser.add_argument(
            '--skip-load-translations',
            action='store_true',
            help='Skip loading all translations into memory (useful when only processing improved translations)',
        )
        parser.add_argument(
            '--optimize-db',
            action='store_true',
            help='Optimize database',
        )
        parser.add_argument(
            '--process-improved-translations',
            action='store_true',
            help='Process any pending improved translations and update the static file',
        )
        parser.add_argument(
            '--enable-huggingface',
            action='store_true',
            help='Enable Hugging Face integration for translations',
        )
        parser.add_argument(
            '--enable-attention',
            action='store_true',
            help='Enable attention mechanism for translations',
        )
        parser.add_argument(
            '--use-bleu',
            action='store_true',
            help='Use BLEU score for translation quality evaluation',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='Run all optimization tasks',
        )
        parser.add_argument(
            '--enable-auto-processes',
            action='store_true',
            help='Enable automatic processes after optimization',
        )
        parser.add_argument(
            '--disable-auto-processes',
            action='store_true',
            help='Disable automatic processes after optimization',
        )

    def handle(self, *args, **options):
        self.stdout.write('Starting translation system optimization...')

        # Track start time
        start_time = time.time()

        # Determine which tasks to run
        run_all = options['all']
        run_metrics = options['update_metrics'] or run_all
        run_static_file = options['update_static_file'] or run_all
        run_translations = options['load_translations'] or run_all
        run_optimize_db = options['optimize_db'] or run_all
        run_process_improved = options['process_improved_translations'] or run_all
        enable_auto = options['enable_auto_processes']
        disable_auto = options['disable_auto_processes']
        skip_load_translations = options['skip_load_translations']
        skip_static_file = options['skip_static_file']
        enable_huggingface = options['enable_huggingface']
        enable_attention = options['enable_attention']
        use_bleu = options['use_bleu']

        # If no specific tasks are selected, run all
        if not any([run_metrics, run_static_file, run_translations, run_optimize_db, run_process_improved]):
            run_all = True
            run_metrics = True
            run_static_file = not skip_static_file  # Skip static file if requested
            run_translations = True
            run_optimize_db = True
            run_process_improved = True

        # First optimize the database
        if run_optimize_db:
            self.stdout.write('Optimizing database...')
            self.optimize_database()

        # Then update metrics
        if run_metrics:
            self.stdout.write('Updating metrics...')
            self.update_metrics()

        # Process improved translations if requested
        if run_process_improved:
            self.stdout.write('Processing improved translations...')
            self.process_improved_translations()

        # Then update static file (unless skipped)
        if run_static_file and not skip_static_file:
            self.stdout.write('Updating static translations file...')
            self.update_static_file()
        elif skip_static_file:
            self.stdout.write('Skipping static file update to prioritize database operations')

        # Finally load translations (unless skipped)
        if run_translations and not skip_load_translations:
            self.stdout.write('Loading translations...')
            # Store the use_bleu flag as an attribute so it can be accessed in load_translations
            self.use_bleu = use_bleu
            if use_bleu:
                self.stdout.write('Using BLEU score for translation quality evaluation')
            # Pass the Hugging Face and attention flags
            self.load_translations(enable_huggingface, enable_attention)
        elif skip_load_translations:
            self.stdout.write('Skipping full translation reload as requested')
            # Update only the specific translations in memory
            self.update_translations_in_memory()

        # Handle auto processes settings
        if enable_auto and disable_auto:
            self.stdout.write(self.style.WARNING(
                'Both --enable-auto-processes and --disable-auto-processes specified. No change made to auto processes setting.'
            ))
        elif enable_auto:
            self.enable_auto_processes()
        elif disable_auto:
            self.disable_auto_processes()

        # Report completion time
        elapsed_time = time.time() - start_time
        self.stdout.write(self.style.SUCCESS(
            f'Translation system optimization completed in {elapsed_time:.2f} seconds!'
        ))

    def optimize_database(self):
        """Apply database optimizations for MySQL or SQLite."""
        try:
            # Check if we're using MySQL
            is_mysql = 'mysql' in settings.DATABASES['default']['ENGINE']

            if is_mysql:
                # MySQL optimization
                self.stdout.write('Optimizing MySQL database...')

                # Close Django's connection first
                connection.close()

                # Reconnect
                connection.connect()

                # Get a cursor
                with connection.cursor() as cursor:
                    # Set session variables for better performance in a single command
                    cursor.execute("""
                        SET SESSION
                            wait_timeout = 600,
                            interactive_timeout = 600,
                            sql_mode = 'STRICT_TRANS_TABLES',
                            innodb_lock_wait_timeout = 50,
                            net_read_timeout = 60,
                            net_write_timeout = 60
                    """)

                    # Optimize tables
                    cursor.execute("SHOW TABLES;")
                    tables = [row[0] for row in cursor.fetchall()]

                    for table in tables:
                        self.stdout.write(f'Optimizing table {table}...')
                        try:
                            cursor.execute(f"OPTIMIZE TABLE `{table}`;")
                        except Exception as table_error:
                            self.stdout.write(self.style.WARNING(f'Error optimizing table {table}: {str(table_error)}'))

                self.stdout.write(self.style.SUCCESS('MySQL database optimized successfully'))
            else:
                # SQLite optimization
                self.stdout.write('Optimizing SQLite database...')

                # Close Django's connection first
                connection.close()

                # Connect directly to the database
                db_path = settings.DATABASES['default']['NAME']
                conn = sqlite3.connect(db_path, timeout=300)
                cursor = conn.cursor()

                # Apply optimizations
                cursor.execute("PRAGMA busy_timeout = 120000;")
                cursor.execute("PRAGMA journal_mode = DELETE;")
                cursor.execute("PRAGMA synchronous = NORMAL;")
                cursor.execute("PRAGMA temp_store = MEMORY;")
                cursor.execute("PRAGMA cache_size = 5000;")
                cursor.execute("PRAGMA page_size = 4096;")
                cursor.execute("PRAGMA locking_mode = EXCLUSIVE;")

                # Optimize and vacuum the database
                cursor.execute("PRAGMA optimize;")
                cursor.execute("VACUUM;")

                # Commit changes and close connection
                conn.commit()
                conn.close()

                self.stdout.write(self.style.SUCCESS('SQLite database optimized successfully'))

            return True
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error optimizing database: {str(e)}'))
            logger.error(f"Error optimizing database: {str(e)}")
            return False

    def update_metrics(self):
        """Update translation metrics."""
        try:
            from django.core.management import call_command
            # Use the --force flag to ensure metrics are updated even if DISABLE_AUTO_PROCESSES is True
            call_command('update_metrics', force=True)
            self.stdout.write(self.style.SUCCESS('Metrics updated successfully'))
            return True
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error updating metrics: {str(e)}'))
            logger.error(f"Error updating metrics: {str(e)}")
            return False

    def update_static_file(self):
        """Update static translations file."""
        try:
            from translation_app.utils.static_file_updater import update_static_translations_file
            result = update_static_translations_file(background=False, force=True)
            if result:
                self.stdout.write(self.style.SUCCESS('Static translations file updated successfully'))
            else:
                self.stdout.write(self.style.WARNING('Static translations file update may have failed'))
            return result
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error updating static translations file: {str(e)}'))
            logger.error(f"Error updating static translations file: {str(e)}")
            return False

    def load_translations(self, enable_huggingface=False, enable_attention=False):
        """
        Load translations into memory.

        Args:
            enable_huggingface: If True, enable Hugging Face integration for translations
            enable_attention: If True, enable attention mechanism for translations
        """
        # Get the use_bleu flag from options
        use_bleu = getattr(self, 'use_bleu', False)
        try:
            # Clear the cache first
            cache.clear()
            self.stdout.write('Cache cleared')

            # Get the translation service and force a reload
            from translation_app.services import get_translation_service
            translation_service = get_translation_service(skip_loading_for_admin=False)

            # Configure the service to use the specified features
            if enable_huggingface:
                self.stdout.write('Enabling Hugging Face integration for translations')
                # Set the appropriate flag in settings or service
                from django.conf import settings
                setattr(settings, 'HUGGINGFACE_ENABLED', True)

            if enable_attention:
                self.stdout.write('Enabling attention mechanism for translations')
                # Set the appropriate flag in settings or service
                from django.conf import settings
                setattr(settings, 'ATTENTION_MECHANISM_ENABLED', True)

            # Enable BLEU score if requested
            if use_bleu:
                self.stdout.write('Enabling BLEU score for translation quality evaluation')
                from django.conf import settings
                setattr(settings, 'USE_BLEU_SCORE', True)

            # Temporarily disable the auto-loading restriction
            from django.conf import settings
            original_setting = getattr(settings, 'DISABLE_AUTO_PROCESSES', True)
            setattr(settings, 'DISABLE_AUTO_PROCESSES', False)
            self.stdout.write('Temporarily enabling automatic processes for translation loading')

            # Reload translations with the new settings
            translation_service.reload_translations()

            # Restore the original setting
            setattr(settings, 'DISABLE_AUTO_PROCESSES', original_setting)
            self.stdout.write(f'Restored automatic processes setting to: {original_setting}')

            self.stdout.write(self.style.SUCCESS('Translations loaded successfully'))
            return True
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error loading translations: {str(e)}'))
            logger.error(f"Error loading translations: {str(e)}")
            return False

    def enable_auto_processes(self):
        """Enable automatic processes by updating the settings file."""
        try:
            import os
            from django.conf import settings

            # Get the settings file path
            settings_path = os.path.join(settings.BASE_DIR, 'config', 'settings.py')

            # Read the current settings file
            with open(settings_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Check if the setting exists
            if 'DISABLE_AUTO_PROCESSES = True' in content:
                # Replace the setting
                content = content.replace('DISABLE_AUTO_PROCESSES = True', 'DISABLE_AUTO_PROCESSES = False')

                # Write the updated content back to the file
                with open(settings_path, 'w', encoding='utf-8') as f:
                    f.write(content)

                self.stdout.write(self.style.SUCCESS('Automatic processes enabled'))

                # Update the runtime setting
                settings.DISABLE_AUTO_PROCESSES = False

                return True
            elif 'DISABLE_AUTO_PROCESSES = False' in content:
                self.stdout.write(self.style.SUCCESS('Automatic processes are already enabled'))
                return True
            else:
                self.stdout.write(self.style.WARNING('Could not find DISABLE_AUTO_PROCESSES setting in settings.py'))
                return False

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error enabling automatic processes: {str(e)}'))
            logger.error(f"Error enabling automatic processes: {str(e)}")
            return False

    def process_improved_translations(self):
        """
        Process any pending improved translations, feedback, ratings, and update the static file.
        This is designed to be run as a scheduled task to avoid database locks
        during normal operation.
        """
        try:
            from translation_app.models import (
                TranslationSuggestion,
                ComprehensiveTranslation,
                TranslationVersion,
                TranslationFeedback,
                TranslationRating
            )
            from translation_app.utils.incremental_updater import incrementally_update_static_file
            from django.utils import timezone
            from django.db.models import Q
            import os
            import glob
            import sys

            # First, process any pending feedback files
            self.stdout.write('Processing pending feedback files...')
            try:
                # Check if the new process_all_pending_feedback.py script exists
                if os.path.exists('process_all_pending_feedback.py'):
                    # Run the script directly
                    import subprocess
                    result = subprocess.run(
                        [sys.executable, 'process_all_pending_feedback.py'],
                        capture_output=True,
                        text=True
                    )

                    # Log the output
                    if result.stdout:
                        self.stdout.write(result.stdout)

                    # Log any errors
                    if result.stderr:
                        self.stdout.write(self.style.ERROR(f"Error processing pending feedback: {result.stderr}"))
                # Fall back to the old script if the new one doesn't exist
                elif os.path.exists('process_pending_feedback.py'):
                    # Run the script directly
                    import subprocess
                    result = subprocess.run(
                        [sys.executable, 'process_pending_feedback.py'],
                        capture_output=True,
                        text=True
                    )

                    # Log the output
                    if result.stdout:
                        self.stdout.write(result.stdout)

                    # Log any errors
                    if result.stderr:
                        self.stdout.write(self.style.ERROR(f"Error processing pending feedback: {result.stderr}"))
                else:
                    # Count pending feedback files
                    pending_files = glob.glob('data/pending_feedback/feedback_*.json')
                    if pending_files:
                        self.stdout.write(self.style.WARNING(
                            f"Found {len(pending_files)} pending feedback files but neither process_all_pending_feedback.py nor process_pending_feedback.py script found."
                        ))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error processing pending feedback files: {str(e)}"))

            # Get recent items (last 24 hours)
            cutoff_time = timezone.now() - timezone.timedelta(hours=24)

            # First, process any pending translation suggestions
            pending_suggestions = TranslationSuggestion.objects.filter(
                status='pending',
                created_at__gte=cutoff_time
            ).order_by('-created_at')

            suggestion_count = pending_suggestions.count()
            self.stdout.write(f'Found {suggestion_count} pending translation suggestions')

            for suggestion in pending_suggestions:
                try:
                    # Update the static file with this suggestion
                    success = incrementally_update_static_file(
                        suggestion.original_text,
                        suggestion.suggested_translation,
                        suggestion.source_language.code,
                        suggestion.target_language.code
                    )

                    if success:
                        # Mark as processed
                        suggestion.status = 'approved'
                        suggestion.save()
                        self.stdout.write(f'Processed suggestion: {suggestion.original_text} → {suggestion.suggested_translation}')
                    else:
                        self.stdout.write(self.style.WARNING(f'Failed to process suggestion: {suggestion.original_text}'))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Error processing suggestion {suggestion.id}: {str(e)}'))

            # Process recent feedback
            recent_feedback = TranslationFeedback.objects.filter(
                created_at__gte=cutoff_time,
                suggested_translation__isnull=False
            ).order_by('-created_at')

            feedback_count = recent_feedback.count()
            self.stdout.write(f'Found {feedback_count} recent feedback submissions')

            for feedback in recent_feedback:
                try:
                    # Update the static file with this feedback
                    success = incrementally_update_static_file(
                        feedback.original_text,
                        feedback.suggested_translation,
                        feedback.source_language.code,
                        feedback.target_language.code
                    )

                    if success:
                        self.stdout.write(f'Processed feedback: {feedback.original_text} → {feedback.suggested_translation}')
                    else:
                        self.stdout.write(self.style.WARNING(f'Failed to process feedback: {feedback.original_text}'))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Error processing feedback {feedback.id}: {str(e)}'))

            # Process recent high ratings
            recent_ratings = TranslationRating.objects.filter(
                created_at__gte=cutoff_time,
                rating__gte=4
            ).order_by('-created_at')

            rating_count = recent_ratings.count()
            self.stdout.write(f'Found {rating_count} recent high ratings')

            for rating in recent_ratings:
                try:
                    # Update the static file with this highly-rated translation
                    success = incrementally_update_static_file(
                        rating.comprehensive_translation.base_word,
                        rating.comprehensive_translation.translation,
                        rating.comprehensive_translation.source_language.code,
                        rating.comprehensive_translation.target_language.code
                    )

                    if success:
                        self.stdout.write(f'Processed high rating for: {rating.comprehensive_translation.base_word}')
                    else:
                        self.stdout.write(self.style.WARNING(f'Failed to process high rating for: {rating.comprehensive_translation.base_word}'))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Error processing rating {rating.id}: {str(e)}'))

            # Next, process recent translation versions
            recent_versions = TranslationVersion.objects.filter(
                created_at__gte=cutoff_time,
                is_active=True
            ).order_by('-created_at')

            version_count = recent_versions.count()
            self.stdout.write(f'Found {version_count} recent translation versions')

            for version in recent_versions:
                try:
                    # Update the static file with this version
                    success = incrementally_update_static_file(
                        version.comprehensive_translation.base_word,
                        version.translation,
                        version.comprehensive_translation.source_language.code,
                        version.comprehensive_translation.target_language.code
                    )

                    if success:
                        self.stdout.write(f'Processed version: {version.comprehensive_translation.base_word} → {version.translation}')
                    else:
                        self.stdout.write(self.style.WARNING(f'Failed to process version: {version.comprehensive_translation.base_word}'))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Error processing version {version.id}: {str(e)}'))

            # Finally, process recent translations
            recent_translations = ComprehensiveTranslation.objects.filter(
                updated_at__gte=cutoff_time
            ).order_by('-updated_at')

            translation_count = recent_translations.count()
            self.stdout.write(f'Found {translation_count} recently updated translations')

            for translation in recent_translations:
                try:
                    # Update the static file with this translation
                    success = incrementally_update_static_file(
                        translation.base_word,
                        translation.translation,
                        translation.source_language.code,
                        translation.target_language.code
                    )

                    if success:
                        self.stdout.write(f'Processed translation: {translation.base_word} → {translation.translation}')
                    else:
                        self.stdout.write(self.style.WARNING(f'Failed to process translation: {translation.base_word}'))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Error processing translation {translation.id}: {str(e)}'))

            total_processed = suggestion_count + feedback_count + rating_count + version_count + translation_count
            self.stdout.write(self.style.SUCCESS(f'Processed a total of {total_processed} items (suggestions, feedback, ratings, versions, translations)'))

            # Update metrics after processing all translations
            try:
                self.stdout.write('Updating metrics after processing translations...')
                from django.core.management import call_command
                # Use the --force flag to ensure metrics are updated even if DISABLE_AUTO_PROCESSES is True
                call_command('update_metrics', quiet=True, force=True)
                self.stdout.write(self.style.SUCCESS('Metrics updated successfully'))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'Error updating metrics: {str(e)}'))
                logger.error(f"Error updating metrics after processing translations: {str(e)}")

            return True

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error processing improved translations: {str(e)}'))
            logger.error(f"Error processing improved translations: {str(e)}")
            return False

    def update_translations_in_memory(self):
        """
        Update only the recently modified translations in memory without reloading everything.
        This is much more efficient than reloading all translations.
        """
        try:
            from translation_app.services import get_translation_service
            from django.utils import timezone

            # Get the translation service
            translation_service = get_translation_service(skip_loading_for_admin=False)

            # Get recent items (last 24 hours)
            cutoff_time = timezone.now() - timezone.timedelta(hours=24)

            # Get recent translations from the database
            from translation_app.models import (
                TranslationSuggestion,
                ComprehensiveTranslation,
                TranslationVersion,
                TranslationFeedback,
                TranslationRating
            )

            # Process recent suggestions
            recent_suggestions = TranslationSuggestion.objects.filter(
                status='approved',
                created_at__gte=cutoff_time
            )

            for suggestion in recent_suggestions:
                try:
                    translation_service.update_single_translation(
                        suggestion.original_text,
                        suggestion.suggested_translation,
                        suggestion.source_language.code,
                        suggestion.target_language.code
                    )
                    self.stdout.write(f'Updated in-memory translation: {suggestion.original_text} to {suggestion.suggested_translation}')
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Failed to process suggestion: {suggestion.original_text} - {str(e)}'))

            # Process recent translations
            recent_translations = ComprehensiveTranslation.objects.filter(
                updated_at__gte=cutoff_time
            )

            for translation in recent_translations:
                try:
                    translation_service.update_single_translation(
                        translation.base_word,
                        translation.translation,
                        translation.source_language.code,
                        translation.target_language.code
                    )
                    self.stdout.write(f'Updated in-memory translation: {translation.base_word} to {translation.translation}')
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Failed to process translation: {translation.base_word} - {str(e)}'))

            # Process recent versions
            recent_versions = TranslationVersion.objects.filter(
                created_at__gte=cutoff_time,
                is_active=True
            )

            for version in recent_versions:
                try:
                    translation_service.update_single_translation(
                        version.comprehensive_translation.base_word,
                        version.translation,
                        version.comprehensive_translation.source_language.code,
                        version.comprehensive_translation.target_language.code
                    )
                    self.stdout.write(f'Updated in-memory translation version: {version.comprehensive_translation.base_word} to {version.translation}')
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Failed to process version: {version.comprehensive_translation.base_word} - {str(e)}'))

            self.stdout.write(self.style.SUCCESS(
                f'Updated {recent_suggestions.count() + recent_translations.count() + recent_versions.count()} translations in memory'
            ))
            return True
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error updating translations in memory: {str(e)}'))
            logger.error(f"Error updating translations in memory: {str(e)}")
            return False

    def disable_auto_processes(self):
        """Disable automatic processes by updating the settings file."""
        try:
            import os
            from django.conf import settings

            # Get the settings file path
            settings_path = os.path.join(settings.BASE_DIR, 'config', 'settings.py')

            # Read the current settings file
            with open(settings_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Check if the setting exists
            if 'DISABLE_AUTO_PROCESSES = False' in content:
                # Replace the setting
                content = content.replace('DISABLE_AUTO_PROCESSES = False', 'DISABLE_AUTO_PROCESSES = True')

                # Write the updated content back to the file
                with open(settings_path, 'w', encoding='utf-8') as f:
                    f.write(content)

                self.stdout.write(self.style.SUCCESS('Automatic processes disabled'))

                # Update the runtime setting
                settings.DISABLE_AUTO_PROCESSES = True

                return True
            elif 'DISABLE_AUTO_PROCESSES = True' in content:
                self.stdout.write(self.style.SUCCESS('Automatic processes are already disabled'))
                return True
            else:
                self.stdout.write(self.style.WARNING('Could not find DISABLE_AUTO_PROCESSES setting in settings.py'))
                return False

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error disabling automatic processes: {str(e)}'))
            logger.error(f"Error disabling automatic processes: {str(e)}")
            return False
