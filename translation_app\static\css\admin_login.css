/* Custom CSS for admin login page */

/* Hide the skip to main content link */
a[href="#content-start"], .skip-to-content-link {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    width: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    z-index: -9999 !important;
    pointer-events: none !important;
}

/* Fix for dark mode toggle */
html[data-theme="dark"] {
    --primary: #006837;
    --secondary: #8C5000;
    --accent: #FFC72C;
    --primary-fg: #fff;
    --body-fg: #e0e0e0;
    --body-bg: #121212;
    --header-color: #FFC72C;
    --header-branding-color: #FFC72C;
    --header-bg: #006837;
    --header-link-color: #fff;
}

/* Ensure the theme toggle is visible */
.theme-toggle {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Style adjustments for dark mode */
html[data-theme="dark"] body.login {
    background: linear-gradient(to bottom right, #004020, #1A2800);
}

html[data-theme="dark"] .login #container {
    background: #1e1e1e;
    color: #e0e0e0;
}

html[data-theme="dark"] .login .welcome-message {
    color: #FFC72C;
}

html[data-theme="dark"] input[type="text"],
html[data-theme="dark"] input[type="password"] {
    background-color: #333;
    color: #e0e0e0;
    border-color: #555;
}

html[data-theme="dark"] .login .submit-row input {
    background: #006837;
    border-color: #006837;
}

html[data-theme="dark"] .login .submit-row input:hover {
    background: #2E4600;
    border-color: #2E4600;
}
