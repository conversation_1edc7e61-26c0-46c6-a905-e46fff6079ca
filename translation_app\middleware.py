"""
Middleware for the translation app.
"""

import re
import time
import random
import logging
import threading
from django.conf import settings
from django.db import connection, OperationalError

# Store the last update time
last_metrics_update = 0
metrics_update_lock = threading.Lock()

# Thread local storage for request
_thread_locals = threading.local()

# Configure logging
logger = logging.getLogger(__name__)

def with_retry(func, max_retries=5, retry_delay=0.5, max_delay=30, jitter=True):
    """
    Execute a database function with retry on locking errors.
    Uses exponential backoff with jitter for more effective retries.
    Supports both MySQL and SQLite database backends.

    Args:
        func: Function to execute
        max_retries: Maximum number of retries
        retry_delay: Initial delay between retries in seconds
        max_delay: Maximum delay between retries in seconds
        jitter: Whether to add random jitter to delay times

    Returns:
        Result of the function
    """
    import random

    # Check if we're using MySQL
    is_mysql = 'mysql' in settings.DATABASES['default']['ENGINE']

    retries = 0
    while True:
        try:
            # Check if the connection is None and reopen if needed
            if connection.connection is None:
                logger.debug("Database connection is None, reopening...")
                connection.ensure_connection()

            # Execute the function
            result = func()

            # Return the result
            return result

        except Exception as e:
            # Check if this is a database operational error
            is_operational_error = isinstance(e, OperationalError)

            # If it's not an operational error, re-raise immediately
            if not is_operational_error:
                logger.error(f"Non-database error: {str(e)}")
                raise

            # For operational errors, check if it's a locking error
            error_str = str(e).lower()

            # Different error patterns for MySQL vs SQLite
            if is_mysql:
                # MySQL-specific error patterns
                is_lock_error = any(err in error_str for err in [
                    "lock wait timeout", "deadlock", "connection reset",
                    "server has gone away", "broken pipe", "connection refused",
                    "can't connect", "lost connection", "access denied",
                    "too many connections", "query execution was interrupted",
                    "no such table", "mysql server has gone away"
                ])
            else:
                # SQLite-specific error patterns
                is_lock_error = any(err in error_str for err in [
                    "locking protocol", "database is locked", "busy",
                    "timeout", "no such table", "disk i/o error", "closed database"
                ])

            if is_lock_error and retries < max_retries:
                retries += 1
                # Calculate delay with exponential backoff
                delay = min(retry_delay * (2 ** (retries - 1)), max_delay)

                # Add jitter to prevent retry storms (all processes retrying at the same time)
                if jitter:
                    delay = delay * (0.5 + random.random())

                logger.warning(f"Database error detected: '{error_str}', retrying ({retries}/{max_retries}) in {delay:.2f}s...")

                # For connection-related errors, ensure the connection is reopened
                connection_errors = ["closed database", "server has gone away", "lost connection",
                                    "connection reset", "broken pipe", "connection refused"]

                if any(err in error_str for err in connection_errors):
                    try:
                        # Force reconnection
                        connection.close()
                        connection.connect()
                        logger.debug("Reopened database connection after connection error")
                    except Exception as conn_error:
                        logger.error(f"Error reopening connection: {str(conn_error)}")

                time.sleep(delay)
            else:
                logger.error(f"Database error after {retries} retries: {str(e)}")
                raise

class DatabaseOptimizationMiddleware:
    """
    Middleware to optimize database connections and prevent locking issues.
    """

    def __init__(self, get_response):
        self.get_response = get_response
        logger.info("DatabaseOptimizationMiddleware initialized")

    def __call__(self, request):
        # Check if this is an admin request and if we should skip optimization
        is_admin_request = '/admin/' in request.path
        skip_admin_optimization = getattr(settings, 'SKIP_ADMIN_DB_OPTIMIZATION', True)

        # Only apply optimizations if not an admin request or if admin optimization is enabled
        if not is_admin_request or not skip_admin_optimization:
            self._optimize_connection()
        elif is_admin_request and skip_admin_optimization:
            logger.debug("Skipping database optimization for admin request")

        # Define a function to process the request with retry
        def process_request():
            return self.get_response(request)

        # Process the request with retry logic
        try:
            # Use our enhanced retry mechanism
            response = with_retry(
                process_request,
                max_retries=3,
                retry_delay=1.0,
                max_delay=10
            )
        except Exception as e:
            logger.error(f"Error processing request: {str(e)}")
            # Re-raise the exception after logging
            raise

        # We no longer close the connection here to prevent "Cannot operate on a closed database" errors
        # Django will handle connection management automatically

        return response

    def _optimize_connection(self):
        """Apply optimizations to the database connection."""
        try:
            # Check if we're using MySQL
            is_mysql = 'mysql' in settings.DATABASES['default']['ENGINE']

            # Only apply optimizations if needed
            if is_mysql:
                try:
                    # For MySQL, we'll use a single command with multiple settings
                    # to reduce the number of database roundtrips
                    with connection.cursor() as cursor:
                        # Set all MySQL session variables in a single command
                        cursor.execute("""
                            SET SESSION
                                wait_timeout = 600,
                                interactive_timeout = 600,
                                sql_mode = 'STRICT_TRANS_TABLES',
                                innodb_lock_wait_timeout = 50,
                                net_read_timeout = 60,
                                net_write_timeout = 60
                        """)
                        logger.debug("MySQL database connection optimized successfully")
                except Exception as mysql_error:
                    logger.error(f"MySQL optimization failed: {str(mysql_error)}")
            else:
                # For SQLite, apply SQLite-specific optimizations
                try:
                    with connection.cursor() as cursor:
                        # Set busy timeout to 120 seconds
                        cursor.execute("PRAGMA busy_timeout = 120000;")
                        # Set journal mode to DELETE
                        cursor.execute("PRAGMA journal_mode = DELETE;")
                        # Set synchronous mode to NORMAL
                        cursor.execute("PRAGMA synchronous = NORMAL;")
                        # Set temp store to MEMORY
                        cursor.execute("PRAGMA temp_store = MEMORY;")
                        # Set cache size
                        cursor.execute("PRAGMA cache_size = 5000;")
                        # Set locking mode
                        cursor.execute("PRAGMA locking_mode = EXCLUSIVE;")
                        # Set page size
                        cursor.execute("PRAGMA page_size = 4096;")

                        logger.debug("SQLite database connection optimized successfully")
                except Exception as sqlite_error:
                    logger.debug(f"SQLite optimization failed: {str(sqlite_error)}")

        except Exception as e:
            logger.error(f"Error optimizing database connection: {str(e)}")

class MetricsUpdateMiddleware:
    """
    Middleware to periodically update metrics.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        global last_metrics_update

        # First check if automatic processes are disabled
        auto_processes_disabled = getattr(settings, 'DISABLE_AUTO_PROCESSES', False)
        if auto_processes_disabled:
            logger.debug("Automatic metrics updates are disabled by DISABLE_AUTO_PROCESSES setting")
            return self.get_response(request)

        # Check if this is an admin request and if we should skip metrics updates
        is_admin_request = '/admin/' in request.path
        skip_admin_metrics = getattr(settings, 'SKIP_ADMIN_METRICS_UPDATE', True)

        # Only update metrics if not an admin request or if admin metrics updates are enabled
        if not is_admin_request or not skip_admin_metrics:
            # Check if it's time to update metrics
            current_time = time.time()
            update_interval = getattr(settings, 'METRICS_UPDATE_INTERVAL', 3600)  # Default: 1 hour

            # Only check for admin users to avoid too many checks
            if request.user.is_staff and (current_time - last_metrics_update) > update_interval:
                # Use a lock to prevent multiple updates at the same time
                if metrics_update_lock.acquire(blocking=False):
                    try:
                        # Double-check after acquiring the lock
                        if (time.time() - last_metrics_update) > update_interval:
                            # Log that we're deferring metrics update to the scheduled task
                            logger.info("Deferring metrics update to scheduled task")
                            last_metrics_update = time.time()
                    finally:
                        metrics_update_lock.release()
        elif is_admin_request and skip_admin_metrics:
            logger.debug("Skipping metrics update for admin request")

        response = self.get_response(request)
        return response

    # The _update_metrics method has been removed as metrics updates are now
    # handled exclusively by the scheduled tasks to avoid database locks


class RequestMiddleware:
    """
    Middleware to store the request in thread locals.
    This allows other parts of the application to access the current request.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Store the request in thread locals
        _thread_locals.request = request

        # Process the request
        response = self.get_response(request)

        # Clean up
        if hasattr(_thread_locals, 'request'):
            del _thread_locals.request

        return response


class RemoveSkipLinkMiddleware:
    """
    Middleware to remove the 'Skip to main content' link from admin pages.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)

        # Only process HTML responses
        content_type = response.get('Content-Type', '')
        if not content_type or 'text/html' not in content_type:
            return response

        # Only process admin pages
        if '/admin/' not in request.path:
            return response

        # Convert response content to string
        try:
            content = response.content.decode('utf-8')

            # Remove the skip link
            content = re.sub(
                r'<a[^>]*href="#content-start"[^>]*>.*?Skip to main content.*?</a>',
                '',
                content,
                flags=re.DOTALL
            )

            # Remove any span with skip-to-content-link class
            content = re.sub(
                r'<span[^>]*class="skip-to-content-link"[^>]*>.*?Skip to main content.*?</span>',
                '',
                content,
                flags=re.DOTALL
            )

            # Update the response content
            response.content = content.encode('utf-8')
        except (UnicodeDecodeError, AttributeError):
            # Skip processing if we can't decode the content
            pass

        return response
