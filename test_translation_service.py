"""
<PERSON><PERSON><PERSON> to test the translation service.
This script tests how the translation service retrieves translations.
"""

import os
import sys
import django
import logging

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Import Django models and services
from translation_app.services import get_translation_service
from translation_app.models import Language

def test_translation_service():
    """Test the translation service."""
    try:
        # Get the translation service
        translation_service = get_translation_service()
        logger.info("Got translation service")
        
        # Get language objects
        tgl = Language.objects.get(code='tgl')
        ted = Language.objects.get(code='ted')
        logger.info(f"Got languages: {tgl.name} and {ted.name}")
        
        # Define the text to translate
        source_text = "umuwi ka na"
        
        # Test translation
        logger.info(f"Testing translation of '{source_text}'...")
        
        # Get translation
        translation = translation_service.translate(source_text, tgl.code, ted.code)
        logger.info(f"Translation result: '{translation}'")
        
        # Get translation details
        details = translation_service.get_translation_details(source_text, tgl.code, ted.code)
        logger.info(f"Translation details: {details}")
        
        # Test with period
        source_text_with_period = "umuwi ka na."
        logger.info(f"Testing translation of '{source_text_with_period}'...")
        
        # Get translation
        translation_with_period = translation_service.translate(source_text_with_period, tgl.code, ted.code)
        logger.info(f"Translation result: '{translation_with_period}'")
        
        # Get translation details
        details_with_period = translation_service.get_translation_details(source_text_with_period, tgl.code, ted.code)
        logger.info(f"Translation details: {details_with_period}")
        
        # Test direct lookup in the translations cache
        logger.info("Testing direct lookup in translations cache...")
        
        # Check if the service has translations loaded
        if hasattr(translation_service, 'translations'):
            # Check Tagalog to Teduray
            tgl_to_ted = translation_service.translations.get('tgl_to_ted', {})
            if source_text.lower() in tgl_to_ted:
                logger.info(f"Found in cache: {source_text} → {tgl_to_ted[source_text.lower()]}")
            else:
                logger.warning(f"Not found in cache: {source_text}")
            
            # Check with period
            if source_text_with_period.lower() in tgl_to_ted:
                logger.info(f"Found in cache: {source_text_with_period} → {tgl_to_ted[source_text_with_period.lower()]}")
            else:
                logger.warning(f"Not found in cache: {source_text_with_period}")
            
            # List all similar entries
            similar_entries = {k: v for k, v in tgl_to_ted.items() if 'umuwi' in k.lower()}
            if similar_entries:
                logger.info(f"Found {len(similar_entries)} similar entries:")
                for k, v in similar_entries.items():
                    logger.info(f"  - {k} → {v['translation']}")
            else:
                logger.warning("No similar entries found in cache")
        else:
            logger.warning("Translation service does not have translations loaded")
        
        # Force reload translations
        logger.info("Forcing reload of translations...")
        translation_service.reload_translations()
        logger.info("Translations reloaded")
        
        # Test translation again
        logger.info(f"Testing translation of '{source_text}' after reload...")
        
        # Get translation
        translation_after_reload = translation_service.translate(source_text, tgl.code, ted.code)
        logger.info(f"Translation result after reload: '{translation_after_reload}'")
        
        # Get translation details
        details_after_reload = translation_service.get_translation_details(source_text, tgl.code, ted.code)
        logger.info(f"Translation details after reload: {details_after_reload}")
        
        return True
    except Exception as e:
        logger.error(f"Error testing translation service: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("Starting translation service test script...")
    
    if test_translation_service():
        logger.info("Translation service test completed successfully!")
    else:
        logger.error("Translation service test failed.")
