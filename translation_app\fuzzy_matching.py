"""
Fuzzy Matching Module for Translation System

This module provides utilities for fuzzy matching and partial word recognition
to improve translation quality when exact matches aren't found.
"""

import re
import logging
import difflib
from typing import Dict, List, Tuple, Any, Optional
from collections import defaultdict

logger = logging.getLogger(__name__)

class FuzzyMatcher:
    """
    Class for implementing fuzzy matching and partial word recognition in translations.
    """

    def __init__(self):
        """Initialize the fuzzy matcher"""
        self.word_cache = {
            'tgl_to_ted': {},  # Tagalog to Teduray word cache
            'ted_to_tgl': {}   # Teduray to Tagalog word cache
        }
        self.similarity_threshold = 0.7  # Default similarity threshold

    def initialize_with_dictionary(self, dictionary: Dict[str, Dict[str, Any]], direction: str):
        """
        Initialize the fuzzy matcher with a dictionary of translations.

        Args:
            dictionary: Dictionary of translations
            direction: Translation direction ('tgl_to_ted' or 'ted_to_tgl')
        """
        self.word_cache[direction] = dictionary
        logger.info(f"Initialized fuzzy matcher with {len(dictionary)} words for {direction}")

    def find_best_match(self, word: str, direction: str) -> <PERSON><PERSON>[Optional[str], Optional[Dict[str, Any]], float]:
        """
        Find the best match for a word in the dictionary using fuzzy matching.

        Args:
            word: Word to match
            direction: Translation direction ('tgl_to_ted' or 'ted_to_tgl')

        Returns:
            Tuple of (matched_word, word_info, similarity_score)
        """
        if not word or not direction in self.word_cache:
            return None, None, 0.0

        # Normalize the word
        word = word.lower().strip()

        # Check for exact match first
        if word in self.word_cache[direction]:
            return word, self.word_cache[direction][word], 1.0

        # If no exact match, try fuzzy matching
        best_match = None
        best_score = 0.0
        best_info = None

        # First try prefix matching (faster)
        prefix = word[:3] if len(word) >= 3 else word
        candidates = [k for k in self.word_cache[direction].keys() if k.startswith(prefix)]

        # Also try suffix matching for better results with different word forms
        suffix = word[-3:] if len(word) >= 3 else word
        suffix_candidates = [k for k in self.word_cache[direction].keys() if k.endswith(suffix)]
        candidates.extend([c for c in suffix_candidates if c not in candidates])

        # Also try substring matching for better results with compound words
        if len(word) >= 5:
            substring = word[1:-1]  # Middle part of the word
            substring_candidates = [k for k in self.word_cache[direction].keys() if substring in k]
            candidates.extend([c for c in substring_candidates if c not in candidates])

        # If no matches yet, try looking for words that contain this word
        if not candidates and len(word) >= 4:
            containing_candidates = [k for k in self.word_cache[direction].keys() if word in k]
            candidates.extend(containing_candidates[:100])  # Limit to 100 for performance

        # If still no matches, use all words
        if not candidates:
            # Limit to 1000 words for performance
            candidates = list(self.word_cache[direction].keys())[:1000]

        # For single words, use a lower threshold to increase chances of finding a match
        local_threshold = self.similarity_threshold
        if len(word.split()) == 1 and len(word) >= 4:
            local_threshold = 0.6  # Lower threshold for single words

        for candidate in candidates:
            # Skip very different length words for efficiency, but be more lenient for single words
            max_length_diff = max(3, len(word) // 2)
            if len(word.split()) == 1:
                max_length_diff = max(5, len(word) // 1.5)  # More lenient for single words

            if abs(len(candidate) - len(word)) > max_length_diff:
                continue

            # Calculate similarity using SequenceMatcher
            similarity = difflib.SequenceMatcher(None, word, candidate).ratio()

            # Boost similarity for words that share the same root or prefix
            if word[:3] == candidate[:3] and len(word) >= 4 and len(candidate) >= 4:
                similarity += 0.1  # Boost for same prefix

            # Boost similarity for words that are contained within each other
            if word in candidate or candidate in word:
                similarity += 0.1  # Boost for substring relationship

            # Cap similarity at 0.99 to reserve 1.0 for exact matches
            similarity = min(0.99, similarity)

            if similarity > best_score:
                best_score = similarity
                best_match = candidate
                best_info = self.word_cache[direction][candidate]

        # Only return matches above threshold
        if best_score >= local_threshold:
            return best_match, best_info, best_score

        return None, None, 0.0

    def tokenize_text(self, text: str) -> List[str]:
        """
        Tokenize text into words, preserving important punctuation.

        Args:
            text: Text to tokenize

        Returns:
            List of tokens
        """
        # Preserve sentence-ending punctuation
        text = re.sub(r'([.!?])', r' \1 ', text)

        # Split by whitespace
        tokens = text.split()

        return tokens

    def translate_with_fuzzy_matching(self, text: str, direction: str) -> Dict[str, Any]:
        """
        Translate text using fuzzy matching for unknown words.

        Args:
            text: Text to translate
            direction: Translation direction ('tgl_to_ted' or 'ted_to_tgl')

        Returns:
            Dictionary with translation information
        """
        if not text or not direction in self.word_cache:
            return {
                'original': text,
                'translation': text,
                'confidence': 0.0,
                'source': 'fuzzy_matching_failed',
                'word_by_word': []
            }

        # Tokenize the text
        tokens = self.tokenize_text(text)

        # Translate each token
        translated_tokens = []
        word_by_word = []
        confidence_sum = 0.0
        confidence_count = 0

        for token in tokens:
            # Skip punctuation tokens
            if token in '.!?,:;':
                translated_tokens.append(token)
                continue

            # Try to find a match
            matched_word, word_info, similarity = self.find_best_match(token, direction)

            if matched_word and word_info:
                # We found a match
                translation = word_info['translation']
                confidence = similarity * word_info.get('confidence', 0.9)
                source = f"fuzzy_match_{word_info.get('source', 'unknown')}"

                translated_tokens.append(translation)
                word_by_word.append({
                    'original': token,
                    'translation': translation,
                    'confidence': confidence,
                    'source': source,
                    'part_of_speech': word_info.get('part_of_speech', ''),
                    'notes': f"Matched with '{matched_word}' (similarity: {similarity:.2f})"
                })

                confidence_sum += confidence
                confidence_count += 1
            else:
                # No match found, use original token
                translated_tokens.append(token)
                word_by_word.append({
                    'original': token,
                    'translation': token,
                    'confidence': 0.0,
                    'source': 'unknown',
                    'part_of_speech': '',
                    'notes': 'No match found'
                })

        # Calculate average confidence
        avg_confidence = confidence_sum / max(confidence_count, 1)

        # Join the translated tokens
        translation = ' '.join(translated_tokens)

        # Clean up spacing around punctuation
        translation = re.sub(r'\s+([.!?,;:])', r'\1', translation)

        return {
            'original': text,
            'translation': translation,
            'confidence': avg_confidence,
            'source': 'fuzzy_matching',
            'word_by_word': word_by_word
        }

    def extract_words_from_phrase(self, phrase: str, direction: str) -> Dict[str, Dict[str, Any]]:
        """
        Extract known words from a longer phrase to build a translation dictionary.

        Args:
            phrase: Phrase to analyze
            direction: Translation direction ('tgl_to_ted' or 'ted_to_tgl')

        Returns:
            Dictionary of {word: translation_info}
        """
        if not phrase or not direction in self.word_cache:
            return {}

        # Tokenize the phrase
        tokens = self.tokenize_text(phrase.lower())

        # Extract known words
        extracted_words = {}

        for token in tokens:
            # Skip very short tokens and punctuation
            if len(token) <= 2 or token in '.!?,:;':
                continue

            # Check for exact match first
            if token in self.word_cache[direction]:
                extracted_words[token] = self.word_cache[direction][token]
            else:
                # Try fuzzy matching
                matched_word, word_info, similarity = self.find_best_match(token, direction)
                if matched_word and word_info and similarity >= 0.8:  # Higher threshold for extraction
                    extracted_words[token] = word_info

        return extracted_words

    def learn_from_phrase(self, source_phrase: str, target_phrase: str, direction: str) -> Dict[str, Any]:
        """
        Learn word mappings from a phrase pair.

        Args:
            source_phrase: Source phrase
            target_phrase: Target phrase
            direction: Translation direction ('tgl_to_ted' or 'ted_to_tgl')

        Returns:
            Dictionary with learning results
        """
        if not source_phrase or not target_phrase or not direction in self.word_cache:
            return {'learned': 0}

        # Tokenize the phrases
        source_tokens = self.tokenize_text(source_phrase.lower())
        target_tokens = self.tokenize_text(target_phrase.lower())

        # Skip if token counts are very different
        if abs(len(source_tokens) - len(target_tokens)) > max(len(source_tokens), len(target_tokens)) / 2:
            return {'learned': 0}

        # Learn one-to-one mappings for tokens of similar length
        learned_count = 0
        min_len = min(len(source_tokens), len(target_tokens))

        for i in range(min_len):
            source_token = source_tokens[i]
            target_token = target_tokens[i]

            # Skip very short tokens and punctuation
            if len(source_token) <= 2 or len(target_token) <= 2 or source_token in '.!?,:;' or target_token in '.!?,:;':
                continue

            # Skip if source token already has a translation with high confidence
            if source_token in self.word_cache[direction] and self.word_cache[direction][source_token].get('confidence', 0) > 0.8:
                continue

            # Add or update the translation
            self.word_cache[direction][source_token] = {
                'translation': target_token,
                'confidence': 0.7,  # Medium confidence for learned mappings
                'source': 'learned_from_phrase',
                'part_of_speech': '',
                'notes': f'Learned from phrase: "{source_phrase}" -> "{target_phrase}"'
            }

            learned_count += 1

        return {'learned': learned_count}

# Singleton instance
fuzzy_matcher = FuzzyMatcher()
