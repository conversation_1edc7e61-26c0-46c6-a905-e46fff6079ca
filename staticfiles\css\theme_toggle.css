/* Theme toggle styles */
.theme-toggle {
    display: inline-block !important;
    margin-left: 10px;
}

.theme-toggle button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    margin: 0 2px;
    border-radius: 4px;
    color: var(--header-link-color);
}

.theme-toggle button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.theme-toggle button[aria-pressed="true"] {
    background-color: rgba(255, 255, 255, 0.2);
}

.theme-toggle .theme-icon {
    width: 16px;
    height: 16px;
    fill: currentColor;
}

/* Dark mode styles */
html[data-theme="dark"] {
    --primary: #006837;
    --secondary: #8C5000;
    --accent: #FFC72C;
    --primary-fg: #fff;
    --body-fg: #e0e0e0;
    --body-bg: #121212;
    --header-color: #FFC72C;
    --header-branding-color: #FFC72C;
    --header-bg: #006837;
    --header-link-color: #fff;
    --breadcrumbs-fg: #e0e0e0;
    --breadcrumbs-link-fg: #e0e0e0;
    --breadcrumbs-bg: #333;
    --link-fg: #81d4fa;
    --link-hover-color: #4fc3f7;
    --link-selected-fg: #4fc3f7;
    --hairline-color: #333;
    --border-color: #333;
    --error-fg: #e57373;
    --message-success-bg: #43a047;
    --message-warning-bg: #ef6c00;
    --message-error-bg: #c62828;
    --card-bg: #1e1e1e;
    --selected-bg: #264f36;
    --selected-row: #264f36;
    --close-button-bg: #333;
    --close-button-hover-bg: #666;
}

/* Light mode styles */
html[data-theme="light"] {
    --primary: #006837;
    --secondary: #8C5000;
    --accent: #FFC72C;
    --primary-fg: #fff;
    --body-fg: #333;
    --body-bg: #F5F0E1;
    --header-color: #FFC72C;
    --header-branding-color: #FFC72C;
    --header-bg: #006837;
    --header-link-color: #fff;
}
