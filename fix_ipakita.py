"""
<PERSON><PERSON>t to fix the translation of 'Ipaki<PERSON>'.
This script adds the correct translation for 'Ipaki<PERSON>' to the database and clears caches.
"""

import os
import sys
import django
import logging

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Import Django models and services
from translation_app.services import get_translation_service
from translation_app.models import Language, ComprehensiveTranslation, TranslationVersion
from django.core.cache import cache

def fix_ipakita():
    """Fix the translation of 'Ipakita'."""
    try:
        # Get language objects
        tgl = Language.objects.get(code='tgl')
        ted = Language.objects.get(code='ted')

        # Check if the translation exists
        existing_translation = ComprehensiveTranslation.objects.filter(
            base_word__iexact="Ipakita",
            source_language=tgl,
            target_language=ted
        ).first()

        if existing_translation:
            logger.info(f"Found existing translation: Ipakita → {existing_translation.translation}")

            # Check if it's correct
            if existing_translation.translation != "Fëgitonën":
                logger.info(f"Updating translation from '{existing_translation.translation}' to 'Fëgitonën'")

                # Create a new version with the old translation (inactive)
                TranslationVersion.objects.create(
                    comprehensive_translation=existing_translation,
                    translation=existing_translation.translation,
                    created_by='system',
                    confidence_score=0.8,
                    is_active=False,
                    notes='Archived before correction'
                )

                # Update the translation
                existing_translation.translation = "Fëgitonën"
                existing_translation.save()

                # Create a new active version
                TranslationVersion.objects.create(
                    comprehensive_translation=existing_translation,
                    translation="Fëgitonën",
                    created_by='system',
                    confidence_score=1.0,
                    is_active=True,
                    notes='Corrected translation'
                )

                logger.info("Translation updated successfully")
            else:
                logger.info("Translation is already correct")
        else:
            logger.info("Creating new translation: Ipakita → Fëgitonën")

            # Create new translation
            new_translation = ComprehensiveTranslation.objects.create(
                base_word="Ipakita",
                translation="Fëgitonën",
                source_language=tgl,
                target_language=ted,
                part_of_speech='verb',
                notes='Added manually to fix translation issue'
            )

            # Create a version
            TranslationVersion.objects.create(
                comprehensive_translation=new_translation,
                translation="Fëgitonën",
                created_by='system',
                confidence_score=1.0,
                is_active=True,
                notes='Initial version'
            )

            logger.info("New translation created successfully")

        # Clear cache
        cache.clear()
        logger.info("Cache cleared")

        # Force reload translations in the translation service
        translation_service = get_translation_service()
        translation_service.reload_translations()
        logger.info("Translations reloaded")

        # Test the translation
        result = translation_service.translate_text("Ipakita", tgl.code, ted.code)
        logger.info(f"Translation after fix: 'Ipakita' → '{result['translation']}' (source: {result['source']})")

        return True
    except Exception as e:
        logger.error(f"Error fixing translation: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("Starting Ipakita translation fix script...")

    if fix_ipakita():
        logger.info("Translation fixed successfully!")
    else:
        logger.error("Failed to fix translation.")
