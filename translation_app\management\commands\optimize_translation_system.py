"""
Management command to optimize the translation system using attention mechanisms and BLEU scores.
This script should be run manually to update translations.
"""

import os
import json
import logging
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db.models import Q
from django.conf import settings

from translation_app.models import (
    ComprehensiveTranslation,
    TranslationVersion,
    TranslationFeedback,
    Language,
    AttentionData,
    BleuScoreHistory
)
from translation_app.services import get_translation_service

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Optimize the translation system using attention mechanisms and BLEU scores'

    def add_arguments(self, parser):
        parser.add_argument(
            '--attention_focus',
            action='store_true',
            help='Focus on updating attention data',
        )
        parser.add_argument(
            '--update_metrics',
            action='store_true',
            help='Update translation metrics',
        )
        parser.add_argument(
            '--load_translations',
            action='store_true',
            help='Reload translations after optimization',
        )
        parser.add_argument(
            '--process_feedback',
            action='store_true',
            help='Process pending feedback',
        )
        parser.add_argument(
            '--limit',
            type=int,
            default=100,
            help='Limit the number of translations to process',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting translation system optimization...'))

        # Get options
        attention_focus = options.get('attention_focus', False)
        update_metrics = options.get('update_metrics', False)
        load_translations = options.get('load_translations', False)
        process_feedback = options.get('process_feedback', False)
        limit = options.get('limit', 100)

        # Process feedback if requested
        if process_feedback:
            self.process_pending_feedback()

        # Update attention data if requested
        if attention_focus:
            self.update_attention_data(limit)

        # Update metrics if requested
        if update_metrics:
            self.update_translation_metrics()

        # Reload translations if requested
        if load_translations:
            self.reload_translations()

        self.stdout.write(self.style.SUCCESS('Translation system optimization completed successfully!'))

    def update_attention_data(self, limit=100):
        """Update attention data for translations"""
        self.stdout.write('Updating attention data...')

        try:
            # Import attention mechanism
            from translation_app.attention_mechanism import attention_mechanism

            # Get translations without attention data
            translations = ComprehensiveTranslation.objects.filter(
                ~Q(base_word__exact='')  # Exclude empty strings
            ).order_by('-updated_at')[:limit]

            self.stdout.write(f'Processing {translations.count()} translations...')

            # Process each translation
            for translation in translations:
                try:
                    # Check if attention data already exists
                    attention_exists = AttentionData.objects.filter(
                        comprehensive_translation=translation
                    ).exists()

                    # Skip if attention data already exists
                    if attention_exists:
                        continue

                    # Compute attention data
                    attention_data = attention_mechanism.compute_attention(
                        translation.base_word,
                        translation.translation,
                        translation.source_language.code,
                        translation.target_language.code
                    )

                    # Create attention data record
                    AttentionData.objects.create(
                        comprehensive_translation=translation,
                        source_text=translation.base_word,
                        target_text=translation.translation,
                        source_language=translation.source_language,
                        target_language=translation.target_language,
                        attention_weights=json.dumps(attention_data['attention_weights'])
                    )

                    self.stdout.write(f'Created attention data for: {translation.base_word}')
                except Exception as e:
                    self.stderr.write(f'Error processing attention data for {translation.base_word}: {str(e)}')

            self.stdout.write(self.style.SUCCESS('Attention data update completed!'))
        except Exception as e:
            self.stderr.write(f'Error updating attention data: {str(e)}')

    def update_translation_metrics(self):
        """Update translation metrics including BLEU scores"""
        self.stdout.write('Updating translation metrics...')

        try:
            # Import BLEU score calculator
            from translation_app.bleu_score import bleu_calculator

            # Get language pairs
            language_pairs = []

            # Get all translations grouped by language pair
            translations = ComprehensiveTranslation.objects.values(
                'source_language', 'target_language'
            ).distinct()

            for pair in translations:
                source_lang = Language.objects.get(id=pair['source_language'])
                target_lang = Language.objects.get(id=pair['target_language'])
                language_pairs.append((source_lang, target_lang))

            self.stdout.write(f'Processing {len(language_pairs)} language pairs...')

            # Process each language pair
            for source_lang, target_lang in language_pairs:
                try:
                    # Calculate BLEU score
                    bleu_score = bleu_calculator.calculate_bleu_score(
                        source_lang.code,
                        target_lang.code
                    )

                    # Create BLEU score history record
                    BleuScoreHistory.objects.create(
                        date=timezone.now().date(),
                        source_language=source_lang,
                        target_language=target_lang,
                        bleu_score=bleu_score['bleu'],
                        bleu_1gram=bleu_score['bleu_1'],
                        bleu_2gram=bleu_score['bleu_2'],
                        bleu_3gram=bleu_score['bleu_3'],
                        bleu_4gram=bleu_score['bleu_4'],
                        f1_score=bleu_score.get('f1', 0.0),
                        reference_count=bleu_score.get('reference_count', 0),
                        test_set_size=bleu_score.get('test_set_size', 0),
                        notes=f'Calculated on {timezone.now().strftime("%Y-%m-%d %H:%M:%S")}'
                    )

                    self.stdout.write(f'Updated BLEU score for {source_lang.code} to {target_lang.code}: {bleu_score["bleu"]:.4f}')
                except Exception as e:
                    self.stderr.write(f'Error calculating BLEU score for {source_lang.code} to {target_lang.code}: {str(e)}')

            self.stdout.write(self.style.SUCCESS('Translation metrics update completed!'))
        except Exception as e:
            self.stderr.write(f'Error updating translation metrics: {str(e)}')

    def reload_translations(self):
        """
        Reload translations after optimization.
        This ensures that all translations are available in memory and in the frontend.
        """
        self.stdout.write('Reloading translations...')

        try:
            # Get translation service
            translation_service = get_translation_service()

            # Force a complete reload of translations
            translation_service.reload_translations(force_load=True)

            # Also update the static files to ensure frontend has latest translations
            try:
                from django.core.management import call_command

                # Call the export_translations command to update static files
                self.stdout.write('Updating static translation files...')
                call_command('update_static_translations')
                self.stdout.write('Static translation files updated successfully!')
            except Exception as e:
                self.stderr.write(f'Error updating static translation files: {str(e)}')

            self.stdout.write(self.style.SUCCESS('Translations reloaded successfully!'))
        except Exception as e:
            self.stderr.write(f'Error reloading translations: {str(e)}')

    def process_pending_feedback(self):
        """
        Process pending feedback and update static files and attention mechanism.
        This ensures that approved translations are immediately available in the frontend.
        """
        self.stdout.write('Processing pending feedback and updating static files...')

        try:
            # Get pending feedback
            pending_feedback = TranslationFeedback.objects.filter(processed=False)

            self.stdout.write(f'Processing {pending_feedback.count()} pending feedback items...')

            # Track if we need to update static files
            static_files_updated = False
            attention_data_updated = False

            # Process each feedback item
            for feedback in pending_feedback:
                try:
                    # Skip if no suggested translation
                    if not feedback.suggested_translation:
                        feedback.processed = True
                        feedback.save()
                        continue

                    # Find or create translation
                    translation, created = ComprehensiveTranslation.objects.get_or_create(
                        base_word=feedback.original_text,
                        source_language=feedback.source_language,
                        target_language=feedback.target_language,
                        defaults={
                            'translation': feedback.suggested_translation,
                            'part_of_speech': 'phrase' if ' ' in feedback.original_text else 'word',
                            'notes': f'Created from user feedback on {timezone.now().strftime("%Y-%m-%d")}'
                        }
                    )

                    if not created:
                        # Create a version record of the current translation
                        TranslationVersion.objects.create(
                            comprehensive_translation=translation,
                            translation=translation.translation,
                            created_by='system',
                            confidence_score=0.95,
                            is_active=False,
                            notes='Archived before feedback update'
                        )

                        # Update with the suggested translation
                        translation.translation = feedback.suggested_translation
                        translation.notes = (translation.notes or '') + f'\nUpdated from user feedback on {timezone.now().strftime("%Y-%m-%d")}'
                        translation.save()

                    # Create a new version for the suggested translation
                    TranslationVersion.objects.create(
                        comprehensive_translation=translation,
                        translation=feedback.suggested_translation,
                        created_by='feedback',
                        confidence_score=0.99,  # Higher confidence for admin-approved feedback
                        is_active=True,
                        notes=f'Created from admin-approved feedback on {timezone.now().strftime("%Y-%m-%d")}'
                    )

                    # Update static files incrementally
                    try:
                        from translation_app.utils.incremental_updater import incrementally_update_static_file

                        # Determine direction
                        source_lang = feedback.source_language.code
                        target_lang = feedback.target_language.code

                        # Update static file
                        success = incrementally_update_static_file(
                            feedback.original_text,
                            feedback.suggested_translation,
                            source_lang,
                            target_lang
                        )

                        if success:
                            self.stdout.write(f'Updated static file with translation: {feedback.original_text} → {feedback.suggested_translation}')
                            static_files_updated = True
                        else:
                            self.stderr.write(f'Failed to update static file for: {feedback.original_text}')
                    except Exception as e:
                        self.stderr.write(f'Error updating static file: {str(e)}')

                    # Update attention data
                    try:
                        from translation_app.attention_mechanism import attention_mechanism

                        # Compute attention data
                        attention_data = attention_mechanism.compute_attention(
                            feedback.original_text,
                            feedback.suggested_translation,
                            feedback.source_language.code,
                            feedback.target_language.code
                        )

                        # Create or update attention data record
                        AttentionData.objects.update_or_create(
                            comprehensive_translation=translation,
                            defaults={
                                'source_text': feedback.original_text,
                                'target_text': feedback.suggested_translation,
                                'source_language': feedback.source_language,
                                'target_language': feedback.target_language,
                                'attention_weights': json.dumps(attention_data['attention_weights'])
                            }
                        )

                        self.stdout.write(f'Updated attention data for: {feedback.original_text}')
                        attention_data_updated = True
                    except Exception as e:
                        self.stderr.write(f'Error updating attention data: {str(e)}')

                    # Mark feedback as processed
                    feedback.processed = True
                    feedback.save()

                    self.stdout.write(f'Processed feedback for: {feedback.original_text}')
                except Exception as e:
                    self.stderr.write(f'Error processing feedback for {feedback.original_text}: {str(e)}')

            # If we updated static files or attention data, reload translations
            if static_files_updated or attention_data_updated:
                self.stdout.write('Updates made to static files or attention data. Reloading translations...')
                self.reload_translations()

                # Also copy static files from staticfiles to static
                try:
                    self.stdout.write('Copying static files from staticfiles to static...')
                    import subprocess
                    result = subprocess.run(['python', 'copy_static_files.py'], capture_output=True, text=True)
                    if result.returncode == 0:
                        self.stdout.write(self.style.SUCCESS('Static files copied successfully!'))
                    else:
                        self.stderr.write(f'Error copying static files: {result.stderr}')
                except Exception as e:
                    self.stderr.write(f'Error running copy_static_files.py: {str(e)}')

            self.stdout.write(self.style.SUCCESS('Feedback processing and static file updates completed!'))
        except Exception as e:
            self.stderr.write(f'Error processing feedback: {str(e)}')
