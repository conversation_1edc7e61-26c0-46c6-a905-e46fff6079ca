/* Teduray/Mindanao Theme CSS */
:root {
    /* Teduray/Mindanao Theme Colors */
    --teduray-primary: #006837; /* Forest green representing Mindanao's lush forests */
    --teduray-secondary: #8C5000; /* Earth brown representing indigenous culture */
    --teduray-accent: #FFC72C; /* Golden yellow representing tribal ornaments */
    --teduray-dark: #2E4600; /* Dark green for contrast */
    --teduray-light: #F5F0E1; /* Natural off-white for backgrounds */
    --teduray-highlight: #FF9E1B; /* Orange highlight for important elements */
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--teduray-light);
    color: #333;
    margin: 0;
    padding: 0;
}

/* Mobile-first approach */
.container {
    padding: 0 15px;
    width: 100%;
    max-width: 100%;
}

.header {
    background-color: var(--teduray-primary);
    background-image: linear-gradient(135deg, var(--teduray-primary), var(--teduray-dark));
    color: white;
    padding: 20px 0;
    margin-bottom: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(to right, var(--teduray-accent), var(--teduray-highlight));
    z-index: 5;
}

.header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: repeating-linear-gradient(
        45deg,
        var(--teduray-accent),
        var(--teduray-accent) 10px,
        var(--teduray-secondary) 10px,
        var(--teduray-secondary) 20px
    );
    opacity: 0.8;
}

.header h1 {
    font-size: 1.8rem;
    margin-bottom: 8px;
    font-weight: 600;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
}

.header p {
    font-size: 1.1rem;
    opacity: 1;
    font-weight: 400;
    letter-spacing: 0.5px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.content-container {
    background-color: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.translation-result, .search-result {
    background-color: var(--teduray-light);
    border-radius: 12px;
    padding: 15px;
    margin-top: 15px;
    min-height: 80px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.word-breakdown {
    margin-top: 20px;
    border-top: 1px solid #dee2e6;
    padding-top: 15px;
}

.word-item, .phrase-item {
    background-color: white;
    border-radius: 10px;
    padding: 12px;
    margin-bottom: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-left: 4px solid var(--teduray-primary);
}

.confidence-high {
    border-left: 4px solid var(--teduray-primary);
}

.confidence-medium {
    border-left: 4px solid var(--teduray-accent);
}

.confidence-low {
    border-left: 4px solid #dc3545;
}

.footer {
    background-color: var(--teduray-dark);
    color: white;
    padding: 15px 0;
    margin-top: 20px;
    font-size: 0.9rem;
}

.btn-primary {
    background-color: var(--teduray-primary);
    border-color: var(--teduray-primary);
    border-radius: 50px;
    padding: 8px 20px;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
    background-color: var(--teduray-dark);
    border-color: var(--teduray-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.feedback-form {
    display: none;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

.philippines-colors {
    display: flex;
    height: 5px;
}

.color-blue {
    background-color: var(--teduray-primary);
    flex: 1;
}

.color-red {
    background-color: var(--teduray-secondary);
    flex: 1;
}

.color-yellow {
    background-color: var(--teduray-accent);
    flex: 1;
}

.nav-pills .nav-link.active {
    background-color: var(--teduray-primary);
    border-radius: 50px;
}

.nav-pills .nav-link {
    color: var(--teduray-primary);
    border-radius: 50px;
    margin: 0 2px;
    padding: 8px 12px;
    font-size: 0.9rem;
}

/* Mobile-specific styles */
@media (max-width: 767px) {
    .row {
        margin-left: -10px;
        margin-right: -10px;
    }

    .col-md-5, .col-md-2 {
        padding-left: 10px;
        padding-right: 10px;
    }

    /* Mobile layout for translation interface */
    .translator-layout {
        display: flex;
        flex-direction: column;
    }

    .source-container {
        order: 1;
        margin-bottom: 15px;
    }

    .translate-button-container {
        order: 2;
        margin: 10px 0;
        display: flex;
        justify-content: center;
    }

    .target-container {
        order: 3;
    }

    /* Larger buttons for mobile */
    #translateBtn {
        width: 100%;
        padding: 12px;
        font-size: 1rem;
    }

    /* Tribal pattern decorative elements */
    .tribal-pattern {
        height: 10px;
        background-image: repeating-linear-gradient(
            45deg,
            var(--teduray-accent),
            var(--teduray-accent) 10px,
            transparent 10px,
            transparent 20px
        );
        margin: 15px 0;
        border-radius: 5px;
    }

    /* Mobile search form */
    .search-form {
        flex-direction: column;
    }

    .search-form .form-control {
        margin-bottom: 10px;
    }

    /* Mobile metrics cards */
    .metrics-card {
        margin-bottom: 15px;
    }
}

/* Tablet and desktop styles */
@media (min-width: 768px) {
    .container {
        padding: 0 30px;
    }

    .header {
        padding: 35px 0 30px;
        margin-bottom: 30px;
        background-image: linear-gradient(135deg, var(--teduray-primary), var(--teduray-dark), var(--teduray-secondary));
        background-size: 200% 200%;
        animation: gradientAnimation 15s ease infinite;
    }

    @keyframes gradientAnimation {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    .header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
        letter-spacing: 1px;
    }

    .header p {
        font-size: 1.25rem;
        max-width: 80%;
        margin: 0 auto;
        position: relative;
        display: inline-block;
    }

    .header p::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 25%;
        right: 25%;
        height: 2px;
        background: var(--teduray-accent);
        border-radius: 2px;
    }

    .content-container {
        padding: 30px;
        border-radius: 20px;
    }

    /* Hide mobile-specific elements */
    .tribal-pattern {
        display: none;
    }

    /* Desktop search form */
    .search-form {
        display: flex;
        align-items: center;
    }

    .search-form .form-control {
        margin-right: 10px;
    }
}

/* Form styling improvements */
.form-control, .form-select {
    border-radius: 10px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 10px 15px;
}

.form-control:focus, .form-select:focus {
    border-color: var(--teduray-primary);
    box-shadow: 0 0 0 0.25rem rgba(0, 104, 55, 0.25);
}

/* Custom styling for the word breakdown */
.word-breakdown h5, .section-title {
    color: var(--teduray-secondary);
    font-weight: 600;
    border-bottom: 2px solid var(--teduray-accent);
    padding-bottom: 8px;
    display: inline-block;
}

/* Metrics Dashboard Styles */
.metrics-container {
    padding: 15px;
}

.metrics-card {
    background-color: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    padding: 20px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: transform 0.2s;
}

.metrics-card:hover {
    transform: translateY(-5px);
}

.metrics-header {
    border-bottom: 1px solid #eee;
    margin-bottom: 15px;
    padding-bottom: 10px;
    color: var(--teduray-primary);
}

.metrics-title {
    font-size: 1.5rem;
    margin: 0;
    color: var(--teduray-secondary);
}

.metrics-subtitle {
    color: #666;
    margin: 5px 0 0;
    font-size: 0.9rem;
}

.metrics-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--teduray-primary);
    margin-bottom: 5px;
}

.metrics-label {
    color: #666;
    font-size: 0.9rem;
}

.chart-container {
    background-color: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    padding: 20px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

/* Add Translation Form Styles */
.translation-form {
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background-color: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.form-row {
    margin-bottom: 15px;
}

.form-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: var(--teduray-secondary);
}

.form-row input[type="text"],
.form-row textarea,
.form-row select {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 10px;
}

.form-row textarea {
    min-height: 100px;
}

.help-text {
    font-size: 0.85rem;
    color: #666;
    margin-top: 5px;
}

.submit-row {
    margin-top: 20px;
    text-align: right;
}

/* Badge styling */
.badge {
    border-radius: 50px;
    padding: 5px 10px;
    font-weight: normal;
}

.badge-primary {
    background-color: var(--teduray-primary);
}

/* Alert styling */
.alert {
    border-radius: 10px;
}

.alert-success {
    background-color: rgba(0, 104, 55, 0.1);
    border-color: var(--teduray-primary);
    color: var(--teduray-primary);
}

.alert-info {
    background-color: rgba(255, 199, 44, 0.1);
    border-color: var(--teduray-accent);
    color: var(--teduray-secondary);
}

/* Dictionary and Phrases specific styles */
.search-container {
    margin-bottom: 30px;
}

.results-container {
    margin-top: 20px;
}

.example-item {
    background-color: var(--teduray-light);
    border-radius: 8px;
    padding: 10px;
    margin-top: 10px;
}

.phrase-source, .phrase-target {
    font-size: 1.1rem;
}

.phrase-source {
    font-weight: 600;
    color: var(--teduray-secondary);
}

.phrase-target {
    color: var(--teduray-primary);
}

/* Common categories section */
.common-categories {
    margin-top: 30px;
}

.category-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    overflow: hidden;
    transition: transform 0.2s;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.category-card:hover {
    transform: translateY(-5px);
}

.category-header {
    background-color: var(--teduray-primary);
    color: white;
    padding: 10px 15px;
}

.category-body {
    padding: 15px;
}
