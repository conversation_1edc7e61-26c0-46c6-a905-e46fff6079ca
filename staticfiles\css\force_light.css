/* Force light theme styles */

/* Set light theme variables */
:root {
    --primary: #006837 !important;
    --secondary: #8C5000 !important;
    --accent: #FFC72C !important;
    --primary-fg: #fff !important;
    --body-fg: #333 !important;
    --body-bg: #F5F0E1 !important;
    --header-color: #FFC72C !important;
    --header-branding-color: #FFC72C !important;
    --header-bg: #006837 !important;
    --header-link-color: #fff !important;
    --breadcrumbs-fg: #333 !important;
    --breadcrumbs-link-fg: #006837 !important;
    --breadcrumbs-bg: #e0e0e0 !important;
    --link-fg: #006837 !important;
    --link-hover-color: #004020 !important;
    --link-selected-fg: #004020 !important;
    --hairline-color: #e0e0e0 !important;
    --border-color: #ccc !important;
    --error-fg: #ba2121 !important;
    --message-success-bg: #dfd !important;
    --message-warning-bg: #ffc !important;
    --message-error-bg: #ffefef !important;
    --card-bg: #fff !important;
    --selected-bg: #e5f0e9 !important;
    --selected-row: #e5f0e9 !important;
    --close-button-bg: #888 !important;
    --close-button-hover-bg: #333 !important;
}

/* Force light theme on html element */
html, html[data-theme="dark"], html[data-theme="light"], html[data-theme="auto"] {
    --primary: #006837 !important;
    --secondary: #8C5000 !important;
    --accent: #FFC72C !important;
    --primary-fg: #fff !important;
    --body-fg: #333 !important;
    --body-bg: #F5F0E1 !important;
    --header-color: #FFC72C !important;
    --header-branding-color: #FFC72C !important;
    --header-bg: #006837 !important;
    --header-link-color: #fff !important;
}

/* Force light theme on body */
body {
    background-color: #F5F0E1 !important;
    color: #333 !important;
}

/* Force light theme on header */
#header {
    background-color: #006837 !important;
    color: #fff !important;
}

/* Force light theme on links */
a:link, a:visited {
    color: #006837 !important;
}

a:hover {
    color: #004020 !important;
}

/* Force light theme on buttons */
.button, input[type=submit], input[type=button], .submit-row input, a.button {
    background-color: #006837 !important;
    color: #fff !important;
}

.button:hover, input[type=submit]:hover, input[type=button]:hover {
    background-color: #004020 !important;
}

/* Force light theme on module headers */
.module h2, .module caption {
    background-color: #006837 !important;
    color: #fff !important;
}

/* Force light theme on form elements */
input[type=text], input[type=password], input[type=email], input[type=url], input[type=number], input[type=tel], textarea, select, .vTextField {
    background-color: #fff !important;
    color: #333 !important;
    border-color: #ccc !important;
}

/* Force light theme on breadcrumbs */
.breadcrumbs {
    background-color: #e0e0e0 !important;
    color: #333 !important;
}

.breadcrumbs a {
    color: #006837 !important;
}

/* Force light theme on messages */
.messagelist .success {
    background-color: #dfd !important;
    color: #333 !important;
}

.messagelist .warning {
    background-color: #ffc !important;
    color: #333 !important;
}

.messagelist .error {
    background-color: #ffefef !important;
    color: #333 !important;
}
