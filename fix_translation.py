import json
import re

# Open the file
with open('translation_app/static/translation_app/js/all_translations.js', 'r', encoding='utf-8') as f:
    content = f.read()

# Extract the JavaScript object part
start_index = content.find('const ALL_TRANSLATIONS = ')
if start_index != -1:
    # Find the start of the actual JSON object
    json_start = content.find('{', start_index)
    # Find the end of the JSON object (last closing brace before the semicolon)
    json_end = content.rfind('}', json_start) + 1

    if json_start != -1 and json_end != -1:
        # Extract the JSON part
        json_str = content[json_start:json_end]

        # Check if our phrase is in there
        if '"nagtatrabaho ako"' in json_str:
            print("Found the phrase in the JSON!")

            # First, find the tgl_to_ted section
            tgl_to_ted_start = json_str.find('"tgl_to_ted"')
            if tgl_to_ted_start != -1:
                # Look for "nagtatrabaho ako" within the tgl_to_ted section
                nagtatrabaho_start = json_str.find('"nagtatrabaho ako"', tgl_to_ted_start)
                if nagtatrabaho_start != -1:
                    # Find the translation field
                    translation_start = json_str.find('"translation"', nagtatrabaho_start)
                    if translation_start != -1:
                        # Find the actual translation value
                        value_start = json_str.find('"', translation_start + 14) + 1
                        value_end = json_str.find('"', value_start)

                        if value_start != 0 and value_end != -1:
                            current_translation = json_str[value_start:value_end]
                            print(f"Current translation: {current_translation}")

                            # Replace the translation
                            new_json_str = json_str[:value_start] + "Gëmalbëk ku" + json_str[value_end:]

                            # Replace in the original content
                            new_content = content[:json_start] + new_json_str + content[json_end:]

                            # Write back to the file
                            with open('translation_app/static/translation_app/js/all_translations.js', 'w', encoding='utf-8') as f:
                                f.write(new_content)

                            print("Translation updated successfully!")
                        else:
                            print("Could not find the translation value.")
                    else:
                        print("Could not find the translation field.")
                else:
                    print("Could not find 'nagtatrabaho ako' in the tgl_to_ted section.")
            else:
                print("Could not find the tgl_to_ted section.")
        else:
            print("Phrase 'nagtatrabaho ako' not found in the JSON part.")
    else:
        print("Could not find the JSON object boundaries.")
else:
    print("Could not find the ALL_TRANSLATIONS variable.")
