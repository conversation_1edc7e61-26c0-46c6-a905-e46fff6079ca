#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to process improved translations on PythonAnywhere.
This script is designed to be run as a scheduled task to avoid database locks.

Usage:
    python process_improved_translations.py

This script will:
1. Process any pending improved translations, feedback, and ratings
2. Update the static translations file with the new translations
3. Update metrics
4. Log the results

This script is optimized for PythonAnywhere and will not cause database locks.
"""

import os
import sys
import logging
import datetime
import django

# Set up logging
log_file = 'process_improved_translations.log'
logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main function to process improved translations."""
    start_time = datetime.datetime.now()
    logger.info(f"Starting improved translations processing at {start_time}")

    try:
        # Set up Django environment
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
        django.setup()

        # Import Django-specific modules after setup
        from django.core.management import call_command

        # Run the optimize_system management command with the process-improved-translations option
        # Make sure DISABLE_AUTO_PROCESSES is respected by using the appropriate flags
        logger.info("Running optimize_system command with process-improved-translations option")

        # First, ensure the database is optimized
        logger.info("Optimizing database...")
        call_command('optimize_system', optimize_db=True)

        # Then process improved translations
        logger.info("Processing improved translations...")
        call_command('optimize_system', process_improved_translations=True)

        # Finally, update metrics with the force flag to override DISABLE_AUTO_PROCESSES
        logger.info("Updating metrics...")
        call_command('update_metrics', force=True, quiet=True)

        end_time = datetime.datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"Finished processing improved translations at {end_time} (duration: {duration:.2f} seconds)")

        return True
    except Exception as e:
        logger.error(f"Error processing improved translations: {str(e)}")
        return False

if __name__ == '__main__':
    print("Processing improved translations...")
    success = main()
    if success:
        print("Successfully processed improved translations. See process_improved_translations.log for details.")
    else:
        print("Error processing improved translations. See process_improved_translations.log for details.")
