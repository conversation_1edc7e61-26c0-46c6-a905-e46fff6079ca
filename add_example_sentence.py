import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from translation_app.models import Language, ComprehensiveTranslation, TranslationExample

# Get language objects
try:
    tagalog = Language.objects.get(code='tgl')
    teduray = Language.objects.get(code='ted')
    
    # Add the translation
    translation, created = ComprehensiveTranslation.objects.get_or_create(
        base_word='Ang ilang mga kababaihan, kapag sila ay nanganak, ay hindi umiiyak.',
        source_language=tagalog,
        target_language=teduray,
        defaults={
            'translation': 'Uwëni do dumo libun, amuk mëg<PERSON>nga ro, ënda këmërew ro.',
            'part_of_speech': 'sentence',
            'notes': 'Example sentence with umiiyak'
        }
    )
    
    if created:
        print(f"Added new translation: Ang ilang mga kababaihan, kapag sila ay nanganak, ay hindi umiiyak. → Uwëni do dumo libun, amuk më<PERSON> ro, ënda këm<PERSON>rew ro.")
    else:
        print(f"Updated existing translation: Ang ilang mga kababaihan, kapag sila ay nanganak, ay hindi umiiyak. → Uwëni do dumo libun, amuk mëgënga ro, ënda këmërew ro.")
    
    # Add an example
    example, created = TranslationExample.objects.get_or_create(
        comprehensive_translation=translation,
        source_text='Ang ilang mga kababaihan, kapag sila ay nanganak, ay hindi umiiyak.',
        target_text='Uwëni do dumo libun, amuk mëgënga ro, ënda këmërew ro.'
    )
    
    if created:
        print("Added example")
    else:
        print("Example already exists")
    
except Exception as e:
    print(f"Error: {str(e)}")
