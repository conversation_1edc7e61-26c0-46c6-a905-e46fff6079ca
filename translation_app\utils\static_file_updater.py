"""
Utility functions for updating the static translations file.
"""
import os
import logging
import threading
import time
import datetime
import json
from django.core.management import call_command
from ..utils.platform_detection import PLATFORM_SETTINGS

logger = logging.getLogger(__name__)

def update_static_translations_file(background=True, force=False):
    """
    Update the static translations file.

    Args:
        background (bool): Whether to update in the background or synchronously.
        force (bool): Whether to force the update even if one was recently done.

    Returns:
        bool: True if the update was successful, False otherwise.
    """
    # Check if background tasks are supported on this platform
    use_background = background and PLATFORM_SETTINGS['use_background_tasks']

    # Use a simple file-based lock to prevent multiple updates at the same time
    lock_file = 'static_translations_update.lock'

    # Check if an update is already in progress (unless forced)
    if not force and os.path.exists(lock_file):
        # Check if the lock is stale (older than 5 minutes)
        lock_time = os.path.getmtime(lock_file)
        current_time = time.time()
        if current_time - lock_time < 300:  # 5 minutes in seconds
            logger.info("Static translations update already in progress. Skipping.")
            return False
        else:
            logger.warning("Found stale lock file. Removing it.")
            try:
                os.remove(lock_file)
            except Exception as e:
                logger.error(f"Error removing stale lock file: {str(e)}")

    # Check if the static file already exists and is too large
    static_file_path = os.path.join('static', 'translation_app', 'js', 'all_translations.js')
    if os.path.exists(static_file_path):
        file_size = os.path.getsize(static_file_path)
        max_size = PLATFORM_SETTINGS['max_static_file_size']

        if file_size > max_size:
            logger.warning(f"Static file is too large ({file_size} bytes). Maximum allowed is {max_size} bytes.")
            logger.warning("Will create a reduced version with only essential translations.")

            # Set a flag to create a reduced version
            create_reduced = True
        else:
            create_reduced = False
    else:
        create_reduced = False

    def _update_static_file():
        try:
            # Create lock file
            with open(lock_file, 'w') as f:
                f.write(f"Update started at {datetime.datetime.now().isoformat()}")

            # Call the management command to update the static file
            if create_reduced:
                # Call with the reduced flag
                call_command('update_static_translations', reduced=True)
                logger.info("Reduced static translations file updated successfully")
            else:
                # Normal update
                call_command('update_static_translations')
                logger.info("Static translations file updated successfully")

            # Remove lock file
            if os.path.exists(lock_file):
                os.remove(lock_file)

            return True
        except Exception as e:
            logger.error(f"Error updating static translations file: {str(e)}")

            # Remove lock file even if there was an error
            if os.path.exists(lock_file):
                try:
                    os.remove(lock_file)
                except:
                    pass

            return False

    if use_background:
        # Update in the background
        thread = threading.Thread(target=_update_static_file)
        thread.daemon = True
        thread.start()
        logger.info("Started background thread to update static translations file")
        return True
    else:
        # Update synchronously
        logger.info("Updating static translations file synchronously")
        return _update_static_file()

def update_single_translation_in_static_file(source_text, translation_text, direction):
    """
    Update a single translation in the static file without regenerating the entire file.

    Args:
        source_text (str): The source text to translate
        translation_text (str): The translation
        direction (str): The translation direction (e.g., 'tgl_to_ted')

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Use the incremental updater
        from .incremental_updater import incrementally_update_static_file

        # Determine the source and target languages from the direction
        if direction == 'tgl_to_ted':
            source_lang = 'tgl'
            target_lang = 'ted'
        elif direction == 'ted_to_tgl':
            source_lang = 'ted'
            target_lang = 'tgl'
        else:
            logger.warning(f"Unsupported direction: {direction}")
            return False

        # Update the static file
        success = incrementally_update_static_file(
            source_text,
            translation_text,
            source_lang,
            target_lang
        )

        if success:
            logger.info(f"Successfully updated static file with translation: {source_text} → {translation_text}")
        else:
            logger.warning(f"Failed to update static file with translation: {source_text} → {translation_text}")

        return success
    except Exception as e:
        logger.error(f"Error updating single translation in static file: {str(e)}")
        return False

def create_logs_directory():
    """
    Create the logs directory if it doesn't exist.
    """
    logs_dir = os.path.join('logs')
    os.makedirs(logs_dir, exist_ok=True)

    # Create a log file for translation updates
    log_file = os.path.join(logs_dir, 'translation_updates.log')
    if not os.path.exists(log_file):
        with open(log_file, 'w') as f:
            f.write("# Translation Updates Log\n")

    return logs_dir
