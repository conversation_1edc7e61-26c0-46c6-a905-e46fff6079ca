#!/usr/bin/env python
"""
Test script to check if the db_init module can be imported correctly.
"""

import os
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_import')

# Add the project directory to the Python path
project_dir = os.path.dirname(os.path.abspath(__file__))
if project_dir not in sys.path:
    sys.path.insert(0, project_dir)
    logger.info(f"Added {project_dir} to Python path")

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

# Try to import the module
try:
    logger.info("Attempting to import db_init module...")
    
    # Print Python path for debugging
    logger.info(f"Python path: {sys.path}")
    
    # List files in the translation_app directory
    translation_app_dir = os.path.join(project_dir, 'translation_app')
    if os.path.exists(translation_app_dir):
        logger.info(f"Files in {translation_app_dir}:")
        for file in os.listdir(translation_app_dir):
            logger.info(f"  - {file}")
    else:
        logger.error(f"Directory not found: {translation_app_dir}")
    
    # Try to import the module
    from translation_app.db_init import ensure_database_ready, initialize_database_in_background
    
    logger.info("Successfully imported db_init module")
    
    # Initialize Django
    import django
    django.setup()
    
    # Try to use the imported functions
    logger.info("Calling initialize_database_in_background...")
    initialize_database_in_background()
    
    logger.info("Test completed successfully")
except Exception as e:
    logger.error(f"Error importing db_init module: {str(e)}")
    import traceback
    logger.error(f"Traceback: {traceback.format_exc()}")
