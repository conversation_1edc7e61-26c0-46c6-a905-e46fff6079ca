"""
Management command to learn new translations from any context.
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from translation_app.models import ComprehensiveTranslation, Language, TranslationExample
from translation_app.services import get_translation_service
from translation_app.pattern_learning import pattern_learner

class Command(BaseCommand):
    help = 'Learn new translations from any context'
    
    def add_arguments(self, parser):
        parser.add_argument('--source-lang', type=str, default='tgl', help='Source language code (default: tgl)')
        parser.add_argument('--target-lang', type=str, default='ted', help='Target language code (default: ted)')
        parser.add_argument('--source-text', type=str, required=True, help='Source text to translate')
        parser.add_argument('--target-text', type=str, required=True, help='Target text (translation)')
        parser.add_argument('--context', type=str, default='', help='Context information (e.g., Bible verse, conversation)')
        parser.add_argument('--confidence', type=float, default=0.9, help='Confidence score (0.0-1.0)')
        parser.add_argument('--extract-words', action='store_true', help='Extract individual words and phrases')
    
    def handle(self, *args, **options):
        source_lang_code = options['source_lang']
        target_lang_code = options['target_lang']
        source_text = options['source_text']
        target_text = options['target_text']
        context = options['context']
        confidence = options['confidence']
        extract_words = options['extract_words']
        
        self.stdout.write(self.style.SUCCESS(f'Learning translation from {source_lang_code} to {target_lang_code}...'))
        self.stdout.write(f'Source: {source_text}')
        self.stdout.write(f'Target: {target_text}')
        
        try:
            # Get language objects
            source_language = Language.objects.get(code=source_lang_code)
            target_language = Language.objects.get(code=target_lang_code)
            
            # Add the translation to the database
            with transaction.atomic():
                # Create or update the translation
                trans, created = ComprehensiveTranslation.objects.get_or_create(
                    base_word=source_text,
                    source_language=source_language,
                    target_language=target_language,
                    defaults={
                        'translation': target_text,
                        'part_of_speech': 'phrase' if ' ' in source_text else 'word',
                        'notes': f'Context: {context}' if context else 'Added manually'
                    }
                )
                
                if created:
                    self.stdout.write(self.style.SUCCESS(f'Created new translation: {source_text} → {target_text}'))
                else:
                    self.stdout.write(self.style.SUCCESS(f'Updated existing translation: {source_text} → {target_text}'))
                    trans.translation = target_text
                    if context:
                        trans.notes = f'Context: {context}'
                    trans.save()
                
                # Add an example
                TranslationExample.objects.get_or_create(
                    comprehensive_translation=trans,
                    source_text=source_text,
                    target_text=target_text
                )
                
                # Use pattern learning to extract patterns
                if hasattr(pattern_learner, 'learn_from_example'):
                    pattern_learner.learn_from_example(
                        source_text, 
                        target_text, 
                        source_lang_code, 
                        target_lang_code
                    )
                
                # Extract individual words and phrases if requested
                if extract_words and ' ' in source_text and ' ' in target_text:
                    self.stdout.write('Extracting individual words and phrases...')
                    
                    # This is a simplified implementation
                    # In a production system, you would use more sophisticated alignment algorithms
                    source_words = source_text.split()
                    target_words = target_text.split()
                    
                    # Only attempt word extraction if the number of words is similar
                    if 0.5 <= len(source_words) / len(target_words) <= 2.0:
                        # Extract single words (very simplified approach)
                        for i, source_word in enumerate(source_words):
                            if i < len(target_words):
                                target_word = target_words[i]
                                
                                # Skip very short words
                                if len(source_word) <= 2 or len(target_word) <= 2:
                                    continue
                                
                                # Create or update word translation
                                word_trans, word_created = ComprehensiveTranslation.objects.get_or_create(
                                    base_word=source_word,
                                    source_language=source_language,
                                    target_language=target_language,
                                    defaults={
                                        'translation': target_word,
                                        'part_of_speech': 'word',
                                        'notes': f'Extracted from: {source_text} → {target_text}'
                                    }
                                )
                                
                                if word_created:
                                    self.stdout.write(f'  Created word translation: {source_word} → {target_word}')
            
            # Reload the translation service
            translation_service = get_translation_service()
            translation_service.load_translations(clear_cache=True)
            
            # Test the translation
            result = translation_service.translate_text(source_text, source_lang_code, target_lang_code)
            self.stdout.write(self.style.SUCCESS(f'Translation test: {result["translation"]} (Confidence: {result["confidence"]:.2f})'))
            
            self.stdout.write(self.style.SUCCESS('Translation learned successfully!'))
            
        except Language.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'Language not found: {source_lang_code} or {target_lang_code}'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error learning translation: {str(e)}'))
