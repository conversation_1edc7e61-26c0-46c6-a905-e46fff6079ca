"""
Database utilities for the translation app.

This module provides utilities for managing database connections
and preventing locking issues with SQLite.
"""

import logging
import time
import random
import threading
from contextlib import contextmanager
from django.db import connection, OperationalError

# Configure logging
logger = logging.getLogger(__name__)

# Thread-local storage for connection state
_local = threading.local()

# Lock for synchronizing access to connection pool
_lock = threading.Lock()

# Maximum number of retries for database operations
MAX_RETRIES = 5

# Initial delay between retries in seconds
INITIAL_RETRY_DELAY = 0.5

# Maximum delay between retries in seconds
MAX_RETRY_DELAY = 30

# Whether to add jitter to retry delays
USE_JITTER = True

# Connection pool size
MAX_CONNECTIONS = 5

# Connection pool (not actually used with SQLite, but useful for future migration)
_connection_pool = []


def optimize_connection(conn=None):
    """
    Apply optimizations to a database connection.
    
    Args:
        conn: Database connection to optimize (uses default if None)
    """
    conn = conn or connection
    
    try:
        with conn.cursor() as cursor:
            # Set busy timeout to 120 seconds
            cursor.execute("PRAGMA busy_timeout = 120000;")

            # Set journal mode to DELETE (safer than WAL for PythonAnywhere)
            cursor.execute("PRAGMA journal_mode = DELETE;")

            # Set synchronous mode to NORMAL (safer than OFF)
            cursor.execute("PRAGMA synchronous = NORMAL;")

            # Set temp store to MEMORY
            cursor.execute("PRAGMA temp_store = MEMORY;")

            # Set cache size (in pages, 1 page = 4KB)
            cursor.execute("PRAGMA cache_size = 5000;")
            
            # Set locking mode to EXCLUSIVE to reduce lock contention
            cursor.execute("PRAGMA locking_mode = EXCLUSIVE;")
            
            # Set page size for better performance
            cursor.execute("PRAGMA page_size = 4096;")
            
            logger.debug("Database connection optimized successfully")
    except Exception as e:
        logger.error(f"Error optimizing database connection: {str(e)}")


@contextmanager
def db_retry_context(max_retries=MAX_RETRIES, retry_delay=INITIAL_RETRY_DELAY, 
                    max_delay=MAX_RETRY_DELAY, jitter=USE_JITTER):
    """
    Context manager for database operations with retry logic.
    
    Args:
        max_retries: Maximum number of retries
        retry_delay: Initial delay between retries in seconds
        max_delay: Maximum delay between retries in seconds
        jitter: Whether to add random jitter to delay times
        
    Yields:
        Database connection
    """
    retries = 0
    
    while True:
        try:
            # Optimize the connection before use
            optimize_connection()
            
            # Yield the connection for use
            yield connection
            
            # If we get here, the operation succeeded
            break
            
        except OperationalError as e:
            error_str = str(e).lower()
            # Check for various locking-related errors
            is_lock_error = any(err in error_str for err in [
                "locking protocol", "database is locked", "busy", 
                "timeout", "no such table", "disk i/o error"
            ])
            
            if is_lock_error and retries < max_retries:
                retries += 1
                # Calculate delay with exponential backoff
                delay = min(retry_delay * (2 ** (retries - 1)), max_delay)
                
                # Add jitter to prevent retry storms
                if jitter:
                    delay = delay * (0.5 + random.random())
                
                logger.warning(f"Database lock detected: '{error_str}', retrying ({retries}/{max_retries}) in {delay:.2f}s...")
                time.sleep(delay)
            else:
                logger.error(f"Database error after {retries} retries: {str(e)}")
                raise
        finally:
            # Always ensure the connection is properly closed
            try:
                connection.close()
            except Exception as e:
                logger.error(f"Error closing database connection: {str(e)}")


def with_db_retry(func, *args, **kwargs):
    """
    Execute a database function with retry on locking errors.
    
    Args:
        func: Function to execute
        *args: Arguments to pass to the function
        **kwargs: Keyword arguments to pass to the function
        
    Returns:
        Result of the function
    """
    max_retries = kwargs.pop('max_retries', MAX_RETRIES)
    retry_delay = kwargs.pop('retry_delay', INITIAL_RETRY_DELAY)
    max_delay = kwargs.pop('max_delay', MAX_RETRY_DELAY)
    jitter = kwargs.pop('jitter', USE_JITTER)
    
    with db_retry_context(max_retries, retry_delay, max_delay, jitter):
        return func(*args, **kwargs)


def execute_query(query, params=None, fetchall=True):
    """
    Execute a SQL query with retry logic.
    
    Args:
        query: SQL query to execute
        params: Parameters for the query
        fetchall: Whether to fetch all results
        
    Returns:
        Query results
    """
    def _execute():
        with connection.cursor() as cursor:
            cursor.execute(query, params or [])
            if fetchall:
                return cursor.fetchall()
            return cursor.fetchone()
    
    return with_db_retry(_execute)


def vacuum_database():
    """
    Vacuum the database to optimize storage and performance.
    """
    try:
        logger.info("Vacuuming database...")
        with connection.cursor() as cursor:
            # First run PRAGMA optimize
            cursor.execute("PRAGMA optimize;")
            # Then vacuum the database
            cursor.execute("VACUUM;")
        logger.info("Database vacuum completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error vacuuming database: {str(e)}")
        return False
