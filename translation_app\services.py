"""
Translation services for the Tagalog-Teduray translation app.
"""

import logging
import re
import json
import os
import datetime
import threading
from typing import Dict, List, Optional, Tuple
from django.db.models import Q
from django.core.cache import cache
from django.conf import settings
from django.utils import timezone

from translation_app.models import (
    Word,
    Translation,
    Language,
    ComprehensiveTranslation,
    TranslationExample,
    TranslationVersion,
    TranslationMetrics,
    BleuScoreHistory,
    AttentionData
)

# Import pattern learning module
try:
    from translation_app.pattern_learning import pattern_learner, apply_patterns
    PATTERN_LEARNING_ENABLED = True
except ImportError:
    PATTERN_LEARNING_ENABLED = False

# Import phonological rules module
try:
    from translation_app.phonological_rules import process_text as apply_phonological_rules
    PHONOLOGICAL_RULES_ENABLED = True
except ImportError:
    PHONOLOGICAL_RULES_ENABLED = False

# Import attention mechanism module
try:
    from translation_app.attention_mechanism import attention_mechanism
    ATTENTION_MECHANISM_ENABLED = True
except ImportError:
    ATTENTION_MECHANISM_ENABLED = False

# Import BLEU score module
try:
    from translation_app.bleu_score import bleu_calculator
    BLEU_SCORE_ENABLED = True
except ImportError:
    BLEU_SCORE_ENABLED = False

# Import fuzzy matching module
try:
    from translation_app.fuzzy_matching import fuzzy_matcher
    FUZZY_MATCHING_ENABLED = True
except ImportError:
    FUZZY_MATCHING_ENABLED = False

# Import Hugging Face integration module - disabled to prevent database locks
# try:
#     from translation_app.huggingface_translation import huggingface_translator
#     HUGGINGFACE_ENABLED = True
# except ImportError:
#     HUGGINGFACE_ENABLED = False
HUGGINGFACE_ENABLED = False  # Explicitly disabled to prevent database locks

logger = logging.getLogger(__name__)

class TranslationService:
    """
    Service for translating between Tagalog and Teduray.
    """
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.cache_key = 'translation_service_cache'
        self.cache_timeout = 3600  # 1 hour

        # Add a recent translations cache to speed up repeated translations
        self.recent_translations_cache = {}
        self.recent_translations_max_size = 1000  # Store up to 1000 recent translations

        # Add a flag to indicate if we're still loading translations
        self.is_loading = False

        # Add a flag to track if we've done a complete load
        self.complete_load_done = False

        # Add a flag to indicate if we should use only the database
        from django.conf import settings
        self.use_database_only = getattr(settings, 'USE_DATABASE_ONLY', False)
        if self.use_database_only:
            self.logger.info("Using database only for translations (static files disabled)")

        # Add flags for loading options
        self.use_lazy_loading = getattr(settings, 'USE_LAZY_LOADING', True)
        self.use_ultra_lazy_loading = getattr(settings, 'USE_ULTRA_LAZY_LOADING', False)
        self.pythonanywhere_optimization = getattr(settings, 'PYTHONANYWHERE_OPTIMIZATION', False)

        # Check if automatic loading is disabled
        self.disable_auto_loading = getattr(settings, 'DISABLE_AUTO_PROCESSES', False)

        # Initialize with empty dictionaries
        self.word_translations = {
            'tgl_to_ted': {},  # Tagalog to Teduray
            'ted_to_tgl': {}   # Teduray to Tagalog
        }

        # If automatic loading is disabled, only load minimal translations
        if self.disable_auto_loading:
            self.logger.info("Automatic loading disabled - only loading minimal translations")
            self._load_minimal_translations()

            # Set flags to indicate we're not fully loaded
            self.is_loading = False
            self.complete_load_done = False

            # Log that translations will be loaded on demand
            self.logger.info("Translations will be loaded on demand or by scheduled task")
            return

        # Check if we've been preloaded
        preloaded = cache.get('translation_service_preloaded', False)

        # Ultra lazy loading - absolute minimum at startup
        if self.use_ultra_lazy_loading:
            self.logger.info("Using ultra lazy loading for translations")

            # If we're on PythonAnywhere, use even more aggressive optimization
            if self.pythonanywhere_optimization:
                self.logger.info("Using PythonAnywhere optimization")

                # If we've been preloaded, we already have essential translations
                if preloaded:
                    self.logger.info("Using preloaded translations")
                    # Just load the absolute minimum (5 most common words)
                    self._load_minimal_translations()
                else:
                    # Load essential translations
                    self._load_minimal_translations()

                # Log that translations will be loaded on demand
                self.logger.info("Additional translations will be loaded on demand or by scheduled task")
            else:
                # Load essential translations
                self._load_minimal_translations()

                # Log that translations will be loaded on demand
                self.logger.info("Additional translations will be loaded on demand or by scheduled task")

        # Regular lazy loading
        elif self.use_lazy_loading:
            self.logger.info("Using lazy loading for translations")

            # Load only essential translations (hardcoded common words)
            self._load_minimal_translations()

            # Log that translations will be loaded on demand
            self.logger.info("Additional translations will be loaded on demand or by scheduled task")

        # Traditional loading (not lazy)
        else:
            # Try to get from cache first
            cached_translations = cache.get(self.cache_key)
            if cached_translations:
                self.word_translations = cached_translations
                self.complete_load_done = cache.get('translation_service_complete_load', False)
                self.logger.info("Using cached translations")
            else:
                # Load only minimal translations
                self._load_minimal_translations()
                self.logger.info("Additional translations will be loaded on demand or by scheduled task")

    def load_translations(self, clear_cache=True):
        """
        Load translations from database and data files into memory.
        Uses a progressive loading approach for better performance.
        """
        self.logger.info("Loading translations progressively...")

        # Set loading flag
        self.is_loading = True

        # Check if we already have translations in cache
        cached_translations = cache.get(self.cache_key)
        if cached_translations and not clear_cache:
            self.logger.info("Using cached translations")
            self.word_translations = cached_translations
            self.is_loading = False
            return

        # Clear cache if requested
        if clear_cache:
            cache.delete(self.cache_key)
            self.logger.info("Cache cleared")

        # Initialize dictionaries for word translations
        self.word_translations = {
            'tgl_to_ted': {},  # Tagalog to Teduray
            'ted_to_tgl': {}   # Teduray to Tagalog
        }

        # First, load only the most common translations for immediate use
        self.load_common_translations()

        # Cache what we have so far for immediate use
        cache.set(self.cache_key, self.word_translations, self.cache_timeout)

        # Start a background thread to load the rest of the translations
        loading_thread = threading.Thread(target=self._load_remaining_translations)
        loading_thread.daemon = True
        loading_thread.start()

        self.logger.info(f"Started progressive loading with {len(self.word_translations['tgl_to_ted'])} initial Tagalog to Teduray translations")
        self.logger.info(f"Started progressive loading with {len(self.word_translations['ted_to_tgl'])} initial Teduray to Tagalog translations")

        # We can set is_loading to False here since we've loaded the common translations
        # and the rest will load in the background
        self.is_loading = False

    def _load_remaining_translations(self):
        """
        Load the remaining translations in the background.
        This allows the application to start responding quickly while still loading all translations.
        """
        try:
            self.logger.info("Loading remaining translations in background...")

            # Set loading flag for background loading
            self.is_loading = True

            # Load from database first (excluding what we've already loaded)
            self.load_translations_from_database(skip_common=True)

            # Then load from JSON files to supplement (without limiting size since this is the full load)
            self.load_translations_from_files(limit_size=False)

            # Load learned translations from text file
            self.load_learned_translations()

            # Update the cache with the complete set of translations
            cache.set(self.cache_key, self.word_translations, self.cache_timeout)

            # Set the complete load flag in the cache
            cache.set('translation_service_complete_load', True, self.cache_timeout)
            self.complete_load_done = True

            # Update fuzzy matcher with the complete dictionary
            if FUZZY_MATCHING_ENABLED and hasattr(fuzzy_matcher, 'initialize_with_dictionary'):
                try:
                    if 'tgl_to_ted' in self.word_translations:
                        fuzzy_matcher.initialize_with_dictionary(self.word_translations['tgl_to_ted'], 'tgl_to_ted')
                    if 'ted_to_tgl' in self.word_translations:
                        fuzzy_matcher.initialize_with_dictionary(self.word_translations['ted_to_tgl'], 'ted_to_tgl')
                    self.logger.info("Updated fuzzy matcher with complete translation dictionary")
                except Exception as e:
                    self.logger.error(f"Error updating fuzzy matcher: {str(e)}")

            self.logger.info(f"Completed loading all translations: {len(self.word_translations['tgl_to_ted'])} Tagalog to Teduray translations")
            self.logger.info(f"Completed loading all translations: {len(self.word_translations['ted_to_tgl'])} Teduray to Tagalog translations")
            self.logger.info("Complete load flag set - future translations will be faster")

            # Reset loading flag
            self.is_loading = False
        except Exception as e:
            self.logger.error(f"Error loading remaining translations: {str(e)}")
            # Make sure to reset loading flag even if there's an error
            self.is_loading = False

    def _load_minimal_translations(self):
        """
        Load only the absolute minimum translations (10 most common words).
        This is used for ultra lazy loading on PythonAnywhere.
        """
        self.logger.info("Loading minimal translations (absolute minimum)...")

        try:
            # Add only the most essential translations directly to memory
            minimal_words = {
                'tgl_to_ted': {
                    # Absolute minimum set of words
                    'kumusta': 'fiyo',
                    'salamat': 'salamat',
                    'oo': 'hoo',
                    'hindi': 'énda',
                    'jesus': 'jesus'
                },
                'ted_to_tgl': {
                    # Absolute minimum set of words
                    'fiyo': 'kumusta',
                    'salamat': 'salamat',
                    'hoo': 'oo',
                    'énda': 'hindi',
                    'jesus': 'jesus'
                }
            }

            # Add minimal words to the dictionaries
            for word, translation in minimal_words['tgl_to_ted'].items():
                self.word_translations['tgl_to_ted'][word.lower()] = {
                    'translation': translation,
                    'part_of_speech': '',
                    'notes': 'Minimal word',
                    'cultural_notes': '',
                    'examples': [],
                    'priority': True,
                    'confidence': 0.99,
                    'source': 'minimal',
                    'version_id': None
                }

            for word, translation in minimal_words['ted_to_tgl'].items():
                self.word_translations['ted_to_tgl'][word.lower()] = {
                    'translation': translation,
                    'part_of_speech': '',
                    'notes': 'Minimal word',
                    'cultural_notes': '',
                    'examples': [],
                    'priority': True,
                    'confidence': 0.99,
                    'source': 'minimal',
                    'version_id': None
                }

            self.logger.info(f"Loaded {len(minimal_words['tgl_to_ted'])} minimal Tagalog to Teduray translations")
            self.logger.info(f"Loaded {len(minimal_words['ted_to_tgl'])} minimal Teduray to Tagalog translations")

        except Exception as e:
            self.logger.error(f"Error loading minimal translations: {str(e)}")

    def _load_essential_translations(self):
        """
        Load only the most essential translations (hardcoded common words).
        This is the absolute minimum needed for the system to function.
        """
        self.logger.info("Loading essential translations (hardcoded)...")

        try:
            # Add some very common translations directly to memory for immediate response
            # This helps with the initial loading speed
            common_words = {
                'tgl_to_ted': {
                    # Basic greetings and common words
                    'kumusta': 'fiyo',
                    'salamat': 'salamat',
                    'oo': 'hoo',
                    'hindi': 'énda',
                    'aklat': 'libro',
                    'jesus': 'jesus',
                    'mahal': 'kégédaw',
                    'kita': 'gito',

                    # Common phrases
                    'kumusta ka': 'fiyo go',
                    'salamat po': 'salamat fo',
                    'magandang umaga': 'fiyo kélungonon',
                    'magandang hapon': 'fiyo kérara',
                    'magandang gabi': 'fiyo kékélungon',
                    'paalam': 'taman',

                    # Common question words
                    'ano': 'ati',
                    'sino': 'ati',
                    'kailan': 'kédiron',
                    'saan': 'ati gonon',
                    'bakit': 'sedek',
                },
                'ted_to_tgl': {
                    # Basic greetings and common words
                    'fiyo': 'kumusta',
                    'salamat': 'salamat',
                    'hoo': 'oo',
                    'énda': 'hindi',
                    'libro': 'aklat',
                    'jesus': 'jesus',
                    'kégédaw': 'mahal',
                    'gito': 'kita',

                    # Common phrases
                    'fiyo go': 'kumusta ka',
                    'salamat fo': 'salamat po',
                    'fiyo kélungonon': 'magandang umaga',
                    'fiyo kérara': 'magandang hapon',
                    'fiyo kékélungon': 'magandang gabi',
                    'taman': 'paalam',

                    # Common question words
                    'ati': 'ano',
                    'kédiron': 'kailan',
                    'ati gonon': 'saan',
                    'sedek': 'bakit',
                }
            }

            # Add common words to the dictionaries
            for word, translation in common_words['tgl_to_ted'].items():
                self.word_translations['tgl_to_ted'][word.lower()] = {
                    'translation': translation,
                    'part_of_speech': '',
                    'notes': 'Common word',
                    'cultural_notes': '',
                    'examples': [],
                    'priority': True,
                    'confidence': 0.99,
                    'source': 'essential',
                    'version_id': None
                }

            for word, translation in common_words['ted_to_tgl'].items():
                self.word_translations['ted_to_tgl'][word.lower()] = {
                    'translation': translation,
                    'part_of_speech': '',
                    'notes': 'Common word',
                    'cultural_notes': '',
                    'examples': [],
                    'priority': True,
                    'confidence': 0.99,
                    'source': 'essential',
                    'version_id': None
                }

            self.logger.info(f"Loaded {len(common_words['tgl_to_ted'])} essential Tagalog to Teduray translations")
            self.logger.info(f"Loaded {len(common_words['ted_to_tgl'])} essential Teduray to Tagalog translations")

        except Exception as e:
            self.logger.error(f"Error loading essential translations: {str(e)}")

    def _load_more_translations_in_background(self):
        """
        Load more translations in the background after the essential ones are loaded.
        This allows the application to start quickly while still loading more translations.
        """
        try:
            self.logger.info("Loading more translations in background...")
            self.is_loading = True

            # Try to get from cache first
            cached_translations = cache.get(self.cache_key)
            if cached_translations:
                # Merge with our essential translations
                for direction in ['tgl_to_ted', 'ted_to_tgl']:
                    if direction in cached_translations:
                        # Only add translations we don't already have
                        for word, data in cached_translations[direction].items():
                            if word not in self.word_translations[direction]:
                                self.word_translations[direction][word] = data

                self.logger.info("Loaded additional translations from cache")
                self.complete_load_done = cache.get('translation_service_complete_load', False)

                # If we have a complete cache, we're done
                if self.complete_load_done:
                    self.is_loading = False
                    self.logger.info("Completed loading translations from cache")
                    return

            # Load common translations from the database (top 100 only)
            self._load_common_translations_from_db(limit=100)

            # Update the cache with what we have so far
            cache.set(self.cache_key, self.word_translations, self.cache_timeout)

            # We're done with the initial background loading
            self.is_loading = False

            # Start another thread for full loading if needed
            if not self.complete_load_done:
                full_loading_thread = threading.Thread(target=self._load_full_translations_in_background)
                full_loading_thread.daemon = True
                full_loading_thread.start()

        except Exception as e:
            self.logger.error(f"Error loading more translations in background: {str(e)}")
            self.is_loading = False

    def _load_common_translations_from_db(self, limit=100):
        """
        Load common translations from the database.

        Args:
            limit: Maximum number of translations to load
        """
        try:
            # Get language objects
            tagalog = Language.objects.filter(code='tgl').first()
            teduray = Language.objects.filter(code='ted').first()

            if not tagalog or not teduray:
                self.logger.error("Languages not found in database")
                return

            # Load the most common translations first
            common_translations = ComprehensiveTranslation.objects.select_related(
                'source_language', 'target_language'
            ).prefetch_related(
                'versions'
            ).filter(
                Q(source_language=tagalog, target_language=teduray) |
                Q(source_language=teduray, target_language=tagalog)
            ).order_by('-updated_at')[:limit]

            # Process translations
            tgl_to_ted_count = 0
            ted_to_tgl_count = 0

            for ct in common_translations:
                # Skip if we already have this translation
                if ct.source_language == tagalog and ct.target_language == teduray:
                    if ct.base_word.lower() in self.word_translations['tgl_to_ted']:
                        continue
                    direction = 'tgl_to_ted'
                    tgl_to_ted_count += 1
                elif ct.source_language == teduray and ct.target_language == tagalog:
                    if ct.base_word.lower() in self.word_translations['ted_to_tgl']:
                        continue
                    direction = 'ted_to_tgl'
                    ted_to_tgl_count += 1
                else:
                    continue

                # Get active version for confidence score
                active_version = None
                try:
                    active_version = next((v for v in ct.versions.all() if v.is_active), None)
                except Exception:
                    pass

                confidence_score = active_version.confidence_score if active_version else 0.95

                # Store translation
                self.word_translations[direction][ct.base_word.lower()] = {
                    'translation': ct.translation,
                    'part_of_speech': ct.part_of_speech,
                    'notes': ct.notes,
                    'cultural_notes': ct.cultural_notes,
                    'examples': [],  # Skip examples for common translations to speed up loading
                    'priority': True,
                    'confidence': confidence_score,
                    'source': 'common_db',
                    'version_id': active_version.id if active_version else None
                }

            self.logger.info(f"Loaded {tgl_to_ted_count} additional Tagalog to Teduray translations from database")
            self.logger.info(f"Loaded {ted_to_tgl_count} additional Teduray to Tagalog translations from database")

        except Exception as e:
            self.logger.error(f"Error loading common translations from database: {str(e)}")

    def _load_full_translations_in_background(self):
        """
        Load all translations in the background.
        This is the final loading step that happens after the application has started.
        """
        try:
            self.logger.info("Starting full translation loading in background...")

            # Load from database
            self.load_translations_from_database(skip_common=True)

            # Load from files (without limiting size since this is the full load)
            self.load_translations_from_files(limit_size=False)

            # Load learned translations
            self.load_learned_translations()

            # Update the cache with the complete set
            cache.set(self.cache_key, self.word_translations, self.cache_timeout)

            # Set the complete load flag
            cache.set('translation_service_complete_load', True, self.cache_timeout)
            self.complete_load_done = True

            self.logger.info(f"Completed full loading: {len(self.word_translations['tgl_to_ted'])} Tagalog to Teduray translations")
            self.logger.info(f"Completed full loading: {len(self.word_translations['ted_to_tgl'])} Teduray to Tagalog translations")

        except Exception as e:
            self.logger.error(f"Error in full translation loading: {str(e)}")

    def load_common_translations(self):
        """
        Load only the most common translations first for faster initial response.
        This is used for progressive loading to improve user experience.
        Optimized for faster loading.
        """
        self.logger.info("Loading common translations (optimized)...")

        try:
            # Initialize dictionaries if they don't exist
            if not self.word_translations:
                self.word_translations = {
                    'tgl_to_ted': {},  # Tagalog to Teduray
                    'ted_to_tgl': {}   # Teduray to Tagalog
                }

            # Load essential translations first
            self._load_essential_translations()

            # Then load common translations from the database
            self._load_common_translations_from_db(limit=500)

            # Cache what we have so far
            cache.set(self.cache_key, self.word_translations, self.cache_timeout)

        except Exception as e:
            self.logger.error(f"Error loading common translations: {str(e)}")

    def load_translations_from_database(self, skip_common=False):
        """
        Load translations from the database into memory

        Args:
            skip_common: If True, skip loading the most common translations (already loaded)
        """
        self.logger.info(f"Loading translations from database... (skip_common={skip_common})")

        # Use select_related and prefetch_related to optimize database queries

        try:
            # Get language objects
            tagalog = Language.objects.get(code='tgl')
            teduray = Language.objects.get(code='ted')

            # Get all translations between Tagalog and Teduray
            translations = Translation.objects.filter(
                (Q(source_word__language=tagalog) & Q(target_word__language=teduray)) |
                (Q(source_word__language=teduray) & Q(target_word__language=tagalog))
            ).select_related('source_word', 'target_word')

            # Process translations
            for translation in translations:
                source_word = translation.source_word
                target_word = translation.target_word

                # Determine direction and add to appropriate dictionary
                if source_word.language.code == 'tgl' and target_word.language.code == 'ted':
                    self.word_translations['tgl_to_ted'][source_word.text.lower()] = {
                        'translation': target_word.text,
                        'part_of_speech': source_word.part_of_speech,
                        'notes': translation.notes,
                        'examples': [],
                        'priority': True,  # Database entries have priority
                        'source': 'database'
                    }
                elif source_word.language.code == 'ted' and target_word.language.code == 'tgl':
                    self.word_translations['ted_to_tgl'][source_word.text.lower()] = {
                        'translation': target_word.text,
                        'part_of_speech': source_word.part_of_speech,
                        'notes': translation.notes,
                        'examples': [],
                        'priority': True,  # Database entries have priority
                        'source': 'database'
                    }

            # Load comprehensive translations with optimized queries
            # Filter to only get translations between Tagalog and Teduray
            # If skip_common is True, exclude translations we've already loaded
            query = Q(source_language=tagalog, target_language=teduray) | Q(source_language=teduray, target_language=tagalog)

            # Add a filter to limit the number of translations if we're in lazy loading mode
            if self.use_lazy_loading and not skip_common:
                # First, prioritize loading all sentence/phrase translations (containing spaces)
                phrase_translations = ComprehensiveTranslation.objects.filter(
                    query,
                    base_word__contains=' '  # This finds phrases/sentences
                ).select_related(
                    'source_language', 'target_language'
                ).prefetch_related(
                    'versions'
                )

                # Then load the most recently updated single-word translations
                word_translations = ComprehensiveTranslation.objects.filter(
                    query,
                    ~Q(base_word__contains=' ')  # This excludes phrases/sentences
                ).select_related(
                    'source_language', 'target_language'
                ).prefetch_related(
                    'versions'
                ).order_by('-updated_at')[:1000]  # Limit to 1000 most recent single-word translations

                # Combine both querysets
                comp_translations = list(phrase_translations) + list(word_translations)

                self.logger.info(f"Using lazy loading - loaded {phrase_translations.count()} phrases and up to 1000 single words")
            else:
                # Load all translations with optimized queries
                comp_translations = ComprehensiveTranslation.objects.filter(query).select_related(
                    'source_language', 'target_language'
                ).prefetch_related(
                    'examples', 'related_forms', 'versions'
                )

            # Get count without executing the full query
            count_estimate = ComprehensiveTranslation.objects.filter(query).count()
            self.logger.info(f"Loading approximately {count_estimate} comprehensive translations")

            for ct in comp_translations:
                direction = None
                if ct.source_language.code == 'tgl' and ct.target_language.code == 'ted':
                    direction = 'tgl_to_ted'
                elif ct.source_language.code == 'ted' and ct.target_language.code == 'tgl':
                    direction = 'ted_to_tgl'

                if direction:
                    # Only load examples if we're not in lazy loading mode or if this is a full load
                    if not self.use_lazy_loading or skip_common:
                        examples = []
                        for ex in ct.examples.all():
                            examples.append({
                                'source': ex.source_text,
                                'target': ex.target_text
                            })
                    else:
                        # Skip loading examples in lazy loading mode for faster startup
                        examples = []

                    # Get the active version if available - using prefetched data
                    active_version = None
                    try:
                        # Use prefetched versions instead of making a new query
                        active_version = next((v for v in ct.versions.all() if v.is_active), None)
                    except Exception:
                        pass

                    # Use the version's translation if available, otherwise use the base translation
                    translation_text = active_version.translation if active_version else ct.translation
                    confidence_score = active_version.confidence_score if active_version else 0.95

                    # Check if this is a phrase (contains spaces)
                    if ' ' in ct.base_word:
                        # Store as a phrase with the special prefix
                        phrase_key = f"phrase:{ct.base_word.lower()}"
                        self.word_translations[direction][phrase_key] = {
                            'translation': translation_text,
                            'part_of_speech': ct.part_of_speech,
                            'notes': ct.notes,
                            'cultural_notes': ct.cultural_notes,
                            'examples': examples,
                            'priority': True,
                            'confidence': confidence_score,
                            'source': 'comprehensive',
                            'version_id': active_version.id if active_version else None
                        }
                        self.logger.info(f"Loaded phrase: {ct.base_word} -> {translation_text}")
                    else:
                        # Store as a regular word
                        self.word_translations[direction][ct.base_word.lower()] = {
                            'translation': translation_text,
                            'part_of_speech': ct.part_of_speech,
                            'notes': ct.notes,
                            'cultural_notes': ct.cultural_notes,
                            'examples': examples,
                            'priority': True,
                            'confidence': confidence_score,
                            'source': 'comprehensive',
                            'version_id': active_version.id if active_version else None
                        }

        except Exception as e:
            self.logger.error(f"Error loading translations from database: {str(e)}")

    def load_translations_from_files(self, limit_size=False):
        """
        Load translations from JSON files in the data directory

        Args:
            limit_size: If True, limit the number of translations loaded from files
        """
        # Skip loading from files if use_database_only is True
        if self.use_database_only:
            self.logger.info("Skipping loading translations from files (database only mode)")
            return

        # Skip loading from files if we're in lazy loading mode and this is not a full load
        if self.use_lazy_loading and limit_size:
            self.logger.info("Skipping loading translations from files in lazy loading mode")
            return

        self.logger.info("Loading translations from JSON files...")

        try:
            # Load word mappings from JSON file
            word_mappings_path = os.path.join('data', 'word_mappings.json')
            if os.path.exists(word_mappings_path):
                with open(word_mappings_path, 'r', encoding='utf-8') as f:
                    word_mappings = json.load(f)

                # Process Teduray to Tagalog mappings
                if 'ted_to_tgl' in word_mappings:
                    for word, info in word_mappings['ted_to_tgl'].items():
                        # Only add if not already in database or if this has priority
                        if word.lower() not in self.word_translations['ted_to_tgl'] or \
                           (info.get('priority', False) and not self.word_translations['ted_to_tgl'][word.lower()].get('priority', False)):
                            self.word_translations['ted_to_tgl'][word.lower()] = {
                                'translation': info.get('translation', ''),
                                'part_of_speech': info.get('part_of_speech', ''),
                                'notes': info.get('notes', ''),
                                'examples': info.get('examples', []),
                                'priority': info.get('priority', False),
                                'source': 'word_mappings'
                            }

                # Process Tagalog to Teduray mappings
                if 'tgl_to_ted' in word_mappings:
                    for word, info in word_mappings['tgl_to_ted'].items():
                        # Only add if not already in database or if this has priority
                        if word.lower() not in self.word_translations['tgl_to_ted'] or \
                           (info.get('priority', False) and not self.word_translations['tgl_to_ted'][word.lower()].get('priority', False)):
                            self.word_translations['tgl_to_ted'][word.lower()] = {
                                'translation': info.get('translation', ''),
                                'part_of_speech': info.get('part_of_speech', ''),
                                'notes': info.get('notes', ''),
                                'examples': info.get('examples', []),
                                'priority': info.get('priority', False),
                                'source': 'word_mappings'
                            }

            # Load Bible translations for examples
            bible_path = os.path.join('data', 'bible_translations.json')
            if os.path.exists(bible_path):
                with open(bible_path, 'r', encoding='utf-8') as f:
                    bible_data = json.load(f)

                # Process Bible verses if they exist
                if 'verses' in bible_data:
                    for verse_id, verse_data in bible_data['verses'].items():
                        if 'ted' in verse_data and 'tgl' in verse_data:
                            # Extract words from verses and create mappings
                            self._extract_phrase_mappings(verse_data['ted'], verse_data['tgl'], 'ted_to_tgl')
                            self._extract_phrase_mappings(verse_data['tgl'], verse_data['ted'], 'tgl_to_ted')

        except Exception as e:
            self.logger.error(f"Error loading translations from files: {str(e)}")

    def load_learned_translations(self):
        """Load learned translations from text file"""
        # Skip loading from files if use_database_only is True
        if self.use_database_only:
            self.logger.info("Skipping loading learned translations from files (database only mode)")
            return

        self.logger.info("Loading learned translations...")

        try:
            learned_path = os.path.join('data', 'learned_translations.txt')
            if os.path.exists(learned_path):
                with open(learned_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if not line:
                            continue

                        parts = line.split('|')
                        if len(parts) == 4:
                            source_lang, source_text, target_lang, target_text = parts

                            if source_lang == 'tgl' and target_lang == 'ted':
                                direction = 'tgl_to_ted'
                                self._add_learned_translation(source_text, target_text, direction)
                            elif source_lang == 'ted' and target_lang == 'tgl':
                                direction = 'ted_to_tgl'
                                self._add_learned_translation(source_text, target_text, direction)

        except Exception as e:
            self.logger.error(f"Error loading learned translations: {str(e)}")


    def extract_words_from_sentence(self, source_text, translated_text, source_lang, target_lang):
        """
        Extract individual words from a sentence pair and add them to the translation dictionary.
        This is used when a user likes a translation, to learn individual words from the sentence.

        Args:
            source_text: The original text in the source language
            translated_text: The translated text in the target language
            source_lang: The source language code (e.g., 'tgl')
            target_lang: The target language code (e.g., 'ted')

        Returns:
            A list of word pairs that were extracted and added to the dictionary
        """
        if not source_text or not translated_text:
            return []

        # Determine the translation direction
        direction = f"{source_lang}_to_{target_lang}"

        # Clean up punctuation and normalize
        import re
        source_clean = re.sub(r'[^\w\s]', '', source_text.lower())
        target_clean = re.sub(r'[^\w\s]', '', translated_text.lower())

        # Split the texts into words
        source_words = source_clean.split()
        target_words = target_clean.split()

        # If the number of words is very different, we can't reliably extract word pairs
        if len(source_words) == 0 or len(target_words) == 0:
            return []

        # If there's only one word in each, it's a direct mapping
        if len(source_words) == 1 and len(target_words) == 1:
            self._add_learned_translation(source_words[0], target_words[0], direction)
            return [(source_words[0], target_words[0])]

        # For multi-word sentences, we need to be more careful
        # We'll use a simple alignment strategy based on position
        extracted_pairs = []

        # Special case handling for known words
        # This is a hardcoded approach for specific words we know are problematic
        # In a production system, this would be replaced with a more sophisticated approach
        known_word_pairs = {
            'umiiyak': 'këmërew',
            'hindi': 'ënda',
            'nanganak': 'mëgënga',
            'kababaihan': 'libun',
            'ilang': 'uwëni',
            'sila': 'ro',
            'kapag': 'amuk'
        }

        # First, check for known words in the source text
        for source_word in source_words:
            # Clean the word (remove punctuation)
            clean_word = re.sub(r'[^\w\s]', '', source_word.lower())

            # Check if this is a known word
            if clean_word in known_word_pairs:
                target_word = known_word_pairs[clean_word]
                self._add_learned_translation(clean_word, target_word, direction)
                extracted_pairs.append((clean_word, target_word))
                self.logger.info(f"Added known word pair: {clean_word} → {target_word}")

        # If we found known words, we're done
        if extracted_pairs:
            self.logger.info(f"Extracted {len(extracted_pairs)} known word pairs from sentence")
            return extracted_pairs

        # If the number of words is the same, we can do a direct mapping
        if len(source_words) == len(target_words):
            for i in range(len(source_words)):
                # Skip very short words (less than 3 characters)
                if len(source_words[i]) < 3 or len(target_words[i]) < 3:
                    continue

                # Add the word pair to the dictionary
                self._add_learned_translation(source_words[i], target_words[i], direction)
                extracted_pairs.append((source_words[i], target_words[i]))

        # If the number of words is different, we'll use a more sophisticated approach
        # using attention mechanism if available
        elif ATTENTION_MECHANISM_ENABLED and hasattr(attention_mechanism, 'calculate_attention'):
            try:
                # Create dummy word_by_word data for attention calculation
                word_by_word = []
                for word in source_words:
                    word_by_word.append({
                        'original': word,
                        'translation': '',
                        'confidence': 0.5,
                        'source': 'extraction'
                    })

                # Calculate attention scores
                attention_data = attention_mechanism.calculate_attention(
                    source_words, target_words, word_by_word
                )

                if attention_data and 'attention_matrix' in attention_data:
                    # Use the attention matrix to find the best matches
                    matrix = attention_data['attention_matrix']

                    for i, source_word in enumerate(source_words):
                        # Skip very short words
                        if len(source_word) < 3:
                            continue

                        # Find the target word with the highest attention score
                        max_score = 0
                        best_match_idx = -1

                        for j in range(len(target_words)):
                            if matrix[i][j] > max_score:
                                max_score = matrix[i][j]
                                best_match_idx = j

                        # If we found a good match, add it to the dictionary
                        if max_score > 0.5 and best_match_idx >= 0:
                            target_word = target_words[best_match_idx]

                            # Skip very short target words
                            if len(target_word) < 3:
                                continue

                            self._add_learned_translation(source_word, target_word, direction)
                            extracted_pairs.append((source_word, target_word))
            except Exception as e:
                self.logger.error(f"Error using attention for word extraction: {str(e)}")

        # If attention mechanism is not available or failed, use a simpler approach
        # based on relative positions
        if not extracted_pairs and len(source_words) > 0 and len(target_words) > 0:
            # Calculate the ratio between source and target lengths
            ratio = len(target_words) / len(source_words)

            for i, source_word in enumerate(source_words):
                # Skip very short words
                if len(source_word) < 3:
                    continue

                # Calculate the approximate position in the target text
                target_idx = min(int(i * ratio), len(target_words) - 1)
                target_word = target_words[target_idx]

                # Skip very short target words
                if len(target_word) < 3:
                    continue

                # Add the word pair to the dictionary
                self._add_learned_translation(source_word, target_word, direction)
                extracted_pairs.append((source_word, target_word))

        self.logger.info(f"Extracted {len(extracted_pairs)} word pairs from sentence")
        return extracted_pairs

    def update_single_translation(self, original_text, new_translation, source_lang, target_lang):
        """
        Update a single translation in the cache without reloading everything.
        This is much faster than reloading all translations.
        Also updates the static file immediately to ensure the translation is available to all users.

        Args:
            original_text: The source text
            new_translation: The updated translation
            source_lang: Source language code
            target_lang: Target language code
        """
        # Determine the translation direction
        if source_lang == 'tgl' and target_lang == 'ted':
            direction = 'tgl_to_ted'
        elif source_lang == 'ted' and target_lang == 'tgl':
            direction = 'ted_to_tgl'
        else:
            self.logger.warning(f"Unsupported language pair: {source_lang} to {target_lang}")
            return

        # Normalize the text
        normalized_text = original_text.lower()

        # Create translation info dictionary
        translation_info = {
            'translation': new_translation,
            'confidence': 0.99,
            'source': 'user_feedback',
            'notes': 'Updated via user feedback',
            'part_of_speech': 'phrase' if ' ' in normalized_text else '',
            'examples': [],
            'priority': True,
            'do_not_modify': True
        }

        # Update the translation in the cache
        if ' ' in normalized_text:
            # It's a phrase
            phrase_key = f"phrase:{normalized_text}"
            self.word_translations[direction][phrase_key] = translation_info
        else:
            # It's a word
            self.word_translations[direction][normalized_text] = translation_info

        # Update the cache
        cache.set(self.cache_key, self.word_translations, self.cache_timeout)

        # Clear the recent translations cache for this text
        safe_text = original_text.replace(' ', '_').replace(':', '_').replace('\n', '_')[:200]
        cache_key = f"{source_lang}_{target_lang}_{safe_text}"
        if cache_key in self.recent_translations_cache:
            # Remove from cache to force a fresh lookup
            del self.recent_translations_cache[cache_key]
            self.logger.info(f"Cleared cache for: {original_text}")

        # Also clear Django's cache for this key
        django_cache_key = f"{source_lang}_{target_lang}_{safe_text}"
        cache.delete(django_cache_key)

        # Try to update the database directly
        try:
            # Get language objects
            source_lang_obj = Language.objects.get(code=source_lang)
            target_lang_obj = Language.objects.get(code=target_lang)

            # Find or update the translation in the database
            existing = ComprehensiveTranslation.objects.filter(
                base_word__iexact=original_text,
                source_language=source_lang_obj,
                target_language=target_lang_obj
            ).first()

            if existing:
                # Update existing translation
                existing.translation = new_translation
                existing.notes = (existing.notes or '') + f'\nUpdated via update_single_translation on {timezone.now().strftime("%Y-%m-%d")}'
                existing.save()
                self.logger.info(f"Updated existing translation in database: {original_text} → {new_translation}")

                # Create a new version with high confidence
                TranslationVersion.objects.create(
                    comprehensive_translation=existing,
                    translation=new_translation,
                    confidence_score=0.99,  # High confidence for direct updates
                    is_active=True,
                    notes='Updated via update_single_translation'
                )
            else:
                # Create new translation
                new_trans = ComprehensiveTranslation.objects.create(
                    base_word=original_text,
                    translation=new_translation,
                    source_language=source_lang_obj,
                    target_language=target_lang_obj,
                    part_of_speech='phrase' if ' ' in original_text else 'word',
                    notes='Added via update_single_translation'
                )
                self.logger.info(f"Created new translation in database: {original_text} → {new_translation}")

                # Create initial version with high confidence
                TranslationVersion.objects.create(
                    comprehensive_translation=new_trans,
                    translation=new_translation,
                    confidence_score=0.99,  # High confidence for direct updates
                    is_active=True,
                    notes='Created via update_single_translation'
                )

            # Log that static file update will be deferred to the optimization script
            self.logger.info(f"Static file update deferred for translation: {original_text} → {new_translation} - will be processed by optimize_translation_system.py script")

        except Exception as e:
            self.logger.error(f"Error updating translation in database: {str(e)}")

        self.logger.info(f"Updated translation: {original_text} -> {new_translation}")

    def _add_learned_translation(self, source_text, target_text, direction):
        """Add a learned translation to the in-memory dictionary"""
        if ' ' in source_text:
            # It's a phrase
            phrase_key = f"phrase:{source_text.lower()}"
            if phrase_key not in self.word_translations[direction]:
                self.word_translations[direction][phrase_key] = {
                    'translation': target_text,
                    'part_of_speech': 'phrase',
                    'notes': 'Learned from user feedback',
                    'examples': [],
                    'priority': False,
                    'confidence': 0.8,
                    'source': 'learned'
                }
        else:
            # It's a word
            if source_text.lower() not in self.word_translations[direction]:
                self.word_translations[direction][source_text.lower()] = {
                    'translation': target_text,
                    'part_of_speech': '',
                    'notes': 'Learned from user feedback',
                    'examples': [],
                    'priority': False,
                    'confidence': 0.8,
                    'source': 'learned'
                }

    def _learn_translation(self, source_text, target_text, source_lang, target_lang, confidence=0.7):
        """
        Learn a new translation from the attention mechanism or similar word matching.
        This adds the translation to both the in-memory dictionary and the database.
        Enhanced to better handle longer sentences and phrases.

        Args:
            source_text: Source text
            target_text: Target text
            source_lang: Source language code
            target_lang: Target language code
            confidence: Confidence score for this translation
        """
        # Determine the translation direction
        if source_lang == 'tgl' and target_lang == 'ted':
            direction = 'tgl_to_ted'
        elif source_lang == 'ted' and target_lang == 'tgl':
            direction = 'ted_to_tgl'
        else:
            self.logger.warning(f"Unsupported language pair for learning: {source_lang} to {target_lang}")
            return

        # Add to in-memory dictionary
        source_lower = source_text.lower()

        # Check if this is a phrase (contains spaces)
        if ' ' in source_lower:
            # Store as a phrase with the special prefix
            phrase_key = f"phrase:{source_lower}"
            self.word_translations[direction][phrase_key] = {
                'translation': target_text,
                'confidence': confidence,
                'source': 'learned_attention',
                'notes': 'Learned from attention mechanism',
                'part_of_speech': 'phrase',
                'examples': [],
                'priority': True  # Give it priority to ensure it's used
            }
            self.logger.info(f"Learned phrase: {source_text} -> {target_text}")

            # Also learn individual words if confidence is high
            if confidence > 0.7:  # Lowered threshold to learn more words
                words = source_lower.split()
                target_words = target_text.split()

                # Only attempt word extraction if the number of words is similar
                if 0.5 <= len(words) / max(len(target_words), 1) <= 2.0:
                    for i, word in enumerate(words):
                        if word not in self.word_translations[direction] and len(word) > 2:
                            # Try to map to corresponding target word
                            target_idx = min(i, len(target_words) - 1)
                            target_word = target_words[target_idx]

                            self.word_translations[direction][word] = {
                                'translation': target_word,
                                'confidence': confidence * 0.8,  # Slightly lower confidence for extracted words
                                'source': 'extracted_from_phrase',
                                'notes': f'Extracted from phrase: {source_text}',
                                'examples': [],
                                'priority': True  # Give priority to extracted words
                            }
                            self.logger.info(f"Extracted word from phrase: {word} -> {target_word}")

                # Also learn common sub-phrases (2-word combinations)
                if len(words) >= 2:
                    for i in range(len(words) - 1):
                        sub_phrase = f"{words[i]} {words[i+1]}"
                        sub_phrase_key = f"phrase:{sub_phrase}"

                        # Only add if we don't already have this sub-phrase
                        if sub_phrase_key not in self.word_translations[direction]:
                            # Get corresponding target words if possible
                            if i+1 < len(target_words):
                                sub_target = f"{target_words[i]} {target_words[min(i+1, len(target_words)-1)]}"
                            else:
                                sub_target = target_words[-1] if target_words else target_text

                            self.word_translations[direction][sub_phrase_key] = {
                                'translation': sub_target,
                                'confidence': confidence * 0.85,  # Slightly lower confidence for extracted sub-phrases
                                'source': 'extracted_sub_phrase',
                                'notes': f'Extracted from phrase: {source_text}',
                                'examples': [],
                                'part_of_speech': 'phrase',
                                'priority': True  # Give priority to extracted sub-phrases
                            }
                            self.logger.info(f"Extracted sub-phrase: {sub_phrase} -> {sub_target}")

                # Use fuzzy matcher to learn from this phrase if available
                if FUZZY_MATCHING_ENABLED and hasattr(fuzzy_matcher, 'learn_from_phrase'):
                    try:
                        learning_result = fuzzy_matcher.learn_from_phrase(source_text, target_text, direction)
                        if learning_result and learning_result.get('learned', 0) > 0:
                            self.logger.info(f"Fuzzy matcher learned {learning_result.get('learned')} mappings from phrase: {source_text}")

                            # Update our in-memory dictionary with what the fuzzy matcher learned
                            for word, info in fuzzy_matcher.word_cache[direction].items():
                                if info.get('source') == 'learned_from_phrase' and word not in self.word_translations[direction]:
                                    self.word_translations[direction][word] = info
                    except Exception as e:
                        self.logger.error(f"Error using fuzzy matcher to learn from phrase: {str(e)}")
        else:
            # Store as a regular word
            self.word_translations[direction][source_lower] = {
                'translation': target_text,
                'confidence': confidence,
                'source': 'learned_attention',
                'notes': 'Learned from attention mechanism',
                'examples': [],
                'priority': True  # Give priority to learned words
            }

        # Add to database in a background thread to avoid blocking
        try:
            # Get language objects
            source_lang_obj = Language.objects.get(code=source_lang)
            target_lang_obj = Language.objects.get(code=target_lang)

            # Check if this translation already exists
            existing = ComprehensiveTranslation.objects.filter(
                base_word__iexact=source_text,
                source_language=source_lang_obj,
                target_language=target_lang_obj
            ).first()

            if existing:
                # Update existing translation if confidence is higher
                if confidence > 0.8:  # Only update if we're fairly confident
                    # Create a new version
                    TranslationVersion.objects.create(
                        comprehensive_translation=existing,
                        translation=target_text,
                        confidence_score=confidence,
                        is_active=True,
                        notes='Updated via attention mechanism'
                    )
                    self.logger.info(f"Updated existing translation: {source_text} -> {target_text}")
            else:
                # Create new translation if confidence is reasonable
                if confidence > 0.6:  # Only create if we have moderate confidence
                    new_translation = ComprehensiveTranslation.objects.create(
                        base_word=source_text,
                        translation=target_text,
                        source_language=source_lang_obj,
                        target_language=target_lang_obj,
                        part_of_speech='',  # Unknown
                        notes='Created via attention mechanism'
                    )

                    # Create initial version
                    TranslationVersion.objects.create(
                        comprehensive_translation=new_translation,
                        translation=target_text,
                        confidence_score=confidence,
                        is_active=True,
                        notes='Created via attention mechanism'
                    )
                    self.logger.info(f"Created new translation: {source_text} -> {target_text}")

                    # Add to learned translations file for persistence
                    self._add_to_learned_file(source_text, target_text, source_lang, target_lang)
        except Exception as e:
            self.logger.error(f"Error learning translation: {str(e)}")

    def _add_to_learned_file(self, source_text, target_text, source_lang, target_lang):
        """
        Add a learned translation to the learned_translations.txt file.

        Args:
            source_text: Source text
            target_text: Target text
            source_lang: Source language code
            target_lang: Target language code
        """
        try:
            learned_path = os.path.join('data', 'learned_translations.txt')
            with open(learned_path, 'a', encoding='utf-8') as f:
                f.write(f"{source_lang}|{source_text}|{target_lang}|{target_text}\n")
        except Exception as e:
            self.logger.error(f"Error adding to learned file: {str(e)}")

    def _extract_phrase_mappings(self, source_text, target_text, direction):
        """Extract phrase mappings from parallel texts"""
        # This is a simplified implementation
        # In a production system, you would use more sophisticated alignment algorithms
        source_words = source_text.split()
        target_words = target_text.split()

        # Only attempt phrase extraction if the number of words is similar
        if 0.5 <= len(source_words) / len(target_words) <= 2.0:
            # Extract phrases (2-3 words)
            for i in range(len(source_words) - 1):
                if i < len(source_words) - 2:
                    # 3-word phrase
                    source_phrase = ' '.join(source_words[i:i+3])
                    if len(source_phrase) > 5:  # Only consider phrases with more than 5 characters
                        phrase_key = f"phrase:{source_phrase.lower()}"
                        if phrase_key not in self.word_translations[direction]:
                            # This is just a placeholder - in a real system you would use alignment algorithms
                            target_phrase = ' '.join(target_words[min(i, len(target_words)-3):min(i+3, len(target_words))])
                            self.word_translations[direction][phrase_key] = {
                                'translation': target_phrase,
                                'part_of_speech': 'phrase',
                                'notes': 'Extracted from parallel texts',
                                'examples': [],
                                'priority': False,
                                'confidence': 0.6,
                                'source': 'extracted'
                            }

                # 2-word phrase
                source_phrase = ' '.join(source_words[i:i+2])
                if len(source_phrase) > 4:  # Only consider phrases with more than 4 characters
                    phrase_key = f"phrase:{source_phrase.lower()}"
                    if phrase_key not in self.word_translations[direction]:
                        # This is just a placeholder - in a real system you would use alignment algorithms
                        target_phrase = ' '.join(target_words[min(i, len(target_words)-2):min(i+2, len(target_words))])
                        self.word_translations[direction][phrase_key] = {
                            'translation': target_phrase,
                            'part_of_speech': 'phrase',
                            'notes': 'Extracted from parallel texts',
                            'examples': [],
                            'priority': False,
                            'confidence': 0.6,
                            'source': 'extracted'
                        }

    def translate_text(self, text, source_lang, target_lang):
        """
        Translate text from source language to target language.
        Optimized to prioritize database lookups for the most up-to-date translations.
        Now also uses Hugging Face and attention mechanism for better translations.

        Args:
            text: Text to translate
            source_lang: Source language code
            target_lang: Target language code

        Returns:
            Dictionary with translation information
        """
        # Use a thread lock to prevent concurrent database access
        with threading.RLock():
            # Log the translation request for debugging
            self.logger.info(f"Translating: '{text}' from {source_lang} to {target_lang}")

            # Special case for "Yumuko ka" - hardcoded fix
            if text.lower() == "yumuko ka" and source_lang == 'tgl' and target_lang == 'ted':
                self.logger.info("Using hardcoded translation for 'Yumuko ka'")
                result = {
                    'original': text,
                    'translation': "Mëntëdungul go",
                    'confidence': 0.99,
                    'source': 'hardcoded_fix',
                    'word_by_word': [{
                        'original': text,
                        'translation': "Mëntëdungul go",
                        'confidence': 0.99,
                        'source': 'hardcoded_fix',
                        'part_of_speech': 'phrase',
                        'notes': 'Fixed via hardcoded translation'
                    }]
                }

                # Store in cache
                safe_text = text.replace(' ', '_').replace(':', '_').replace('\n', '_')[:200]
                cache_key = f"{source_lang}_{target_lang}_{safe_text}"
                self.recent_translations_cache[cache_key] = result

                # Also update the database with this translation
                try:
                    # Get language objects
                    source_lang_obj = Language.objects.get(code=source_lang)
                    target_lang_obj = Language.objects.get(code=target_lang)

                    # Find or create the translation in the database
                    trans, created = ComprehensiveTranslation.objects.get_or_create(
                        base_word="Yumuko ka",
                        source_language=source_lang_obj,
                        target_language=target_lang_obj,
                        defaults={
                            'translation': "Mëntëdungul go",
                            'part_of_speech': 'phrase',
                            'notes': 'Added via hardcoded fix'
                        }
                    )

                    if not created:
                        # Update existing translation
                        trans.translation = "Mëntëdungul go"
                        trans.save()

                        # Deactivate all existing versions
                        for version in trans.versions.all():
                            version.is_active = False
                            version.save()

                    # Create a new version with very high confidence
                    TranslationVersion.objects.create(
                        comprehensive_translation=trans,
                        translation="Mëntëdungul go",
                        confidence_score=0.99,
                        is_active=True,
                        notes='Created via hardcoded fix'
                    )

                    self.logger.info("Updated database with hardcoded translation for 'Yumuko ka'")
                except Exception as e:
                    self.logger.error(f"Error updating database with hardcoded translation: {str(e)}")

                return result

            # First, try to get the translation from the database
            try:
                # Get language objects
                source_lang_obj = Language.objects.get(code=source_lang)
                target_lang_obj = Language.objects.get(code=target_lang)

                # Look for exact match in ComprehensiveTranslation
                exact_match = ComprehensiveTranslation.objects.filter(
                    base_word__iexact=text,
                    source_language=source_lang_obj,
                    target_language=target_lang_obj
                ).select_related(
                    'source_language', 'target_language'
                ).prefetch_related(
                    'versions'
                ).first()

                if exact_match:
                    # Get the active version if available
                    active_version = None
                    try:
                        active_version = next((v for v in exact_match.versions.all() if v.is_active), None)
                    except Exception:
                        pass

                    translation_text = active_version.translation if active_version else exact_match.translation
                    confidence_score = active_version.confidence_score if active_version else 0.95

                    # Create result with database translation
                    result = {
                        'original': text,
                        'translation': translation_text,
                        'confidence': confidence_score,
                        'source': 'database',
                        'word_by_word': [{
                            'original': text,
                            'translation': translation_text,
                            'confidence': confidence_score,
                            'source': 'database',
                            'part_of_speech': exact_match.part_of_speech,
                            'notes': exact_match.notes
                        }]
                    }

                    # Store in cache
                    safe_text = text.replace(' ', '_').replace(':', '_').replace('\n', '_')[:200]
                    cache_key = f"{source_lang}_{target_lang}_{safe_text}"
                    self.recent_translations_cache[cache_key] = result

                    self.logger.info(f"Found exact match in database: {text} → {translation_text}")
                    return result
            except Exception as e:
                self.logger.error(f"Error querying database for exact match: {str(e)}")

            # If not found in database, try AI translation with Hugging Face
            from django.conf import settings
            huggingface_enabled = getattr(settings, 'HUGGINGFACE_ENABLED', False)

            if huggingface_enabled:
                try:
                    # Try to use Hugging Face for translation
                    self.logger.info(f"Using Hugging Face for translation: {text}")
                    ai_result = self.translate_with_ai(text, source_lang, target_lang)

                    # If we got a good result, return it
                    if ai_result and ai_result.get('translation') != text and ai_result.get('confidence', 0) > 0.5:
                        self.logger.info(f"Using AI translation: {text} → {ai_result.get('translation')}")
                        return ai_result
                except Exception as e:
                    self.logger.error(f"Error using Hugging Face for translation: {str(e)}")

            # Check for word-by-word translations
            # (This comment replaces the duplicate hardcoded fix)

            # Always check the database first for the most up-to-date translations
            # This ensures we're using the latest data even if we've done a complete load
            try:
                # Get language objects
                source_lang_obj = Language.objects.get(code=source_lang)
                target_lang_obj = Language.objects.get(code=target_lang)

                # Look for exact match in ComprehensiveTranslation
                exact_match = ComprehensiveTranslation.objects.filter(
                    base_word__iexact=text,
                    source_language=source_lang_obj,
                    target_language=target_lang_obj
                ).select_related(
                    'source_language', 'target_language'
                ).prefetch_related(
                    'versions'
                ).first()

                if exact_match:
                    # Get the active version if available - using prefetched data
                    active_version = None
                    try:
                        active_version = next((v for v in exact_match.versions.all() if v.is_active), None)
                    except Exception:
                        pass

                    translation_text = active_version.translation if active_version else exact_match.translation
                    confidence_score = active_version.confidence_score if active_version else 0.95

                    # Create result with database translation
                    result = {
                        'original': text,
                        'translation': translation_text,
                        'confidence': confidence_score,
                        'source': 'database',
                        'word_by_word': [{
                            'original': text,
                            'translation': translation_text,
                            'confidence': confidence_score,
                            'source': 'database',
                            'part_of_speech': exact_match.part_of_speech,
                            'notes': exact_match.notes
                        }]
                    }

                    # Store in cache
                    safe_text = text.replace(' ', '_').replace(':', '_').replace('\n', '_')[:200]
                    cache_key = f"{source_lang}_{target_lang}_{safe_text}"
                    self.recent_translations_cache[cache_key] = result

                    # Also update in-memory dictionary for future use
                    normalized_text = text.lower()
                    if source_lang == 'tgl' and target_lang == 'ted':
                        direction = 'tgl_to_ted'
                    elif source_lang == 'ted' and target_lang == 'tgl':
                        direction = 'ted_to_tgl'
                    else:
                        direction = ''

                    if direction:
                        self.word_translations[direction][normalized_text] = {
                            'translation': translation_text,
                            'part_of_speech': exact_match.part_of_speech,
                            'notes': exact_match.notes,
                            'cultural_notes': exact_match.cultural_notes,
                            'examples': [],
                            'priority': True,
                            'confidence': confidence_score,
                            'source': 'database',
                            'version_id': active_version.id if active_version else None
                        }

                    self.logger.info(f"Found exact match in database: {text} -> {translation_text}")
                    return result
            except Exception as e:
                self.logger.error(f"Error querying database for exact match: {str(e)}")

            # Now check if we have this translation in our recent translations cache
            safe_text = text.replace(' ', '_').replace(':', '_').replace('\n', '_')[:200]
            cache_key = f"{source_lang}_{target_lang}_{safe_text}"
            if cache_key in self.recent_translations_cache:
                self.logger.info(f"Using cached translation for: {text}")
                cached_result = self.recent_translations_cache[cache_key]

                # Add BLEU score if not present
                if 'bleu_score' not in cached_result and len(text.split()) > 1:
                    try:
                        if BLEU_SCORE_ENABLED and hasattr(bleu_calculator, 'calculate_bleu'):
                            bleu = bleu_calculator.calculate_bleu(
                                text,
                                cached_result['translation'],
                                source_lang,
                                target_lang
                            )
                            cached_result['bleu_score'] = bleu
                    except Exception as e:
                        self.logger.error(f"Error calculating BLEU score: {str(e)}")

                return cached_result

            # Enhanced path for all words - prioritize database access for better knowledge base utilization
            # This ensures we're always using the most up-to-date translations from the database
            normalized_text = text.lower()

            # Determine the translation direction
            if source_lang == 'tgl' and target_lang == 'ted':
                direction = 'tgl_to_ted'
            elif source_lang == 'ted' and target_lang == 'tgl':
                direction = 'ted_to_tgl'
            else:
                return {
                    'original': text,
                    'translation': text,
                    'confidence': 0.0,
                    'source': 'unsupported',
                    'word_by_word': []
                }

            # If not found in database, check in-memory dictionary
            if normalized_text in self.word_translations[direction]:
                word_info = self.word_translations[direction][normalized_text]
                result = {
                    'original': text,
                    'translation': word_info['translation'],
                    'confidence': word_info.get('confidence', 0.9),
                    'source': word_info.get('source', 'dictionary'),
                    'word_by_word': [{
                        'original': text,
                        'translation': word_info['translation'],
                        'confidence': word_info.get('confidence', 0.9),
                        'source': word_info.get('source', 'dictionary'),
                        'part_of_speech': word_info.get('part_of_speech', ''),
                        'notes': word_info.get('notes', '')
                    }]
                }

                # Store in cache
                self.recent_translations_cache[cache_key] = result
                self.logger.info(f"Found in memory dictionary: {text} -> {word_info['translation']}")
                return result

            # Check for phrase match
            phrase_key = f"phrase:{normalized_text}"
            if phrase_key in self.word_translations[direction]:
                phrase_info = self.word_translations[direction][phrase_key]
                result = {
                    'original': text,
                    'translation': phrase_info['translation'],
                    'confidence': phrase_info.get('confidence', 0.9),
                    'source': phrase_info.get('source', 'dictionary_phrase'),
                    'word_by_word': [{
                        'original': text,
                        'translation': phrase_info['translation'],
                        'confidence': phrase_info.get('confidence', 0.9),
                        'source': phrase_info.get('source', 'dictionary_phrase'),
                        'part_of_speech': phrase_info.get('part_of_speech', ''),
                        'notes': phrase_info.get('notes', '')
                    }]
                }

                # Store in cache
                self.recent_translations_cache[cache_key] = result
                self.logger.info(f"Found phrase in memory dictionary: {text} -> {phrase_info['translation']}")
                return result

            # If no exact match in database, check if we have this word in our dictionary
            if len(text.split()) == 1 and normalized_text in self.word_translations[direction]:
                word_info = self.word_translations[direction][normalized_text]
                result = {
                    'original': text,
                    'translation': word_info['translation'],
                    'confidence': word_info.get('confidence', 0.9),
                    'source': word_info.get('source', 'dictionary'),
                    'word_by_word': [{
                        'original': text,
                        'translation': word_info['translation'],
                        'confidence': word_info.get('confidence', 0.9),
                        'source': word_info.get('source', 'dictionary'),
                        'part_of_speech': word_info.get('part_of_speech', ''),
                        'notes': word_info.get('notes', '')
                    }]
                }

                # Store in cache
                self.recent_translations_cache[cache_key] = result
                return result

            # Special handling for single words - try harder to find a match with fuzzy matching
            if len(text.split()) == 1 and FUZZY_MATCHING_ENABLED:
                try:
                    # For single words, we'll try a more aggressive fuzzy matching approach
                    matched_word, word_info, similarity = fuzzy_matcher.find_best_match(normalized_text, direction)
                    if matched_word and word_info and similarity >= 0.6:  # Lower threshold for single words
                        self.logger.info(f"Single word fuzzy match: '{text}' matched with '{matched_word}' (similarity: {similarity:.2f})")

                        result = {
                            'original': text,
                            'translation': word_info['translation'],
                            'confidence': similarity * 0.9,  # Adjust confidence based on similarity
                            'source': 'single_word_fuzzy_match',
                            'part_of_speech': word_info.get('part_of_speech', ''),
                            'notes': f"Matched with '{matched_word}' (similarity: {similarity:.2f})",
                            'word_by_word': [{
                                'original': text,
                                'translation': word_info['translation'],
                                'confidence': similarity * 0.9,
                                'source': 'single_word_fuzzy_match',
                                'part_of_speech': word_info.get('part_of_speech', ''),
                                'notes': f"Matched with '{matched_word}' (similarity: {similarity:.2f})"
                            }]
                        }

                        # Store in cache
                        self.recent_translations_cache[cache_key] = result

                        # Learn this translation for future use
                        self._learn_translation(text, word_info['translation'], source_lang, target_lang, similarity * 0.9)

                        return result
                except Exception as e:
                    self.logger.error(f"Error in single word fuzzy matching: {str(e)}")

            # For unfamiliar words, try to find similar words
            # If we've done a complete load, we'll only look in memory
            if self.complete_load_done:
                # Find similar words in memory
                similar_words = []
                prefix = normalized_text[:3].lower()

                # Look for words that start with the same prefix
                for word, info in self.word_translations[direction].items():
                    if word.startswith(prefix) and word != normalized_text:
                        similar_words.append({
                            'word': word,
                            'translation': info['translation'],
                            'part_of_speech': info.get('part_of_speech', ''),
                            'notes': info.get('notes', '')
                        })

                        # Limit to 5 similar words
                        if len(similar_words) >= 5:
                            break

                if similar_words:
                    # Use the first similar word
                    similar_word = similar_words[0]

                    # Apply attention mechanism if available
                    attention_score = 0.7  # Default score
                    if ATTENTION_MECHANISM_ENABLED and hasattr(attention_mechanism, 'calculate_similarity'):
                        try:
                            attention_score = attention_mechanism.calculate_similarity(
                                normalized_text, similar_word['word']
                            )
                        except Exception as e:
                            self.logger.error(f"Error calculating attention similarity: {str(e)}")

                    # Only use if similarity is above threshold
                    if attention_score > 0.5:
                        result = {
                            'original': text,
                            'translation': similar_word['translation'],
                            'confidence': attention_score,
                            'source': 'similar_word_memory',
                            'word_by_word': [{
                                'original': text,
                                'translation': similar_word['translation'],
                                'confidence': attention_score,
                                'source': 'similar_word_memory',
                                'part_of_speech': similar_word['part_of_speech'],
                                'notes': f"Similar to '{similar_word['word']}'"
                            }]
                        }

                        # Store in cache
                        self.recent_translations_cache[cache_key] = result

                        # Learn this translation for future use
                        self._learn_translation(text, similar_word['translation'], source_lang, target_lang, attention_score)

                        return result
            else:
                # If we haven't done a complete load, try to find similar words in the database
                try:
                    # Get language objects (we already have them from earlier, but just in case)
                    source_lang_obj = Language.objects.get(code=source_lang)
                    target_lang_obj = Language.objects.get(code=target_lang)

                    # Find similar words in the database
                    similar_words = ComprehensiveTranslation.objects.filter(
                        source_language=source_lang_obj,
                        target_language=target_lang_obj,
                        base_word__icontains=normalized_text[:3]  # Match first few characters for speed
                    ).order_by('-updated_at')[:5]

                    if similar_words.exists():
                        # Use the most recent similar word
                        similar_word = similar_words.first()

                        # Apply attention mechanism if available
                        attention_score = 0.7  # Default score
                        if ATTENTION_MECHANISM_ENABLED and hasattr(attention_mechanism, 'calculate_similarity'):
                            try:
                                attention_score = attention_mechanism.calculate_similarity(
                                    normalized_text, similar_word.base_word
                                )
                            except Exception as e:
                                self.logger.error(f"Error calculating attention similarity: {str(e)}")

                        # Only use if similarity is above threshold
                        if attention_score > 0.5:
                            result = {
                                'original': text,
                                'translation': similar_word.translation,
                                'confidence': attention_score,
                                'source': 'similar_word',
                                'word_by_word': [{
                                    'original': text,
                                    'translation': similar_word.translation,
                                    'confidence': attention_score,
                                    'source': 'similar_word',
                                    'part_of_speech': similar_word.part_of_speech,
                                    'notes': f"Similar to '{similar_word.base_word}'"
                                }]
                            }

                            # Store in cache
                            self.recent_translations_cache[cache_key] = result

                            # Learn this translation for future use
                            self._learn_translation(text, similar_word.translation, source_lang, target_lang, attention_score)

                            return result
                except Exception as e:
                    self.logger.error(f"Error finding similar words in database: {str(e)}")

        # Determine the translation direction
        if source_lang == 'tgl' and target_lang == 'ted':
            direction = 'tgl_to_ted'
        elif source_lang == 'ted' and target_lang == 'tgl':
            direction = 'ted_to_tgl'
        else:
            return {
                'original': text,
                'translation': text,
                'confidence': 0.0,
                'source': 'unsupported',
                'word_by_word': []
            }

        # Import text utilities
        from translation_app.text_utils import normalize_for_lookup, apply_punctuation, preserve_capitalization, align_words

        # Check if we're still loading translations
        if self.is_loading:
            self.logger.info("Translations are still loading, using partial data")

        # Normalize the text for lookup
        normalized_text = normalize_for_lookup(text)

        # Check for exact phrase match first
        phrase_key = f"phrase:{normalized_text.lower()}"
        if phrase_key in self.word_translations[direction]:
            phrase_info = self.word_translations[direction][phrase_key]
            translation = phrase_info['translation']

            # Apply punctuation and capitalization
            translation = apply_punctuation(translation, text)
            translation = preserve_capitalization(translation, text)

            result = {
                'original': text,
                'translation': translation,
                'confidence': phrase_info.get('confidence', 0.95),
                'source': phrase_info.get('source', 'phrase'),
                'notes': phrase_info.get('notes', 'Exact phrase match'),
                'word_by_word': []  # No word-by-word for exact phrases
            }

            # Store in recent translations cache
            self.recent_translations_cache[cache_key] = result

            # If the cache is too large, remove the oldest entries
            if len(self.recent_translations_cache) > self.recent_translations_max_size:
                # Remove 10% of the oldest entries
                keys_to_remove = list(self.recent_translations_cache.keys())[:int(self.recent_translations_max_size * 0.1)]
                for key in keys_to_remove:
                    del self.recent_translations_cache[key]

            return result

        # Split the text into words
        words = normalized_text.split()

        # Try to translate each word
        translated_words = []
        word_by_word = []
        confidence_sum = 0
        confidence_count = 0

        for word in words:
            word_lower = word.lower()

            # Check if we have a translation for this word
            if word_lower in self.word_translations[direction]:
                word_info = self.word_translations[direction][word_lower]
                translated_word = word_info['translation']
                confidence = word_info.get('confidence', 0.9)
                source = word_info.get('source', 'dictionary')

                translated_words.append(translated_word)
                word_by_word.append({
                    'original': word,
                    'translation': translated_word,
                    'confidence': confidence,
                    'source': source,
                    'part_of_speech': word_info.get('part_of_speech', ''),
                    'notes': word_info.get('notes', '')
                })

                confidence_sum += confidence
                confidence_count += 1
            else:
                # No exact translation found, try fuzzy matching
                if FUZZY_MATCHING_ENABLED and hasattr(fuzzy_matcher, 'find_best_match'):
                    try:
                        matched_word, word_info, similarity = fuzzy_matcher.find_best_match(word_lower, direction)

                        if matched_word and word_info and similarity >= 0.7:  # Use threshold for quality
                            # We found a fuzzy match
                            translated_word = word_info['translation']
                            confidence = similarity * 0.9  # Adjust confidence based on similarity

                            translated_words.append(translated_word)
                            word_by_word.append({
                                'original': word,
                                'translation': translated_word,
                                'confidence': confidence,
                                'source': 'fuzzy_match',
                                'part_of_speech': word_info.get('part_of_speech', ''),
                                'notes': f"Fuzzy matched with '{matched_word}' (similarity: {similarity:.2f})"
                            })

                            confidence_sum += confidence
                            confidence_count += 1

                            # Learn this match for future use if it's good
                            if similarity > 0.8:
                                self.word_translations[direction][word_lower] = {
                                    'translation': translated_word,
                                    'part_of_speech': word_info.get('part_of_speech', ''),
                                    'notes': f"Learned from fuzzy match with '{matched_word}'",
                                    'examples': [],
                                    'priority': False,
                                    'confidence': confidence,
                                    'source': 'fuzzy_learned'
                                }

                            continue
                    except Exception as e:
                        self.logger.error(f"Error in fuzzy matching: {str(e)}")

                # If we get here, no translation was found (even with fuzzy matching)
                translated_words.append(word)
                word_by_word.append({
                    'original': word,
                    'translation': word,
                    'confidence': 0.0,
                    'source': 'unknown',
                    'part_of_speech': '',
                    'notes': 'No translation found'
                })

        # Calculate average confidence
        avg_confidence = confidence_sum / max(confidence_count, 1)

        # Join the translated words
        raw_translation = ' '.join(translated_words)

        # Apply pattern-based transformations if available
        if PATTERN_LEARNING_ENABLED and hasattr(apply_patterns, 'apply_patterns_to_translation'):
            try:
                pattern_translation = apply_patterns.apply_patterns_to_translation(
                    normalized_text, raw_translation, source_lang, target_lang
                )
                if pattern_translation and pattern_translation != raw_translation:
                    self.logger.info(f"Applied patterns: {raw_translation} -> {pattern_translation}")
                    raw_translation = pattern_translation
                    avg_confidence = min(avg_confidence + 0.1, 1.0)  # Increase confidence slightly
            except Exception as e:
                self.logger.error(f"Error applying patterns: {str(e)}")

        # Apply phonological rules if available
        if PHONOLOGICAL_RULES_ENABLED:
            try:
                # Try with target_lang parameter first
                try:
                    phonological_translation = apply_phonological_rules(raw_translation, target_lang)
                    if phonological_translation and phonological_translation != raw_translation:
                        self.logger.info(f"Applied phonological rules: {raw_translation} -> {phonological_translation}")
                        raw_translation = phonological_translation
                except TypeError:
                    # Fall back to single parameter if the function doesn't accept two parameters
                    phonological_translation = apply_phonological_rules(raw_translation)
                    if phonological_translation and phonological_translation != raw_translation:
                        self.logger.info(f"Applied phonological rules (fallback): {raw_translation} -> {phonological_translation}")
                        raw_translation = phonological_translation
            except Exception as e:
                self.logger.error(f"Error applying phonological rules: {str(e)}")

        # Apply punctuation and capitalization
        translation = apply_punctuation(raw_translation, text)
        translation = preserve_capitalization(translation, text)

        # Apply attention mechanism if available
        attention_data = None
        if ATTENTION_MECHANISM_ENABLED and hasattr(attention_mechanism, 'calculate_attention'):
            try:
                attention_data = attention_mechanism.calculate_attention(
                    normalized_text.split(), translation.split(), word_by_word
                )
            except Exception as e:
                self.logger.error(f"Error calculating attention: {str(e)}")

        # Calculate BLEU score if available
        bleu_score = None
        if BLEU_SCORE_ENABLED and hasattr(bleu_calculator, 'calculate_bleu') and len(text.split()) > 1:
            try:
                bleu_score = bleu_calculator.calculate_bleu(text, translation, source_lang, target_lang)
                self.logger.info(f"BLEU score for translation: {bleu_score}")
            except Exception as e:
                self.logger.error(f"Error calculating BLEU score: {str(e)}")

        # Create the result
        result = {
            'original': text,
            'translation': translation,
            'confidence': avg_confidence,
            'source': 'word_by_word',
            'word_by_word': word_by_word,
            'attention_data': attention_data,
            'bleu_score': bleu_score
        }

        # If this is a good translation (high confidence or BLEU score), learn it
        if avg_confidence > 0.7 or (bleu_score and bleu_score > 0.5):
            self._learn_translation(text, translation, source_lang, target_lang, avg_confidence)

        # Store in recent translations cache
        self.recent_translations_cache[cache_key] = result

        # If the cache is too large, remove the oldest entries
        if len(self.recent_translations_cache) > self.recent_translations_max_size:
            # Remove 10% of the oldest entries
            keys_to_remove = list(self.recent_translations_cache.keys())[:int(self.recent_translations_max_size * 0.1)]
            for key in keys_to_remove:
                del self.recent_translations_cache[key]

        return result

    def translate_with_ai(self, text, source_lang, target_lang):
        """
        Translate text using Hugging Face, database, and attention mechanism.
        This method tries to use all available AI features for the best translation.

        Args:
            text: Text to translate
            source_lang: Source language code
            target_lang: Target language code

        Returns:
            Dictionary with translation information
        """
        # Check if Hugging Face is enabled
        from django.conf import settings
        huggingface_enabled = getattr(settings, 'HUGGINGFACE_ENABLED', False)
        attention_enabled = getattr(settings, 'ATTENTION_MECHANISM_ENABLED', False)

        if huggingface_enabled:
            self.logger.info(f"Using Hugging Face for AI translation: {text}")
        else:
            self.logger.info("Hugging Face integration is disabled. Using database and attention mechanism.")

        # Check if we have this translation in our recent translations cache
        safe_text = text.replace(' ', '_').replace(':', '_').replace('\n', '_')[:200]
        cache_key = f"{source_lang}_{target_lang}_{safe_text}_ai"
        if cache_key in self.recent_translations_cache:
            self.logger.info(f"Using cached AI translation for: {text}")
            return self.recent_translations_cache[cache_key]

        # Try to use Hugging Face if available
        if huggingface_enabled:
            try:
                # Import Hugging Face module
                from translation_app.huggingface_translation import HuggingFaceTranslator

                # Get the Hugging Face translator
                hf_translator = HuggingFaceTranslator()

                # Check if it's available
                if hf_translator.is_available:
                    # Get context examples for better translation
                    context_examples = self._get_similar_examples(text, source_lang, target_lang)

                    # Translate with Hugging Face
                    hf_result = hf_translator.translate(text, source_lang, target_lang, context_examples)

                    # If we got a good result, use it
                    if hf_result and hf_result.get('translation') != text and hf_result.get('confidence', 0) > 0.5:
                        self.logger.info(f"Hugging Face translation: {text} → {hf_result.get('translation')}")

                        # Store in cache
                        self.recent_translations_cache[cache_key] = hf_result

                        # Learn this translation for future use
                        self._learn_translation(
                            text,
                            hf_result['translation'],
                            source_lang,
                            target_lang,
                            hf_result.get('confidence', 0.7)
                        )

                        return hf_result
            except Exception as e:
                self.logger.error(f"Error using Hugging Face for translation: {str(e)}")

        # Check for exact phrase match first
        normalized_text = text.lower()
        phrase_key = f"phrase:{normalized_text}"
        if phrase_key in self.word_translations.get(f"{source_lang}_to_{target_lang}", {}):
            phrase_info = self.word_translations[f"{source_lang}_to_{target_lang}"][phrase_key]
            translation = phrase_info['translation']

            result = {
                'original': text,
                'translation': translation,
                'confidence': phrase_info.get('confidence', 0.95),
                'source': phrase_info.get('source', 'phrase'),
                'notes': phrase_info.get('notes', 'Exact phrase match'),
                'word_by_word': []  # No word-by-word for exact phrases
            }

            # Store in cache
            self.recent_translations_cache[cache_key] = result
            return result

        # Find similar examples from the database
        context_examples = []
        try:
            source_lang_obj = Language.objects.get(code=source_lang)
            target_lang_obj = Language.objects.get(code=target_lang)

            # Find similar translations in the database
            similar_texts = ComprehensiveTranslation.objects.filter(
                source_language=source_lang_obj,
                target_language=target_lang_obj
            ).order_by('-created_at')[:5]

            for similar in similar_texts:
                context_examples.append({
                    'source': similar.base_word,
                    'target': similar.translation
                })

            # If we found similar examples, use the most similar one as a fallback
            if context_examples:
                # Use the first (most recent) example as a fallback
                fallback_translation = context_examples[0]['target']

                # Create a result with lower confidence
                result = {
                    'original': text,
                    'translation': fallback_translation,
                    'confidence': 0.6,  # Lower confidence since this is a fallback
                    'source': 'database_fallback',
                    'notes': 'Similar translation from database',
                    'word_by_word': []
                }

                # Store in cache
                self.recent_translations_cache[cache_key] = result
                return result

        except Exception as e:
            self.logger.error(f"Error finding similar examples: {str(e)}")

        # If we get here, we couldn't find a good translation
        # Fall back to standard translation
        self.logger.info("No AI or similar translation found. Falling back to standard translation")
        return self.translate_text(text, source_lang, target_lang)

    def get_word_info(self, word, source_lang, target_lang):
        """
        Get detailed information about a word.

        Args:
            word: Word to look up
            source_lang: Source language code
            target_lang: Target language code

        Returns:
            Dictionary with word information
        """
        # Determine the translation direction
        if source_lang == 'tgl' and target_lang == 'ted':
            direction = 'tgl_to_ted'
        elif source_lang == 'ted' and target_lang == 'tgl':
            direction = 'ted_to_tgl'
        else:
            return None

        # Normalize the word
        word_lower = word.lower()

        # Check if we have information about this word
        if word_lower in self.word_translations[direction]:
            word_info = self.word_translations[direction][word_lower].copy()
            word_info['word'] = word
            return word_info

        return None

    def get_phrase_info(self, phrase, source_lang, target_lang):
        """
        Get detailed information about a phrase.

        Args:
            phrase: Phrase to look up
            source_lang: Source language code
            target_lang: Target language code

        Returns:
            Dictionary with phrase information
        """
        # Determine the translation direction
        if source_lang == 'tgl' and target_lang == 'ted':
            direction = 'tgl_to_ted'
        elif source_lang == 'ted' and target_lang == 'tgl':
            direction = 'ted_to_tgl'
        else:
            return None

        # Normalize the phrase
        phrase_lower = phrase.lower()
        phrase_key = f"phrase:{phrase_lower}"

        # Check if we have information about this phrase
        if phrase_key in self.word_translations[direction]:
            phrase_info = self.word_translations[direction][phrase_key].copy()
            phrase_info['phrase'] = phrase
            return phrase_info

        return None

    def _add_learned_translation(self, source_text, translation_text, direction):
        """
        Add a learned translation to the in-memory dictionary.
        This is a public method that can be called from outside the class.

        Args:
            source_text: Source text
            translation_text: Translation text
            direction: Translation direction (e.g., 'tgl_to_ted')

        Returns:
            None
        """
        if not source_text or not translation_text or not direction:
            return

        # Normalize the source text
        source_lower = source_text.lower()

        # Determine if this is a single word or a phrase
        is_phrase = len(source_text.split()) > 1

        # Create the key
        key = f"phrase:{source_lower}" if is_phrase else source_lower

        # Add to the dictionary
        if direction not in self.word_translations:
            self.word_translations[direction] = {}

        self.word_translations[direction][key] = {
            'translation': translation_text,
            'part_of_speech': '',
            'notes': 'Added from external source',
            'cultural_notes': '',
            'examples': [],
            'priority': True,
            'confidence': 0.9,
            'source': 'learned',
            'version_id': None
        }

        self.logger.info(f"Added learned translation to memory: {source_text} -> {translation_text} ({direction})")

    def update_single_translation(self, source_text, translation_text, source_lang, target_lang):
        """
        Update a single translation in both the database and in-memory dictionary.
        Prioritizes database updates to ensure translations are persisted.

        Args:
            source_text: Source text
            translation_text: Translation text
            source_lang: Source language code
            target_lang: Target language code

        Returns:
            None
        """
        # Determine the translation direction
        if source_lang == 'tgl' and target_lang == 'ted':
            direction = 'tgl_to_ted'
        elif source_lang == 'ted' and target_lang == 'tgl':
            direction = 'ted_to_tgl'
        else:
            return

        # IMPROVED: Update the database first to ensure persistence
        try:
            # Get language objects
            source_lang_obj = Language.objects.get(code=source_lang)
            target_lang_obj = Language.objects.get(code=target_lang)

            # Check if this translation already exists
            existing = ComprehensiveTranslation.objects.filter(
                base_word__iexact=source_text,
                source_language=source_lang_obj,
                target_language=target_lang_obj
            ).first()

            if existing:
                # Update existing translation
                existing.translation = translation_text
                existing.notes = (existing.notes or '') + f'\nUpdated via feedback on {timezone.now().strftime("%Y-%m-%d")}'
                existing.save()
                self.logger.info(f"Updated existing translation in database: {source_text} → {translation_text}")

                # Create a new version with high confidence
                TranslationVersion.objects.create(
                    comprehensive_translation=existing,
                    translation=translation_text,
                    confidence_score=0.95,  # High confidence for user feedback
                    is_active=True,
                    notes='Updated via user feedback'
                )
            else:
                # Create new translation
                new_translation = ComprehensiveTranslation.objects.create(
                    base_word=source_text,
                    translation=translation_text,
                    source_language=source_lang_obj,
                    target_language=target_lang_obj,
                    part_of_speech='phrase' if ' ' in source_text else 'word',
                    notes='Added via user feedback'
                )
                self.logger.info(f"Created new translation in database: {source_text} → {translation_text}")

                # Create initial version with high confidence
                TranslationVersion.objects.create(
                    comprehensive_translation=new_translation,
                    translation=translation_text,
                    confidence_score=0.95,  # High confidence for user feedback
                    is_active=True,
                    notes='Created via user feedback'
                )
        except Exception as e:
            self.logger.error(f"Error updating translation in database: {str(e)}")

        # Add the translation to in-memory dictionary
        self._add_learned_translation(source_text, translation_text, direction)

        # Also invalidate the cache for this translation
        safe_text = source_text.replace(' ', '_').replace(':', '_').replace('\n', '_')[:200]
        cache_key = f"{source_lang}_{target_lang}_{safe_text}"
        if cache_key in self.recent_translations_cache:
            del self.recent_translations_cache[cache_key]
            self.logger.info(f"Invalidated cache for: {source_text}")

        # Update the static file with the new translation (lower priority)
        self._update_static_file_with_translation(source_text, translation_text, direction)

    def _update_static_file_with_translation(self, source_text, translation_text, direction):
        """
        Update a single translation in the static file without reloading everything.
        This avoids database locks by only updating one translation at a time.

        Args:
            source_text: Source text
            translation_text: Translation text
            direction: Translation direction ('tgl_to_ted' or 'ted_to_tgl')

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            import os
            import json
            from django.conf import settings

            # Get the static file path
            static_dir = os.path.join(settings.BASE_DIR, 'static', 'translations')
            os.makedirs(static_dir, exist_ok=True)

            file_path = os.path.join(static_dir, f"{direction}.json")

            # Load existing translations if the file exists
            existing_translations = {}
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        existing_translations = json.load(f)
                except Exception as e:
                    self.logger.error(f"Error loading existing translations: {str(e)}")
                    # Continue with empty dict if file can't be loaded

            # Determine if this is a phrase or word
            is_phrase = ' ' in source_text

            # Create the key
            key = f"phrase:{source_text.lower()}" if is_phrase else source_text.lower()

            # Update or add the translation
            existing_translations[key] = {
                'translation': translation_text,
                'part_of_speech': 'phrase' if is_phrase else 'word',
                'notes': 'Updated via direct update',
                'cultural_notes': '',
                'examples': [],
                'priority': True,
                'confidence': 0.99,
                'source': 'updated',
                'version_id': None
            }

            # Save the updated translations
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(existing_translations, f, ensure_ascii=False, indent=2)

            self.logger.info(f"Updated static file with translation: {source_text} to {translation_text}")
            return True
        except Exception as e:
            self.logger.error(f"Error updating static file: {str(e)}")
            self.logger.info(f"Static file update deferred for translation: {source_text} to {translation_text} - will be processed by optimize_translation_system.py script")
            return False

    def translate(self, text, source_lang, target_lang, use_attention=False, is_fallback=False):
        """
        Translate text from source language to target language.
        This is a wrapper around translate_text that handles sentences better.

        Args:
            text: Text to translate
            source_lang: Source language code
            target_lang: Target language code
            use_attention: Whether to use attention mechanism
            is_fallback: Whether this is a fallback request

        Returns:
            dict: The translation result with detailed information
        """
        # Check if this is a sentence (more than one word)
        is_sentence = len(text.split()) > 1
        is_likely_sentence = any(p in text for p in '.?!,:;') or text.strip().endswith('?')

        # Force attention for sentences
        if is_sentence or is_likely_sentence:
            use_attention = True
            self.logger.info(f"Detected sentence: '{text}'. Forcing attention mechanism.")

        # For sentences, try to find an exact match in the database first
        # but only to use as a reference for the attention mechanism
        exact_match_reference = None
        if is_sentence or is_likely_sentence:
            try:
                # Look for an exact match in the database
                from translation_app.models import ComprehensiveTranslation, Language

                # Get language objects
                source_lang_obj = Language.objects.get(code=source_lang)
                target_lang_obj = Language.objects.get(code=target_lang)

                # Try exact match first (case insensitive)
                exact_match = ComprehensiveTranslation.objects.filter(
                    base_word__iexact=text,
                    source_language=source_lang_obj,
                    target_language=target_lang_obj
                ).first()

                if exact_match:
                    self.logger.info(f"Found exact match for sentence in database: {text}")

                    # Get the active version if available
                    active_version = exact_match.versions.filter(is_active=True).first()
                    translation_text = active_version.translation if active_version else exact_match.translation
                    confidence_score = active_version.confidence_score if active_version else 0.99

                    # Validate the translation - if it's suspiciously short compared to the source
                    source_word_count = len(text.split())
                    translation_word_count = len(translation_text.split())

                    if translation_word_count < source_word_count / 2:
                        self.logger.warning(f"Exact match translation is suspiciously short: {source_word_count} words -> {translation_word_count} words. Will use attention mechanism instead.")

                    # Store the reference for the attention mechanism to use
                    exact_match_reference = {
                        'original': text,
                        'translation': translation_text,
                        'source_lang': source_lang,
                        'target_lang': target_lang
                    }

                    # Store in Django cache for the attention mechanism to use
                    from django.core.cache import cache
                    cache.set('exact_match_reference', exact_match_reference, 300)  # Store for 5 minutes
            except Exception as e:
                self.logger.error(f"Error looking for exact match in database: {str(e)}")

        # If no exact match found or this is a sentence, use the standard translation method
        result = self.translate_text(text, source_lang, target_lang)

        # For sentences, check if the translation is suspiciously short
        if (is_sentence or is_likely_sentence) and len(result.get('translation', '').split()) < len(text.split()) / 2:
            self.logger.warning(f"Translation is suspiciously short: {len(text.split())} words -> {len(result.get('translation', '').split())} words. Will force attention mechanism.")
            use_attention = True

            # Also mark the result to force a new translation
            result['force_new_translation'] = True

        # For sentences or when attention is requested, always try to improve with attention mechanism
        if (use_attention or is_sentence or is_likely_sentence) and ATTENTION_MECHANISM_ENABLED:
            try:
                # Use our direct sentence translation method instead of the attention mechanism
                self.logger.info(f"Generating sentence translation for: '{text}'")
                improved_result = self.generate_sentence_translation(
                    text,
                    result,
                    source_lang,
                    target_lang
                )

                # Check if we got an improved or generated translation
                if improved_result and (
                    improved_result.get('source') in ['attention_improved', 'attention_generated', 'attention_with_reference']
                ):
                    self.logger.info(f"Improved/generated translation with attention: {text} to {improved_result['translation']}")

                    # Also update the recent translations cache
                    safe_text = text.replace(' ', '_').replace(':', '_')
                    safe_text = safe_text.replace('\n', '_')
                    safe_text = safe_text[:200]
                    cache_key = f"{source_lang}_{target_lang}_{safe_text}"
                    self.recent_translations_cache[cache_key] = improved_result

                    # If this is a generated translation, also save it to the database for future use
                    if improved_result.get('source') in ['attention_generated', 'attention_improved', 'attention_with_reference']:
                        try:
                            # Get language objects
                            source_lang_obj = Language.objects.get(code=source_lang)
                            target_lang_obj = Language.objects.get(code=target_lang)

                            # Check if this translation already exists
                            existing = ComprehensiveTranslation.objects.filter(
                                base_word=text,
                                source_language=source_lang_obj,
                                target_language=target_lang_obj
                            ).first()

                            if existing:
                                # Update existing translation
                                existing.translation = improved_result['translation']
                                existing.notes = (existing.notes or '') + f'\nUpdated via sentence translation on {timezone.now().strftime("%Y-%m-%d")}'
                                existing.save()

                                # Create a new version
                                TranslationVersion.objects.create(
                                    comprehensive_translation=existing,
                                    translation=improved_result['translation'],
                                    confidence_score=0.85,  # Higher confidence for sentence translation
                                    is_active=True,
                                    notes='Updated via sentence translation'
                                )

                                self.logger.info(f"Updated existing translation in database: {text} → {improved_result['translation']}")
                            else:
                                # Create a new translation entry
                                new_trans = ComprehensiveTranslation.objects.create(
                                    base_word=text,
                                    translation=improved_result['translation'],
                                    source_language=source_lang_obj,
                                    target_language=target_lang_obj,
                                    part_of_speech='sentence' if is_sentence else 'phrase',
                                    notes='Generated via sentence translation'
                                )

                                # Create initial version with moderate confidence
                                TranslationVersion.objects.create(
                                    comprehensive_translation=new_trans,
                                    translation=improved_result['translation'],
                                    confidence_score=0.85,  # Higher confidence for sentence translation
                                    is_active=True,
                                    notes='Generated via sentence translation'
                                )

                                self.logger.info(f"Saved generated translation to database: {text} → {improved_result['translation']}")
                        except Exception as e:
                            self.logger.error(f"Error saving generated translation to database: {str(e)}")

                    return improved_result
            except Exception as e:
                self.logger.error(f"Error generating sentence translation: {str(e)}")

        return result

    def generate_sentence_translation(self, source_text, result, source_lang, target_lang):
        """
        Generate a complete sentence translation.
        This method is specifically designed for translating sentences.

        Args:
            source_text: Source text
            result: Translation data from the translation service
            source_lang: Source language code
            target_lang: Target language code

        Returns:
            dict: Improved translation data
        """
        import re

        self.logger.info(f"SENTENCE DETECTED: '{source_text}'. Generating proper sentence translation...")

        # Clean the source text
        source_clean = re.sub(r'[^\w\s]', ' ', source_text.lower())
        source_tokens = source_clean.split()

        # Get language objects
        try:
            source_lang_obj = Language.objects.get(code=source_lang)
            target_lang_obj = Language.objects.get(code=target_lang)
        except Exception as e:
            self.logger.error(f"Error getting language objects: {str(e)}")
            return result

        # STEP 1: Try to find similar sentences in the database
        self.logger.info("Step 1: Looking for similar sentences in the database")
        similar_sentences = []

        try:
            # Look for sentences with similar words
            for word in source_tokens:
                similar = ComprehensiveTranslation.objects.filter(
                    base_word__icontains=word,
                    source_language=source_lang_obj,
                    target_language=target_lang_obj,
                    part_of_speech__in=['sentence', 'phrase']
                )[:5]

                for match in similar:
                    # Calculate similarity score based on word overlap
                    match_words = match.base_word.lower().split()
                    common_words = set(source_tokens).intersection(set(match_words))
                    similarity = len(common_words) / max(len(source_tokens), len(match_words))

                    if similarity > 0.3:  # Only consider if at least 30% similar
                        similar_sentences.append({
                            'base_word': match.base_word,
                            'translation': match.translation,
                            'similarity': similarity
                        })

            # Remove duplicates and sort by similarity
            unique_similar = {}
            for s in similar_sentences:
                if s['base_word'] not in unique_similar or s['similarity'] > unique_similar[s['base_word']]['similarity']:
                    unique_similar[s['base_word']] = s

            similar_sentences = list(unique_similar.values())
            similar_sentences.sort(key=lambda x: x['similarity'], reverse=True)

            if similar_sentences:
                self.logger.info(f"Found {len(similar_sentences)} similar sentences")
                for s in similar_sentences[:3]:  # Log top 3
                    self.logger.info(f"Similar: '{s['base_word']}' → '{s['translation']}' (similarity: {s['similarity']:.2f})")
        except Exception as e:
            self.logger.error(f"Error finding similar sentences: {str(e)}")

        # STEP 2: Generate a new translation using word-by-word approach
        self.logger.info("Step 2: Generating word-by-word translation")
        word_translations = []
        covered_indices = set()

        # First try to find translations for multi-word phrases
        for n in range(min(5, len(source_tokens)), 1, -1):
            for i in range(len(source_tokens) - n + 1):
                # Skip if any of these positions are already covered
                if any(j in covered_indices for j in range(i, i+n)):
                    continue

                # Extract n-gram
                ngram = ' '.join(source_tokens[i:i+n])

                # Look for matches in the database
                try:
                    phrase_matches = ComprehensiveTranslation.objects.filter(
                        base_word__iexact=ngram,
                        source_language=source_lang_obj,
                        target_language=target_lang_obj
                    )[:3]

                    if phrase_matches:
                        phrase_match = phrase_matches[0]  # Use the highest confidence match
                        self.logger.info(f"Found phrase match: '{ngram}' → '{phrase_match.translation}'")

                        # Add this phrase translation
                        word_translations.append({
                            'original': ngram,
                            'translation': phrase_match.translation,
                            'confidence': 0.9,
                            'position': i,
                            'length': n
                        })
                        # Mark these positions as covered
                        covered_indices.update(range(i, i+n))
                except Exception as e:
                    self.logger.error(f"Error finding phrase match: {str(e)}")

        # Now get individual word translations for any words not covered by phrases
        for i, word in enumerate(source_tokens):
            if i in covered_indices:
                continue  # Skip words that are part of phrases

            try:
                word_matches = ComprehensiveTranslation.objects.filter(
                    base_word__iexact=word,
                    source_language=source_lang_obj,
                    target_language=target_lang_obj
                )[:3]

                if word_matches:
                    word_trans = word_matches[0]  # Use the highest confidence match
                    self.logger.info(f"Found word match: '{word}' → '{word_trans.translation}'")

                    word_translations.append({
                        'original': word,
                        'translation': word_trans.translation,
                        'confidence': 0.85,
                        'position': i,
                        'length': 1
                    })
                else:
                    # If no translation found, try to find a similar word
                    self.logger.info(f"No exact translation found for word: '{word}', looking for similar words")

                    # Try to find similar words
                    similar_words = ComprehensiveTranslation.objects.filter(
                        base_word__icontains=word[:3] if len(word) > 3 else word,  # Match on prefix
                        source_language=source_lang_obj,
                        target_language=target_lang_obj
                    )[:5]

                    if similar_words:
                        # Use the first similar word
                        similar_word = similar_words[0]
                        self.logger.info(f"Found similar word: '{similar_word.base_word}' → '{similar_word.translation}'")

                        word_translations.append({
                            'original': word,
                            'translation': similar_word.translation,
                            'confidence': 0.7,
                            'position': i,
                            'length': 1
                        })
                    else:
                        # If still no translation found, keep the original word
                        self.logger.info(f"No similar words found for: '{word}', keeping original")
                        word_translations.append({
                            'original': word,
                            'translation': word,
                            'confidence': 0.5,
                            'position': i,
                            'length': 1
                        })
            except Exception as e:
                self.logger.error(f"Error finding word translation: {str(e)}")
                # If error, keep the original word
                word_translations.append({
                    'original': word,
                    'translation': word,
                    'confidence': 0.5,
                    'position': i,
                    'length': 1
                })

        # Sort translations by position
        word_translations.sort(key=lambda x: x.get('position', 0))

        # STEP 3: Construct the translation
        self.logger.info("Step 3: Constructing final translation")

        # If we have similar sentences, use them to improve the translation
        final_translation = ""
        if similar_sentences and similar_sentences[0]['similarity'] > 0.7:
            # If we have a very similar sentence, use its translation as a base
            best_match = similar_sentences[0]
            self.logger.info(f"Using similar sentence as base: '{best_match['base_word']}' → '{best_match['translation']}'")
            final_translation = best_match['translation']
        else:
            # Otherwise, construct from word-by-word translations
            constructed_translation = ' '.join([wt['translation'] for wt in word_translations])
            self.logger.info(f"Constructed translation: '{constructed_translation}'")

            # Check if the constructed translation is too short
            source_word_count = len(source_tokens)
            constructed_word_count = len(constructed_translation.split())

            if constructed_word_count < source_word_count * 0.5:
                self.logger.warning(f"Constructed translation too short: {constructed_word_count} words vs {source_word_count} words in source")

                # Try to use similar sentences to improve
                if similar_sentences:
                    best_match = similar_sentences[0]
                    self.logger.info(f"Using similar sentence to improve: '{best_match['base_word']}' → '{best_match['translation']}'")
                    final_translation = best_match['translation']
                else:
                    final_translation = constructed_translation
            else:
                final_translation = constructed_translation

        # STEP 4: Create the result
        self.logger.info(f"Step 4: Creating final result with translation: '{final_translation}'")

        # Calculate average confidence
        avg_confidence = sum(wt['confidence'] for wt in word_translations) / len(word_translations) if word_translations else 0.75

        # Create a simplified word_by_word without position information for API response
        simplified_word_by_word = []
        for wt in word_translations:
            simplified_word_by_word.append({
                'original': wt['original'],
                'translation': wt['translation'],
                'confidence': wt['confidence'],
                'source': 'attention_generated'
            })

        # Create the result
        source_word_count = len(source_tokens)
        target_word_count = len(final_translation.split())
        ratio = target_word_count / source_word_count if source_word_count > 0 else 0

        new_result = {
            'original': source_text,
            'translation': final_translation,
            'confidence': avg_confidence,
            'source': 'attention_generated',
            'notes': f'Generated using enhanced sentence translation. Word ratio: {ratio:.2f} ({source_word_count} → {target_word_count})',
            'word_by_word': simplified_word_by_word
        }

        self.logger.info(f"FINAL TRANSLATION: {source_text} → {final_translation}")
        return new_result

    def get_translation_details(self, text, source_lang, target_lang):
        """
        Get detailed information about a translation.

        Args:
            text: Text to translate
            source_lang: Source language code
            target_lang: Target language code

        Returns:
            dict: Detailed information about the translation
        """
        return self.translate_text(text, source_lang, target_lang)

    def update_single_translation(self, source_text, target_text, source_lang, target_lang, force_update=False):
        """
        Update a single translation in memory without reloading all translations.
        This is much more efficient than reloading all translations.

        Args:
            source_text: The source text to translate
            target_text: The translation
            source_lang: The source language code
            target_lang: The target language code
            force_update: Whether to force the update even if the translation exists

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Create the key for the translation dictionary
            key = f"{source_lang}_to_{target_lang}"

            # Map language codes to our internal format
            if source_lang == 'tgl' and target_lang == 'ted':
                internal_key = 'tgl_to_ted'
            elif source_lang == 'ted' and target_lang == 'tgl':
                internal_key = 'ted_to_tgl'
            else:
                internal_key = key

            # Ensure the key exists in the dictionary
            if internal_key not in self.word_translations:
                self.word_translations[internal_key] = {}

            # Determine if this is a single word or a phrase
            is_phrase = len(source_text.split()) > 1

            # Create the appropriate key
            dict_key = f"phrase:{source_text.lower()}" if is_phrase else source_text.lower()

            # Update the translation
            if is_phrase:
                # For phrases, store with more metadata
                self.word_translations[internal_key][dict_key] = {
                    'translation': target_text,
                    'part_of_speech': 'phrase',
                    'notes': 'Updated via direct update',
                    'cultural_notes': '',
                    'examples': [],
                    'priority': True,
                    'confidence': 0.99,
                    'source': 'updated',
                    'version_id': None
                }
            else:
                # For single words, store with more metadata
                self.word_translations[internal_key][dict_key] = {
                    'translation': target_text,
                    'part_of_speech': 'word',
                    'notes': 'Updated via direct update',
                    'cultural_notes': '',
                    'examples': [],
                    'priority': True,
                    'confidence': 0.99,
                    'source': 'updated',
                    'version_id': None
                }

            # Also update the recent translations cache
            # Create a safe cache key by replacing problematic characters
            safe_text = source_text.replace(' ', '_').replace(':', '_')
            # Handle newlines separately
            safe_text = safe_text.replace('\n', '_')
            # Limit length to 200 characters
            safe_text = safe_text[:200]
            cache_key = f"{source_lang}_{target_lang}_{safe_text}"
            self.recent_translations_cache[cache_key] = {
                'original': source_text,
                'translation': target_text,
                'confidence': 0.99,
                'source': 'database_updated',
                'word_by_word': [{
                    'original': source_text,
                    'translation': target_text,
                    'confidence': 0.99,
                    'source': 'database_updated'
                }]
            }

            # Log the update
            self.logger.info(f"Updated translation in memory: {source_text} to {target_text}")

            # Update the database if needed
            try:
                # Get language objects
                source_lang_obj = Language.objects.get(code=source_lang)
                target_lang_obj = Language.objects.get(code=target_lang)

                # Find or create the translation
                translation, created = ComprehensiveTranslation.objects.get_or_create(
                    base_word=source_text,
                    source_language=source_lang_obj,
                    target_language=target_lang_obj,
                    defaults={
                        'translation': target_text,
                        'part_of_speech': 'phrase' if is_phrase else 'word',
                        'notes': 'Created via direct update'
                    }
                )

                if not created and (force_update or translation.translation != target_text):
                    # Create a version record of the current translation
                    TranslationVersion.objects.create(
                        comprehensive_translation=translation,
                        translation=translation.translation,
                        created_by='system',
                        confidence_score=0.95,
                        is_active=False,
                        notes='Archived before direct update'
                    )

                    # Update with the new translation
                    translation.translation = target_text
                    translation.notes = (translation.notes or '') + f'\nUpdated via direct update on {timezone.now().strftime("%Y-%m-%d")}'
                    translation.save()

                    # Create a new version for the updated translation
                    TranslationVersion.objects.create(
                        comprehensive_translation=translation,
                        translation=target_text,
                        created_by='system',
                        confidence_score=0.99,
                        is_active=True,
                        notes='Updated via direct update'
                    )

                    self.logger.info(f"Updated translation in database: {source_text} to {target_text}")
            except Exception as e:
                self.logger.error(f"Error updating translation in database: {str(e)}")

            return True
        except Exception as e:
            self.logger.error(f"Error updating translation in memory: {str(e)}")
            return False

    def reload_translations(self, force_load=True):
        """
        Force a complete reload of translations from the database.
        This is useful when translations have been updated in the admin interface
        or through other means outside the normal flow.

        Args:
            force_load: If True, load all translations even if auto processes are disabled

        Returns:
            bool: True if successful, False otherwise
        """
        self.logger.info("Forcing complete reload of translations from database")
        try:
            # Check if automatic loading is disabled and we're not forcing a load
            if self.disable_auto_loading and not force_load:
                self.logger.info("Automatic loading is disabled - only loading minimal translations")

                # Clear all caches
                self.word_translations = {
                    'tgl_to_ted': {},  # Tagalog to Teduray
                    'ted_to_tgl': {}   # Teduray to Tagalog
                }
                self.recent_translations_cache = {}

                # Clear Django cache
                cache.delete(self.cache_key)
                cache.delete('translation_service_complete_load')

                # Load only minimal translations
                self._load_minimal_translations()

                # Set flags
                self.is_loading = False
                self.complete_load_done = False

                self.logger.info("Loaded minimal translations - full loading will be done on demand or by scheduled task")
                return True

            # If we're here, either auto loading is enabled or we're forcing a load
            self.logger.info("Loading all translations (forced load or auto loading enabled)")

            # Clear all caches
            self.word_translations = {
                'tgl_to_ted': {},  # Tagalog to Teduray
                'ted_to_tgl': {}   # Teduray to Tagalog
            }
            self.recent_translations_cache = {}

            # Clear Django cache
            cache.delete(self.cache_key)
            cache.delete('translation_service_complete_load')

            # Set loading flag
            self.is_loading = True
            self.complete_load_done = False

            # Load common translations first for immediate use
            self.load_common_translations()

            # Then load all translations from database
            self.load_translations_from_database(skip_common=False)

            # Load from files
            self.load_translations_from_files()

            # Load learned translations
            self.load_learned_translations()

            # Update cache
            cache.set(self.cache_key, self.word_translations, self.cache_timeout)
            cache.set('translation_service_complete_load', True, self.cache_timeout)

            # Reset flags
            self.is_loading = False
            self.complete_load_done = True

            self.logger.info(f"Completed forced reload: {len(self.word_translations['tgl_to_ted'])} Tagalog to Teduray translations")
            self.logger.info(f"Completed forced reload: {len(self.word_translations['ted_to_tgl'])} Teduray to Tagalog translations")

            return True
        except Exception as e:
            self.logger.error(f"Error during forced reload: {str(e)}")
            self.is_loading = False
            return False




# Add this at the end of the file, after the TranslationService class

# Singleton instance of the translation service
_translation_service = None

def get_translation_service(skip_loading_for_admin=None):
    """
    Get the singleton instance of the translation service.
    This ensures we only load translations once across the application.

    Args:
        skip_loading_for_admin: If True, skip loading translations for admin requests.
                               If None, use the SKIP_ADMIN_TRANSLATION_LOADING setting.

    Returns:
        TranslationService: The singleton translation service instance
    """
    global _translation_service

    # Check if we should skip loading for admin requests
    if skip_loading_for_admin is None:
        from django.conf import settings
        skip_loading_for_admin = getattr(settings, 'SKIP_ADMIN_TRANSLATION_LOADING', True)

    # Check if this is an admin request
    is_admin_request = False
    try:
        # Try to get the current request from middleware
        from threading import local
        _thread_locals = local()
        if hasattr(_thread_locals, 'request') and _thread_locals.request:
            is_admin_request = '/admin/' in _thread_locals.request.path
    except Exception:
        # If we can't get the request, assume it's not an admin request
        pass

    # Create the service if it doesn't exist
    if _translation_service is None:
        _translation_service = TranslationService()

        # If this is an admin request and we should skip loading, don't load translations
        if is_admin_request and skip_loading_for_admin:
            # Initialize with empty dictionaries
            _translation_service.word_translations = {
                'tgl_to_ted': {},
                'ted_to_tgl': {}
            }
            _translation_service.logger.info("Skipping translation loading for admin request")
            _translation_service.is_loading = False
            _translation_service.complete_load_done = False

    return _translation_service