"""
Management command to train translation models from database examples.
"""

from django.core.management.base import BaseCommand, CommandError
from translation_app.model_training import model_trainer, TRAINING_AVAILABLE

class Command(BaseCommand):
    help = 'Train translation models using examples from the database'
    
    def add_arguments(self, parser):
        parser.add_argument('--source', type=str, help='Source language code (e.g., tgl)')
        parser.add_argument('--target', type=str, help='Target language code (e.g., ted)')
        parser.add_argument('--epochs', type=int, default=3, help='Number of training epochs')
        parser.add_argument('--batch-size', type=int, default=8, help='Training batch size')
        parser.add_argument('--custom', action='store_true', help='Create a custom model instead of fine-tuning')
    
    def handle(self, *args, **options):
        if not TRAINING_AVAILABLE:
            self.stderr.write(self.style.ERROR(
                'Training dependencies not installed. Install with: pip install transformers datasets'
            ))
            return
        
        source_lang = options.get('source')
        target_lang = options.get('target')
        
        if not source_lang or not target_lang:
            # Train all language pairs
            self.stdout.write(self.style.WARNING('No language pair specified, training all pairs'))
            
            # Get all language pairs from the database
            from translation_app.models import ComprehensiveTranslation
            pairs = ComprehensiveTranslation.objects.values_list(
                'source_language__code', 'target_language__code'
            ).distinct()
            
            for source, target in pairs:
                self.train_model(source, target, options)
        else:
            # Train specific language pair
            self.train_model(source_lang, target_lang, options)
    
    def train_model(self, source_lang, target_lang, options):
        """Train a model for a specific language pair"""
        epochs = options.get('epochs', 3)
        batch_size = options.get('batch_size', 8)
        custom = options.get('custom', False)
        
        self.stdout.write(self.style.SUCCESS(
            f"Starting {'custom model creation' if custom else 'fine-tuning'} "
            f"for {source_lang} to {target_lang} with {epochs} epochs"
        ))
        
        if custom:
            success = model_trainer.create_custom_model(source_lang, target_lang)
        else:
            success = model_trainer.fine_tune_model(
                source_lang, target_lang, 
                epochs=epochs, 
                batch_size=batch_size
            )
        
        if success:
            self.stdout.write(self.style.SUCCESS(
                f"Successfully {'created custom model' if custom else 'fine-tuned model'} "
                f"for {source_lang} to {target_lang}"
            ))
        else:
            self.stdout.write(self.style.ERROR(
                f"Failed to {'create custom model' if custom else 'fine-tune model'} "
                f"for {source_lang} to {target_lang}"
            ))
