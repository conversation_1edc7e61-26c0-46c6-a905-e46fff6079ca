#!/bin/bash
# Script to test the translation system

# Set environment variables
export DJANGO_SETTINGS_MODULE=translation_project.settings

# Print header
echo "====================================="
echo "Translation System Test"
echo "====================================="
echo "Testing if the translation system correctly handles longer phrases"
echo "and if approved translations are properly loaded."
echo

# Run the Python test script
echo "Running Python test script..."
python test_translation.py

# Print footer
echo
echo "====================================="
echo "Test completed"
echo "====================================="

# Check if we need to test the reload API endpoint
if [ "$1" == "--test-api" ]; then
    echo
    echo "Testing the reload translations API endpoint..."
    echo "This will make a direct HTTP request to the API endpoint."
    echo
    
    # Get CSRF token from the server (if needed)
    CSRF_TOKEN=$(curl -s -c cookies.txt http://localhost:8000/ | grep csrftoken | sed 's/.*value="\([^"]*\)".*/\1/')
    
    # Make the API request
    echo "Making API request to reload translations..."
    curl -X POST \
         -H "Content-Type: application/json" \
         -H "X-CSRFToken: $CSRF_TOKEN" \
         -b cookies.txt \
         http://localhost:8000/api/reload_translations/
    
    echo
    echo "API test completed"
    
    # Clean up
    rm -f cookies.txt
fi

echo
echo "Done!"
