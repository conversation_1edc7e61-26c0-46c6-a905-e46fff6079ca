#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to set up static files for PythonAnywhere deployment.
This script will:
1. Ensure the static directories exist
2. Copy static files manually
3. Print debug information about the static files configuration
"""

import os
import sys
import shutil
from pathlib import Path

# Get the base directory of the project
BASE_DIR = Path(__file__).resolve().parent

def is_running_on_pythonanywhere():
    """Check if we're running on PythonAnywhere."""
    # Check for PythonAnywhere-specific environment variables or paths
    if 'PYTHONANYWHERE_DOMAIN' in os.environ:
        return True

    # Check for PythonAnywhere in the hostname
    try:
        import socket
        if 'pythonanywhere' in socket.gethostname().lower():
            return True
    except:
        pass

    # Check for PythonAnywhere in the sys.path
    for path in sys.path:
        if 'pythonanywhere' in str(path).lower():
            return True

    return False

def setup_static_files():
    """Set up static files for PythonAnywhere deployment."""
    print("Setting up static files for deployment...")

    if is_running_on_pythonanywhere():
        # Define the directories for PythonAnywhere
        static_root = '/home/<USER>/teduray/staticfiles'
        static_dirs = ['/home/<USER>/teduray/static']

        print(f"PythonAnywhere detected.")
        print(f"STATIC_ROOT: {static_root}")
        print(f"STATICFILES_DIRS: {static_dirs}")

        # Ensure the directories exist
        for directory in [static_root] + static_dirs:
            if not os.path.exists(directory):
                try:
                    os.makedirs(directory)
                    print(f"Created directory: {directory}")
                except Exception as e:
                    print(f"Error creating directory {directory}: {e}", file=sys.stderr)
            else:
                print(f"Directory already exists: {directory}")

        # Copy static files manually
        print("\nCopying static files manually...")
        try:
            # Clear the static root directory first
            for item in os.listdir(static_root):
                item_path = os.path.join(static_root, item)
                if os.path.isdir(item_path):
                    shutil.rmtree(item_path)
                else:
                    os.remove(item_path)
            print(f"Cleared directory: {static_root}")

            # Copy files from static directory to static root
            for static_dir in static_dirs:
                if os.path.exists(static_dir):
                    for item in os.listdir(static_dir):
                        source = os.path.join(static_dir, item)
                        destination = os.path.join(static_root, item)

                        if os.path.isdir(source):
                            shutil.copytree(source, destination, dirs_exist_ok=True)
                            print(f"Copied directory: {item}")
                        else:
                            shutil.copy2(source, destination)
                            print(f"Copied file: {item}")

                    print(f"Successfully copied files from {static_dir} to {static_root}")
                else:
                    print(f"Static directory not found: {static_dir}")
        except Exception as e:
            print(f"Error copying static files: {e}", file=sys.stderr)

        print("\nStatic files setup complete.")
    else:
        print("Not running on PythonAnywhere. This script is intended for PythonAnywhere deployment.")
        print("For local development, use 'python manage.py collectstatic'.")

if __name__ == "__main__":
    setup_static_files()
