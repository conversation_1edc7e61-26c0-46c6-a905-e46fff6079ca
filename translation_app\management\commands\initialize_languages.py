"""
Management command to initialize languages in the database.
"""

from django.core.management.base import BaseCommand
from translation_app.models import Language

class Command(BaseCommand):
    help = 'Initialize languages in the database'

    def handle(self, *args, **options):
        # Define the languages
        languages = [
            {
                'name': 'Tagalog',
                'code': 'tgl',
                'description': 'Tagalog is an Austronesian language spoken in the Philippines by about 22 million people as a first language.'
            },
            {
                'name': 'Teduray',
                'code': 'ted',
                'description': 'Teduray is an Austronesian language spoken by the Teduray people in Mindanao, Philippines.'
            },
            {
                'name': 'English',
                'code': 'eng',
                'description': 'English is a West Germanic language of the Indo-European language family.'
            }
        ]

        # Create the languages
        for lang_data in languages:
            language, created = Language.objects.get_or_create(
                code=lang_data['code'],
                defaults={
                    'name': lang_data['name'],
                    'description': lang_data['description']
                }
            )

            if created:
                self.stdout.write(self.style.SUCCESS(f'Created language: {language.name}'))
            else:
                self.stdout.write(f'Language already exists: {language.name}')

        self.stdout.write(self.style.SUCCESS('Languages initialized successfully'))
