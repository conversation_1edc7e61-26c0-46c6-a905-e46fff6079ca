#!/usr/bin/env python
"""
Script to process all pending translation feedback files.

This script:
1. Finds all pending feedback files in the data/pending_feedback directory
2. Processes each file by creating a TranslationFeedback record
3. Updates the database with the suggested translation
4. Updates the in-memory translation cache
5. Marks the feedback as processed

Usage:
    python process_all_pending_feedback.py

This script is designed to be run as a standalone script or as a scheduled task.
"""

import os
import sys
import json
import logging
import time
from glob import glob
import django

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Import Django models and utilities after setting up the environment
from django.db import connection
from django.utils import timezone
from translation_app.models import Language, TranslationFeedback, ComprehensiveTranslation, TranslationVersion
from translation_app.utils import process_feedback_safely
from translation_app.services import get_translation_service
from translation_app.middleware import with_retry

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('feedback_processing.log')
    ]
)
logger = logging.getLogger(__name__)

def process_feedback_file(filepath):
    """Process a single feedback file and update the database."""
    logger.info(f"Processing feedback file: {filepath}")
    
    try:
        # Read the feedback data
        with open(filepath, 'r', encoding='utf-8') as f:
            feedback_data = json.load(f)
        
        # Extract data
        original_text = feedback_data.get('original_text', '')
        translated_text = feedback_data.get('translated_text', '')
        suggested_translation = feedback_data.get('suggested_translation', '')
        source_lang_code = feedback_data.get('source_lang_code', 'tgl')
        target_lang_code = feedback_data.get('target_lang_code', 'ted')
        rating = feedback_data.get('rating')
        comments = feedback_data.get('comments', '')
        user_id = feedback_data.get('user_id')
        
        logger.info(f"Processing feedback: {original_text} → {suggested_translation}")
        
        # Ensure the database connection is open
        if connection.connection is None:
            connection.ensure_connection()
        
        # Get language objects
        def get_source_language():
            return Language.objects.get(code=source_lang_code)

        def get_target_language():
            return Language.objects.get(code=target_lang_code)

        source_language = with_retry(get_source_language)
        target_language = with_retry(get_target_language)
        
        # Create feedback record
        def create_feedback():
            return TranslationFeedback.objects.create(
                original_text=original_text,
                translated_text=translated_text,
                suggested_translation=suggested_translation,
                source_language=source_language,
                target_language=target_language,
                rating=rating,
                comments=comments,
                user_id=user_id,
                processed=True  # Mark as processed immediately
            )
        
        feedback = with_retry(create_feedback)
        logger.info(f"Created feedback record with ID: {feedback.id}")
        
        # Update the database with the suggested translation
        try:
            # Check if this translation already exists
            existing = ComprehensiveTranslation.objects.filter(
                base_word__iexact=original_text,
                source_language=source_language,
                target_language=target_language
            ).first()
            
            if existing:
                # Update existing translation
                def update_existing():
                    existing.translation = suggested_translation
                    existing.notes = (existing.notes or '') + f'\nUpdated via feedback on {timezone.now().strftime("%Y-%m-%d")}'
                    existing.save()
                    return existing
                
                updated = with_retry(update_existing)
                logger.info(f"Updated existing translation: {original_text} → {suggested_translation}")
                
                # Create a new version with high confidence
                def create_version():
                    return TranslationVersion.objects.create(
                        comprehensive_translation=existing,
                        translation=suggested_translation,
                        confidence_score=0.95,  # High confidence for user feedback
                        is_active=True,
                        notes='Updated via feedback processing'
                    )
                
                version = with_retry(create_version)
                logger.info(f"Created new version for translation: {original_text}")
            else:
                # Create new translation
                def create_translation():
                    new_translation = ComprehensiveTranslation.objects.create(
                        base_word=original_text,
                        translation=suggested_translation,
                        source_language=source_language,
                        target_language=target_language,
                        part_of_speech='phrase' if ' ' in original_text else 'word',
                        notes='Added via feedback processing'
                    )
                    
                    # Create initial version with high confidence
                    TranslationVersion.objects.create(
                        comprehensive_translation=new_translation,
                        translation=suggested_translation,
                        confidence_score=0.95,  # High confidence for user feedback
                        is_active=True,
                        notes='Created via feedback processing'
                    )
                    
                    return new_translation
                
                new_translation = with_retry(create_translation)
                logger.info(f"Created new translation: {original_text} → {suggested_translation}")
            
            # Process the feedback safely (for additional processing like pattern learning)
            try:
                result = process_feedback_safely(
                    original_text,
                    translated_text,
                    suggested_translation,
                    source_lang_code,
                    target_lang_code,
                    None  # Don't pass user object
                )
                logger.info(f"Processed feedback safely: {result}")
            except Exception as e:
                logger.error(f"Error in process_feedback_safely: {str(e)}")
            
            # Update the translation in the cache
            try:
                translation_service = get_translation_service()
                translation_service.update_single_translation(
                    original_text,
                    suggested_translation,
                    source_lang_code,
                    target_lang_code
                )
                logger.info(f"Updated translation in cache: {original_text} → {suggested_translation}")
            except Exception as e:
                logger.error(f"Error updating translation in cache: {str(e)}")
            
            # Move the processed file to a backup directory
            backup_dir = os.path.join('data', 'processed_feedback')
            os.makedirs(backup_dir, exist_ok=True)
            
            backup_path = os.path.join(backup_dir, os.path.basename(filepath))
            os.rename(filepath, backup_path)
            logger.info(f"Moved processed file to: {backup_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error processing feedback: {str(e)}")
            return False
            
    except Exception as e:
        logger.error(f"Error processing feedback file {filepath}: {str(e)}")
        return False

def process_all_pending_feedback():
    """Process all pending feedback files."""
    logger.info("Starting to process all pending feedback files")
    
    # Create the directory if it doesn't exist
    os.makedirs('data/pending_feedback', exist_ok=True)
    
    # Get all feedback files
    feedback_files = glob('data/pending_feedback/feedback_*.json')
    logger.info(f"Found {len(feedback_files)} pending feedback files")
    
    # Process each file
    success_count = 0
    error_count = 0
    
    for filepath in feedback_files:
        try:
            # Add a small delay between processing files to reduce database contention
            time.sleep(0.5)
            
            if process_feedback_file(filepath):
                success_count += 1
            else:
                error_count += 1
        except Exception as e:
            logger.error(f"Unhandled error processing {filepath}: {str(e)}")
            error_count += 1
    
    logger.info(f"Finished processing feedback files. Success: {success_count}, Errors: {error_count}")
    return success_count, error_count

if __name__ == '__main__':
    logger.info("Starting feedback processing script")
    success, errors = process_all_pending_feedback()
    logger.info(f"Feedback processing complete. Processed {success + errors} files ({success} successful, {errors} errors)")
