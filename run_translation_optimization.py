#!/usr/bin/env python
"""
Run Translation Optimization Script

This script is a simple wrapper to run the optimize_translation_system.py script
with the attention mechanism enabled. It can be added as a scheduled task in PythonAnywhere.

Usage:
    python run_translation_optimization.py
"""

import os
import sys
import subprocess
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('translation_optimization.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('run_translation_optimization')

def main():
    """Run the optimize_translation_system.py script with attention mechanism."""
    start_time = datetime.now()
    logger.info(f"Starting translation optimization at {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check if the optimize_translation_system.py script exists
    if not os.path.exists('optimize_translation_system.py'):
        logger.error("optimize_translation_system.py script not found")
        return 1
    
    # Run the script with attention mechanism enabled
    try:
        result = subprocess.run(
            [sys.executable, 'optimize_translation_system.py', '--optimize-attention'],
            capture_output=True,
            text=True
        )
        
        # Log the output
        if result.stdout:
            logger.info(f"Process output:\n{result.stdout}")
        
        # Log any errors
        if result.stderr:
            logger.error(f"Process errors:\n{result.stderr}")
        
        # Check return code
        if result.returncode == 0:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.info(f"Translation optimization completed successfully in {duration:.2f} seconds")
            return 0
        else:
            logger.error(f"Translation optimization failed with return code {result.returncode}")
            return result.returncode
    except Exception as e:
        logger.error(f"Error running translation optimization: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
