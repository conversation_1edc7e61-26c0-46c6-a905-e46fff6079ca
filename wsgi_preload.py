"""
Minimal preload script for uWSGI to initialize the application before workers start.
This helps reduce the startup time for each worker.
"""

import os
import sys

# Set environment variables for optimization
os.environ['DJANGO_SETTINGS_MODULE'] = 'config.settings'
os.environ['PYTHONOPTIMIZE'] = '1'  # Enable Python optimization
os.environ['USE_ULTRA_LAZY_LOADING'] = 'True'  # Enable ultra lazy loading
os.environ['PYTHONANYWHERE_OPTIMIZATION'] = 'True'  # Enable PythonAnywhere optimization

# Print to stderr for debugging
print("WSGI preload script running", file=sys.stderr)

# Initialize Django with minimal setup
try:
    import django
    django.setup(set_prefix=False)
    print("Django initialized", file=sys.stderr)
except Exception as e:
    print(f"Error initializing Django: {str(e)}", file=sys.stderr)
    sys.exit(1)

# Preload minimal translations
try:
    from django.core.cache import cache

    # Set a flag in the cache to indicate we've been preloaded
    cache.set('translation_service_preloaded', True, 3600)

    # Only load minimal translations
    from translation_app.services import get_translation_service
    translation_service = get_translation_service()

    if hasattr(translation_service, '_load_minimal_translations'):
        translation_service._load_minimal_translations()
        print("Minimal translations preloaded", file=sys.stderr)
except Exception as e:
    print(f"Error preloading translations: {str(e)}", file=sys.stderr)

print("WSGI preload script completed", file=sys.stderr)
