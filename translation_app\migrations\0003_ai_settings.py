"""
Migration for AI settings model.
"""

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('translation_app', '0002_ai_integration'),
    ]

    operations = [
        migrations.CreateModel(
            name='AITranslationSettings',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ai_translation_enabled', models.BooleanField(default=True, help_text='Enable or disable AI translation')),
                ('confidence_threshold', models.FloatField(default=0.7, help_text='Minimum confidence score required to use AI translations')),
                ('require_review', models.BooleanField(default=True, help_text='Require human review before adding AI translations to the database')),
                ('logging_level', models.CharField(choices=[('none', 'No Logging'), ('error', 'Errors Only'), ('all', 'Log All Translations')], default='all', help_text='Level of logging for AI translations', max_length=10)),
                ('max_daily_translations', models.IntegerField(default=0, help_text='Maximum number of AI translations per day (0 = unlimited)')),
                ('last_modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'AI Translation Settings',
                'verbose_name_plural': 'AI Translation Settings',
            },
        ),
    ]
