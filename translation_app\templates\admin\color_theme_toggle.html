{% load i18n static %}
<div class="theme-toggle">
  <button type="button" data-theme="light" aria-pressed="false" aria-label="{% translate "Light mode" %}">
    <svg class="theme-icon" aria-hidden="true">
      <use href="#icon-sun"></use>
    </svg>
  </button>
  <button type="button" data-theme="dark" aria-pressed="false" aria-label="{% translate "Dark mode" %}">
    <svg class="theme-icon" aria-hidden="true">
      <use href="#icon-moon"></use>
    </svg>
  </button>
  <button type="button" data-theme="auto" aria-pressed="false" aria-label="{% translate "Auto mode" %}">
    <svg class="theme-icon" aria-hidden="true">
      <use href="#icon-auto"></use>
    </svg>
  </button>
</div>
