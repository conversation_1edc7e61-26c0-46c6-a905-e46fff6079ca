"""
Admin views for translation management.
"""

from django.contrib import admin
from django.shortcuts import render, redirect
from django.contrib import messages
from django.contrib.admin.views.decorators import staff_member_required
import threading
import logging
import time

logger = logging.getLogger(__name__)

@staff_member_required
def translation_management(request):
    """Admin view for translation management"""
    context = {
        'title': 'Translation Management',
        'site_title': admin.site.site_title,
        'site_header': admin.site.site_header,
        'has_permission': request.user.is_staff,
    }
    
    if request.method == 'POST':
        action = request.POST.get('action')
        
        if action == 'update_attention':
            # Run the script in a background thread to avoid blocking the admin interface
            def run_update_attention():
                try:
                    from django.core.management import call_command
                    from translation_app.services import get_translation_service
                    
                    # First, run the optimize_system command with attention focus
                    logger.info("Starting attention mechanism update...")
                    call_command('optimize_system', attention_focus=True)
                    
                    # Then reload the translation service
                    translation_service = get_translation_service()
                    translation_service.load_translations(clear_cache=True)
                    
                    logger.info("Attention mechanism update completed successfully")
                except Exception as e:
                    logger.error(f"Error in attention mechanism update: {str(e)}")
            
            # Start the thread
            thread = threading.Thread(target=run_update_attention)
            thread.daemon = True
            thread.start()
            
            messages.success(request, "Attention mechanism update started in the background. This may take a few minutes.")
            
        elif action == 'update_static':
            # Run the script in a background thread to avoid blocking the admin interface
            def run_update_static():
                try:
                    from django.core.management import call_command
                    
                    # Run the update_static_translations command
                    logger.info("Starting static translations update...")
                    call_command('update_static_translations')
                    
                    # Also create a reduced version for mobile/low-bandwidth
                    call_command('update_static_translations', reduced=True)
                    
                    logger.info("Static translations update completed successfully")
                except Exception as e:
                    logger.error(f"Error in static translations update: {str(e)}")
            
            # Start the thread
            thread = threading.Thread(target=run_update_static)
            thread.daemon = True
            thread.start()
            
            messages.success(request, "Static translations update started in the background. This may take a few minutes.")
            
        elif action == 'update_feedback':
            # Run the script in a background thread to avoid blocking the admin interface
            def run_update_feedback():
                try:
                    from django.core.management import call_command
                    from translation_app.models import TranslationFeedback
                    
                    # Get count of pending feedback before processing
                    pending_count = TranslationFeedback.objects.filter(processed=False).count()
                    
                    # Run the process_feedback command
                    logger.info(f"Starting feedback processing for {pending_count} pending items...")
                    call_command('process_feedback')
                    
                    # Get count after processing
                    processed_count = pending_count - TranslationFeedback.objects.filter(processed=False).count()
                    
                    logger.info(f"Feedback processing completed. Processed {processed_count} items.")
                except Exception as e:
                    logger.error(f"Error in feedback processing: {str(e)}")
            
            # Start the thread
            thread = threading.Thread(target=run_update_feedback)
            thread.daemon = True
            thread.start()
            
            messages.success(request, "Translation feedback processing started in the background. This may take a few minutes.")
            
        elif action == 'update_translations':
            # Run the script in a background thread to avoid blocking the admin interface
            def run_update_translations():
                try:
                    from django.core.management import call_command
                    from translation_app.services import get_translation_service
                    
                    # First, run the optimize_system command
                    logger.info("Starting translation system update...")
                    call_command('optimize_system', update_metrics=True, load_translations=True)
                    
                    # Then reload the translation service
                    translation_service = get_translation_service()
                    translation_service.load_translations(clear_cache=True)
                    
                    # Finally, update the static files
                    time.sleep(2)  # Small delay to ensure database operations complete
                    call_command('update_static_translations')
                    
                    logger.info("Translation system update completed successfully")
                except Exception as e:
                    logger.error(f"Error in translation system update: {str(e)}")
            
            # Start the thread
            thread = threading.Thread(target=run_update_translations)
            thread.daemon = True
            thread.start()
            
            messages.success(request, "Translation system update started in the background. This may take a few minutes.")
    
    return render(request, 'admin/translation_management.html', context)
