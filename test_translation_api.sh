#!/bin/bash
# Direct API test script for translation functionality
# This script tests the translation API directly using curl

# Configuration
SERVER_URL="http://localhost:8000"
API_ENDPOINT="/api/translate/"
CSRF_TOKEN=""

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to get CSRF token
get_csrf_token() {
    echo -e "${BLUE}Getting CSRF token...${NC}"
    CSRF_TOKEN=$(curl -s -c cookies.txt $SERVER_URL | grep csrftoken | sed 's/.*value="\([^"]*\)".*/\1/')
    
    if [ -z "$CSRF_TOKEN" ]; then
        echo -e "${RED}Failed to get CSRF token. Make sure the server is running.${NC}"
        exit 1
    else
        echo -e "${GREEN}Got CSRF token: ${CSRF_TOKEN}${NC}"
    fi
}

# Function to test translation
test_translation() {
    local source_text="$1"
    local source_lang="$2"
    local target_lang="$3"
    local expected_translation="$4"
    
    echo -e "\n${YELLOW}Testing translation of '${source_text}' from ${source_lang} to ${target_lang}${NC}"
    
    # Make the API request
    RESPONSE=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -H "X-CSRFToken: $CSRF_TOKEN" \
        -b cookies.txt \
        -d "{\"text\": \"$source_text\", \"source_lang\": \"$source_lang\", \"target_lang\": \"$target_lang\"}" \
        $SERVER_URL$API_ENDPOINT)
    
    # Extract the translation from the response
    TRANSLATION=$(echo $RESPONSE | grep -o '"translation":"[^"]*"' | sed 's/"translation":"//g' | sed 's/"//g')
    SOURCE=$(echo $RESPONSE | grep -o '"source":"[^"]*"' | sed 's/"source":"//g' | sed 's/"//g')
    
    echo -e "${BLUE}Response: $RESPONSE${NC}"
    echo -e "${BLUE}Extracted translation: $TRANSLATION${NC}"
    echo -e "${BLUE}Source: $SOURCE${NC}"
    
    # Check if the translation matches the expected result
    if [ -n "$expected_translation" ] && [ "$TRANSLATION" = "$expected_translation" ]; then
        echo -e "${GREEN}SUCCESS: Translation matches expected result!${NC}"
        return 0
    elif [ -n "$expected_translation" ]; then
        echo -e "${RED}FAILURE: Translation does not match expected result.${NC}"
        echo -e "${RED}Expected: $expected_translation${NC}"
        echo -e "${RED}Got: $TRANSLATION${NC}"
        return 1
    else
        echo -e "${YELLOW}No expected translation provided. Manual verification required.${NC}"
        return 0
    fi
}

# Function to test the reload translations API
test_reload_translations() {
    echo -e "\n${YELLOW}Testing reload translations API...${NC}"
    
    # Make the API request
    RESPONSE=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -H "X-CSRFToken: $CSRF_TOKEN" \
        -b cookies.txt \
        $SERVER_URL/api/reload_translations/)
    
    echo -e "${BLUE}Response: $RESPONSE${NC}"
    
    # Check if the response indicates success
    if echo $RESPONSE | grep -q '"success":true'; then
        echo -e "${GREEN}SUCCESS: Translations reloaded successfully!${NC}"
        return 0
    else
        echo -e "${RED}FAILURE: Failed to reload translations.${NC}"
        return 1
    fi
}

# Main script
echo "====================================="
echo "Translation API Test"
echo "====================================="
echo "Testing if the translation API correctly handles longer phrases"
echo "and if approved translations are properly loaded."
echo

# Check if the server is running
if ! curl -s $SERVER_URL > /dev/null; then
    echo -e "${RED}Error: Server is not running at $SERVER_URL${NC}"
    echo "Please start the server before running this test."
    exit 1
fi

# Get CSRF token
get_csrf_token

# Test some basic translations
test_translation "kumusta" "tgl" "ted" ""
test_translation "salamat" "tgl" "ted" ""

# Test the longer phrase that was having issues
echo -e "\n${YELLOW}Testing the longer phrase that was having issues...${NC}"
LONG_PHRASE_SUCCESS=$(test_translation "Pumunta ka sa bahay kahapon" "tgl" "ted" "Mënangëy go diyo natëmëgëno")

# Test the "baka" translation
echo -e "\n${YELLOW}Testing 'baka' translation...${NC}"
BAKA_SUCCESS=$(test_translation "baka" "tgl" "ted" "beengk")

# Test reloading translations
echo -e "\n${YELLOW}Testing reload translations API...${NC}"
test_reload_translations

# Test translations again after reload
echo -e "\n${YELLOW}Testing translations again after reload...${NC}"
test_translation "Pumunta ka sa bahay kahapon" "tgl" "ted" "Mënangëy go diyo natëmëgëno"
test_translation "baka" "tgl" "ted" "beengk"

# Clean up
rm -f cookies.txt

echo
echo "====================================="
echo "Test completed"
echo "====================================="
echo -e "${GREEN}Done!${NC}"
