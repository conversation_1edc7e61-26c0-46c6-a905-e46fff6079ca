<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/teduray-theme.css' %}">
</head>
<body>
    <div class="philippines-colors">
        <div class="color-blue"></div>
        <div class="color-red"></div>
        <div class="color-yellow"></div>
    </div>

    <header class="header">
        <div class="container">
            <div class="row">
                <div class="col-md-12 position-relative">
                    {% if user.is_staff %}
                    <div class="position-absolute top-0 end-0">
                        <div class="d-flex align-items-center text-white">
                            <span class="me-2">Welcome Admin</span>
                            <form method="post" action="{% url 'admin:logout' %}" style="display: inline;">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-sm btn-outline-light">Logout</button>
                            </form>
                        </div>
                    </div>
                    {% endif %}
                    <h1 class="text-center">Tagalog-Teduray Dictionary</h1>
                    <p class="text-center mb-0">Preserving Mindanao's Cultural Heritage</p>
                </div>
            </div>
        </div>
    </header>

    <main class="container">
        <div class="row mb-4">
            <div class="col-md-12">
                <ul class="nav nav-pills nav-fill">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'translation:index' %}">Translator</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{% url 'translation:dictionary' %}">Dictionary</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'translation:phrases' %}">Phrases</a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="content-container">
            <div class="search-container">
                <h4 class="section-title mb-4"><i class="bi bi-search me-2"></i>Dictionary Search</h4>
                <form method="get" action="{% url 'translation:dictionary' %}" class="mb-4">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="form-group">
                                <label for="sourceLanguage">Source Language:</label>
                                <select class="form-select" id="sourceLanguage" name="source_lang">
                                    {% for language in languages %}
                                    <option value="{{ language.code }}" {% if language.code == source_lang %}selected{% endif %}>{{ language.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="form-group">
                                <label for="targetLanguage">Target Language:</label>
                                <select class="form-select" id="targetLanguage" name="target_lang">
                                    {% for language in languages %}
                                    <option value="{{ language.code }}" {% if language.code == target_lang %}selected{% endif %}>{{ language.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="form-group">
                                <label for="searchQuery">Search:</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="searchQuery" name="q" value="{{ query }}" placeholder="Enter word to search...">
                                    <button class="btn btn-primary" type="submit">
                                        <i class="bi bi-search me-1"></i> Search
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- Decorative tribal pattern for mobile -->
                <div class="tribal-pattern d-md-none"></div>
            </div>

            <div class="results-container">
                {% if query %}
                    <h4 class="section-title mb-4"><i class="bi bi-journal-text me-2"></i>Search Results for "{{ query }}"</h4>

                    {% if results %}
                        {% for result in results %}
                            <div class="word-item">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h5 class="mb-0">{{ result.word }}</h5>
                                    <span class="badge bg-primary rounded-pill">{{ result.part_of_speech }}</span>
                                </div>
                                <p class="mb-1"><strong>Translation:</strong> {{ result.translation }}</p>

                                {% if result.notes %}
                                    <p class="mb-1"><strong>Notes:</strong> {{ result.notes }}</p>
                                {% endif %}

                                {% if result.cultural_notes %}
                                    <p class="mb-1"><strong>Cultural Context:</strong> {{ result.cultural_notes }}</p>
                                {% endif %}

                                {% if result.examples %}
                                    <div class="mt-3">
                                        <h6>Examples:</h6>
                                        {% for example in result.examples %}
                                            <div class="example-item">
                                                <p class="mb-1">{{ example.source }}</p>
                                                <p class="mb-0 text-muted">→ {{ example.target }}</p>
                                            </div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        {% endfor %}

                        {% if page_obj.has_other_pages %}
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?q={{ query }}&source_lang={{ source_lang }}&target_lang={{ target_lang }}&page={{ page_obj.previous_page_number }}">Previous</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">Previous</span>
                                        </li>
                                    {% endif %}

                                    {% for i in page_obj.paginator.page_range %}
                                        {% if page_obj.number == i %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ i }}</span>
                                            </li>
                                        {% else %}
                                            <li class="page-item">
                                                <a class="page-link" href="?q={{ query }}&source_lang={{ source_lang }}&target_lang={{ target_lang }}&page={{ i }}">{{ i }}</a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?q={{ query }}&source_lang={{ source_lang }}&target_lang={{ target_lang }}&page={{ page_obj.next_page_number }}">Next</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">Next</span>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="alert alert-info">
                            No results found for "{{ query }}". Try a different search term.
                        </div>
                    {% endif %}
                {% else %}
                    <div class="text-center py-5">
                        <h5>Enter a word to search in the dictionary</h5>
                        <p class="text-muted">Search for words in Tagalog or Teduray to find their translations</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <!-- About and Contact sections hidden as requested -->

            {% if user.is_staff %}
            <div class="row mt-3 mb-3">
                <div class="col-md-12">
                    <h5 class="mb-3"><i class="bi bi-gear-fill me-2"></i>Admin Tools</h5>
                    <div class="d-flex flex-wrap gap-2">
                        <a href="{% url 'translation:metrics_dashboard' %}" class="btn btn-sm btn-light rounded-pill">
                            <i class="bi bi-graph-up me-1"></i> Metrics Dashboard
                        </a>
                        <a href="{% url 'translation:add_translation' %}" class="btn btn-sm btn-light rounded-pill">
                            <i class="bi bi-plus-circle me-1"></i> Add Translation
                        </a>
                        <a href="/admin/" class="btn btn-sm btn-light rounded-pill">
                            <i class="bi bi-shield-lock me-1"></i> Django Admin
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}

            <div class="text-center mt-3">
                <div class="tribal-pattern mb-3" style="opacity: 0.5;"></div>
                <p class="mb-0">&copy; 2025 Tagalog-Teduray Translator. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Switch languages
            const sourceLanguage = document.getElementById('sourceLanguage');
            const targetLanguage = document.getElementById('targetLanguage');

            sourceLanguage.addEventListener('change', function() {
                if (sourceLanguage.value === targetLanguage.value) {
                    targetLanguage.value = sourceLanguage.value === 'tgl' ? 'ted' : 'tgl';
                }
            });

            targetLanguage.addEventListener('change', function() {
                if (targetLanguage.value === sourceLanguage.value) {
                    sourceLanguage.value = targetLanguage.value === 'tgl' ? 'ted' : 'tgl';
                }
            });
        });
    </script>
</body>
</html>
