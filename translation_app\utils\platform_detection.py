"""
Platform detection utilities for the translation app.
This helps optimize the app for different hosting environments.
"""

import os
import sys
import logging

logger = logging.getLogger(__name__)

def is_pythonanywhere():
    """
    Detect if the app is running on PythonAnywhere.

    Returns:
        bool: True if running on PythonAnywhere, False otherwise
    """
    # Check for PythonAnywhere-specific environment variables or paths
    if 'PYTHONANYWHERE_DOMAIN' in os.environ:
        return True

    # Check for PythonAnywhere in the hostname
    try:
        import socket
        if 'pythonanywhere' in socket.gethostname().lower():
            return True
    except:
        pass

    # Check for PythonAnywhere in the sys.path
    for path in sys.path:
        if 'pythonanywhere' in path.lower():
            return True

    return False

def get_platform_settings():
    """
    Get platform-specific settings.

    Returns:
        dict: Platform-specific settings
    """
    settings = {
        'use_threading': True,
        'max_static_file_size': 10 * 1024 * 1024,  # 10MB default
        'db_retry_count': 3,
        'db_retry_delay': 1,  # seconds
        'use_background_tasks': True
    }

    # Adjust settings for PythonAnywhere
    if is_pythonanywhere():
        logger.info("Detected PythonAnywhere environment, optimizing settings")
        settings.update({
            'use_threading': False,  # Avoid threading on PythonAnywhere
            'max_static_file_size': 5 * 1024 * 1024,  # 5MB for PythonAnywhere
            'db_retry_count': 5,  # More retries on PythonAnywhere
            'db_retry_delay': 2,  # Longer delay between retries
            'use_background_tasks': True,  # Allow immediate processing of feedback
            'process_feedback_immediately': True  # Process feedback immediately
        })

    return settings

# Get platform settings on module import
PLATFORM_SETTINGS = get_platform_settings()

def log_platform_info():
    """Log information about the detected platform"""
    if is_pythonanywhere():
        logger.info("Running on PythonAnywhere with optimized settings")
    else:
        logger.info("Running on standard platform with default settings")

    logger.info(f"Platform settings: {PLATFORM_SETTINGS}")

# Log platform info when module is imported
log_platform_info()
