import os
import logging
import time
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.core.management import call_command

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Automatically update the static translations file when changes are detected'

    def add_arguments(self, parser):
        parser.add_argument(
            '--interval',
            type=int,
            default=60,
            help='Interval in seconds between checks (default: 60)'
        )
        parser.add_argument(
            '--watch-path',
            type=str,
            default='logs/translation_updates.log',
            help='Path to watch for changes (default: logs/translation_updates.log)'
        )

    def handle(self, *args, **options):
        interval = options['interval']
        watch_path = options['watch_path']
        
        self.stdout.write(f'Starting auto-update watcher with interval {interval} seconds')
        self.stdout.write(f'Watching path: {watch_path}')
        
        # Create the log file if it doesn't exist
        log_dir = os.path.dirname(watch_path)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        
        if not os.path.exists(watch_path):
            with open(watch_path, 'w') as f:
                f.write(f'{timezone.now().strftime("%Y-%m-%d %H:%M:%S")} - Auto-update watcher started\n')
        
        # Get the initial modification time
        try:
            last_mtime = os.path.getmtime(watch_path)
        except OSError:
            last_mtime = 0
        
        self.stdout.write(f'Initial modification time: {last_mtime}')
        
        # Main loop
        try:
            while True:
                try:
                    # Check if the file has been modified
                    current_mtime = os.path.getmtime(watch_path)
                    
                    if current_mtime > last_mtime:
                        self.stdout.write(f'Change detected at {timezone.now().strftime("%Y-%m-%d %H:%M:%S")}')
                        self.stdout.write('Updating static translations file...')
                        
                        # Update the static file
                        call_command('update_static_translations')
                        
                        self.stdout.write(self.style.SUCCESS('Static translations file updated successfully'))
                        
                        # Update the last modification time
                        last_mtime = current_mtime
                    
                    # Sleep for the specified interval
                    time.sleep(interval)
                    
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Error: {str(e)}'))
                    logger.error(f'Error in auto-update watcher: {str(e)}')
                    time.sleep(interval)
        
        except KeyboardInterrupt:
            self.stdout.write(self.style.SUCCESS('Auto-update watcher stopped'))
            return
