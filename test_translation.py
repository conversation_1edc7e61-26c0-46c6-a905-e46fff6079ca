#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to test the translation system's functionality.
This script tests if the translation system is correctly using the database.
"""

import os
import sys
import django
import logging

# Set up Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

# Import Django models and services
from translation_app.models import Language, ComprehensiveTranslation, TranslationVersion
from translation_app.services import get_translation_service
from django.db import transaction

def translate_text(source_text, source_lang, target_lang):
    """
    Test translating a text using the translation service.

    Args:
        source_text: Text to translate
        source_lang: Source language code
        target_lang: Target language code
    """
    logger.info(f"Testing translation of '{source_text}' from {source_lang} to {target_lang}")

    # Get the translation service
    translation_service = get_translation_service()

    # Translate the text
    result = translation_service.translate_text(source_text, source_lang, target_lang)

    # Print the result
    logger.info(f"Translation result: {result['translation']}")
    logger.info(f"Confidence: {result['confidence']}")
    logger.info(f"Source: {result['source']}")

    return result

def add_test_translation(source_text, translation_text, source_lang, target_lang):
    """
    Add a test translation to the database.

    Args:
        source_text: Source text
        translation_text: Translation text
        source_lang: Source language code
        target_lang: Target language code
    """
    logger.info(f"Adding test translation: {source_text} → {translation_text}")

    try:
        # Get language objects
        source_lang_obj = Language.objects.get(code=source_lang)
        target_lang_obj = Language.objects.get(code=target_lang)

        # Create or update the translation
        with transaction.atomic():
            # Check if this translation already exists
            existing = ComprehensiveTranslation.objects.filter(
                base_word__iexact=source_text,
                source_language=source_lang_obj,
                target_language=target_lang_obj
            ).first()

            if existing:
                # Update existing translation
                existing.translation = translation_text
                existing.notes = (existing.notes or '') + '\nUpdated via test script'
                existing.save()
                logger.info(f"Updated existing translation in database")

                # Create a new version with high confidence
                TranslationVersion.objects.create(
                    comprehensive_translation=existing,
                    translation=translation_text,
                    confidence_score=0.95,
                    is_active=True,
                    notes='Updated via test script'
                )
            else:
                # Create new translation
                new_translation = ComprehensiveTranslation.objects.create(
                    base_word=source_text,
                    translation=translation_text,
                    source_language=source_lang_obj,
                    target_language=target_lang_obj,
                    part_of_speech='phrase' if ' ' in source_text else 'word',
                    notes='Added via test script'
                )
                logger.info(f"Created new translation in database")

                # Create initial version with high confidence
                TranslationVersion.objects.create(
                    comprehensive_translation=new_translation,
                    translation=translation_text,
                    confidence_score=0.95,
                    is_active=True,
                    notes='Created via test script'
                )

        # Update the translation service
        translation_service = get_translation_service()
        translation_service.update_single_translation(
            source_text,
            translation_text,
            source_lang,
            target_lang
        )

        logger.info(f"Successfully added test translation to database and updated service")
        return True
    except Exception as e:
        logger.error(f"Error adding test translation: {str(e)}")
        return False

def test_database_learning():
    """Test if the system learns from the database correctly."""
    logger.info("Testing database learning functionality")

    # Test data
    test_word = "test_word_" + str(hash(str(django.utils.timezone.now())))[0:6]
    test_translation = "test_translation_" + str(hash(str(django.utils.timezone.now())))[0:6]

    # First, try to translate the word (should fail or use fuzzy matching)
    logger.info("Step 1: Translating before adding to database")
    before_result = translate_text(test_word, 'tgl', 'ted')

    # Add the translation to the database
    logger.info("Step 2: Adding translation to database")
    add_test_translation(test_word, test_translation, 'tgl', 'ted')

    # Clear the translation service cache to force a reload from the database
    logger.info("Step 3: Clearing translation service cache")
    translation_service = get_translation_service()
    translation_service.recent_translations_cache = {}  # Clear the cache

    # Now try to translate again (should use the database)
    logger.info("Step 4: Translating after adding to database")
    after_result = translate_text(test_word, 'tgl', 'ted')

    # Check if the system learned from the database
    if after_result['translation'] == test_translation and after_result['source'] == 'database':
        logger.info("SUCCESS: System correctly learned from the database!")
        return True
    else:
        logger.error(f"FAILURE: System did not learn from database. Got: {after_result['translation']}, Source: {after_result['source']}, Expected: {test_translation}")
        return False

def test_specific_translation():
    """Test a specific translation that was having issues."""
    logger.info("Testing specific translation that was having issues")

    # Test the specific translation
    result = translate_text("umuwi ka na", "tgl", "ted")

    # Check if the translation is correct
    if result['translation'] == "fiyo go béléy fo":
        logger.info("SUCCESS: Translation is correct!")
        return True
    else:
        logger.error(f"FAILURE: Translation is incorrect. Got: {result['translation']}, Expected: fiyo go béléy fo")
        return False

def test_long_phrase_translation():
    """Test the longer phrase translation that was having issues."""
    logger.info("Testing longer phrase translation that was having issues")

    # Test the specific long phrase
    result = translate_text("Pumunta ka sa bahay kahapon", "tgl", "ted")

    # Log the result regardless of what we get
    logger.info(f"Translation result for 'Pumunta ka sa bahay kahapon': {result['translation']}")
    logger.info(f"Source: {result['source']}")

    # Check if the translation is correct (expected translation from the issue description)
    expected = "Mënangëy go diyo natëmëgëno"
    if result['translation'] == expected:
        logger.info(f"SUCCESS: Long phrase translation is correct! Got: {result['translation']}")
        return True
    else:
        logger.error(f"FAILURE: Long phrase translation is incorrect. Got: {result['translation']}, Expected: {expected}")

        # Add the correct translation to the database if it's not there
        logger.info("Adding the correct translation to the database...")
        add_test_translation("Pumunta ka sa bahay kahapon", expected, "tgl", "ted")

        # Try translating again after adding to the database
        logger.info("Translating again after adding to the database...")

        # Get a fresh translation service instance
        translation_service = get_translation_service()
        translation_service.reload_translations(force_load=True)

        # Try translating again
        result2 = translate_text("Pumunta ka sa bahay kahapon", "tgl", "ted")

        if result2['translation'] == expected:
            logger.info(f"SUCCESS after database update: Long phrase translation is now correct! Got: {result2['translation']}")
            return True
        else:
            logger.error(f"FAILURE after database update: Long phrase translation is still incorrect. Got: {result2['translation']}, Expected: {expected}")
            return False

if __name__ == '__main__':
    logger.info("Starting translation system test")

    # Test some known translations
    logger.info("Testing known translations:")
    translate_text("kumusta", "tgl", "ted")
    translate_text("salamat", "tgl", "ted")

    # Test the longer phrase that was having issues
    logger.info("\nTesting longer phrase translation:")
    long_phrase_success = test_long_phrase_translation()

    # Test the specific translation that was having issues
    logger.info("\nTesting specific translation:")
    specific_success = test_specific_translation()

    # Test database learning
    logger.info("\nTesting database learning:")
    db_success = test_database_learning()

    # Test the translation of "baka" to "beengk"
    logger.info("\nTesting 'baka' translation:")
    baka_result = translate_text("baka", "tgl", "ted")
    if baka_result['translation'] == "beengk":
        logger.info("SUCCESS: 'baka' translation is correct!")
        baka_success = True
    else:
        logger.error(f"FAILURE: 'baka' translation is incorrect. Got: {baka_result['translation']}, Expected: beengk")
        logger.info("Adding the correct translation to the database...")
        add_test_translation("baka", "beengk", "tgl", "ted")
        baka_success = False

    # Summarize all test results
    logger.info("\n=== TEST SUMMARY ===")
    logger.info(f"Long phrase translation test: {'PASSED' if long_phrase_success else 'FAILED'}")
    logger.info(f"Specific translation test: {'PASSED' if specific_success else 'FAILED'}")
    logger.info(f"Database learning test: {'PASSED' if db_success else 'FAILED'}")
    logger.info(f"'baka' translation test: {'PASSED' if baka_success else 'FAILED'}")

    if long_phrase_success and specific_success and db_success and baka_success:
        logger.info("All tests completed successfully!")
    else:
        logger.error("Some tests failed. See log for details.")
