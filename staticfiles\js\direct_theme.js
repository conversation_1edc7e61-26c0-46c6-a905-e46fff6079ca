// Direct theme toggle implementation
document.addEventListener('DOMContentLoaded', function() {
    // Function to set theme
    function setTheme(themeName) {
        // Set the theme attribute on html element
        document.documentElement.setAttribute('data-theme', themeName);
        
        // Store the theme preference
        localStorage.setItem('theme', themeName);
        
        console.log('Theme set to:', themeName);
    }

    // Function to toggle between themes
    function toggleTheme() {
        // Get current theme
        const currentTheme = localStorage.getItem('theme') || 'light';
        
        // Toggle between light and dark
        if (currentTheme === 'light') {
            setTheme('dark');
        } else {
            setTheme('light');
        }
    }

    // Initialize theme
    function initTheme() {
        // Get stored theme or use light as default
        const storedTheme = localStorage.getItem('theme') || 'light';
        setTheme(storedTheme);
        
        console.log('Theme initialized to:', storedTheme);
    }
    
    // Add a simple theme toggle button
    function addThemeToggle() {
        // Find the user tools section
        const userTools = document.querySelector('#user-tools');
        if (!userTools) return;
        
        // Create toggle button
        const toggleButton = document.createElement('button');
        toggleButton.id = 'theme-toggle-btn';
        toggleButton.innerHTML = 'Toggle Theme';
        toggleButton.style.marginLeft = '10px';
        toggleButton.style.padding = '4px 8px';
        toggleButton.style.backgroundColor = 'transparent';
        toggleButton.style.border = '1px solid #fff';
        toggleButton.style.borderRadius = '4px';
        toggleButton.style.color = '#fff';
        toggleButton.style.cursor = 'pointer';
        
        // Add click handler
        toggleButton.addEventListener('click', function(e) {
            e.preventDefault();
            toggleTheme();
        });
        
        // Add to user tools
        userTools.appendChild(toggleButton);
        
        console.log('Theme toggle button added');
    }
    
    // Initialize
    initTheme();
    addThemeToggle();
});
