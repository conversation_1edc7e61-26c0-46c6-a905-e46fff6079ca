# Translation System Optimization

This document explains how to optimize the translation system to avoid database locks and improve performance.

## Background

The translation system has several automatic processes that can cause database locks and slow down the admin interface:

1. **Database Optimization Middleware**: Applies SQLite optimizations for every request
2. **Metrics Update Middleware**: Periodically updates metrics in the background
3. **Translation Service Loading**: Loads translations progressively when first accessed
4. **Static File Updates**: Updates the static translations file when translations change

These processes have been disabled for admin requests to improve performance, but they still need to run periodically to keep the system up-to-date.

## Automatic Processes

The system now has a setting to completely disable automatic processes on startup:

```python
# In settings.py
DISABLE_AUTO_PROCESSES = True  # Disable all automatic processes
```

When this setting is enabled:
- No automatic translation loading on startup
- No automatic static file updates on startup
- No automatic metrics updates
- Signal handlers will not trigger static file updates

This significantly improves performance and prevents database locks, especially in the PythonAnywhere environment.

## Running Optimization Manually

### Windows

1. Double-click the `optimize_translation_system.bat` file
2. Choose one of the options:
   - Run optimization only
   - Run optimization and enable automatic processes
   - Run optimization and disable automatic processes
   - Process improved translations only
3. Wait for the optimization to complete
4. Check the `optimize_system.log` file for any errors

### Command Line (Any OS)

```bash
# Run optimization only
python optimize_translation_system.py

# Run optimization and enable automatic processes
python optimize_translation_system.py --enable-auto

# Run optimization and disable automatic processes
python optimize_translation_system.py --disable-auto

# Process improved translations only
python optimize_translation_system.py --process-improved
```

Or run the Django management command directly:

```bash
# Run all optimization tasks
python manage.py optimize_system --all

# Run all optimization tasks and enable automatic processes
python manage.py optimize_system --all --enable-auto-processes

# Run all optimization tasks and disable automatic processes
python manage.py optimize_system --all --disable-auto-processes

# Process improved translations only
python manage.py optimize_system --process-improved-translations
```

## Setting Up a Scheduled Task

### PythonAnywhere

In PythonAnywhere, you can set up scheduled tasks to run the optimization script:

1. Go to the "Tasks" tab in your PythonAnywhere dashboard
2. Add a new scheduled task for full optimization:
   ```
   cd /home/<USER>/teduray && python optimize_translation_system.py
   ```
   Set the schedule (e.g., daily at 3:00 AM)

3. Add another scheduled task for processing improved translations more frequently:
   ```
   cd /home/<USER>/teduray && python optimize_translation_system.py --process-improved
   ```
   Set the schedule (e.g., hourly or every 3 hours)

4. Click "Add Task" for each task

This setup allows you to run the full optimization less frequently (which is more resource-intensive) while still processing improved translations more often to keep the system up-to-date.

### Windows Task Scheduler

1. Open Task Scheduler
2. Create a new task
3. Set the trigger (e.g., daily at 3:00 AM)
4. Set the action to start a program:
   - Program/script: `C:\path\to\python.exe`
   - Arguments: `C:\path\to\optimize_translation_system.py`
   - Start in: `C:\path\to\translation\project`
5. Save the task

## Running Specific Optimization Tasks

If you only want to run specific optimization tasks, you can use the following commands:

```bash
# Update metrics only
python manage.py optimize_system --update-metrics

# Update static translations file only
python manage.py optimize_system --update-static-file

# Load translations only
python manage.py optimize_system --load-translations

# Optimize database only
python manage.py optimize_system --optimize-db
```

## Troubleshooting

If you encounter any issues:

1. Check the `optimize_system.log` file for error messages
2. Make sure the database is not locked by another process
3. Try running the optimization tasks one by one to identify the problematic task
4. If the issue persists, try restarting the server

## Performance Impact

Running the optimization script may temporarily slow down the system, so it's best to run it during off-peak hours.

The script typically takes less than a minute to complete, but it may take longer if there are many translations to process.
