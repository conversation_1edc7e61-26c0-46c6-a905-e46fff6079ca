"""
Simple phrase learning system for the translation app.
"""
from django.utils import timezone
from .models import Language, ComprehensiveTranslation, TranslationVersion, TranslationExample

class PhraseLearner:
    """Simple phrase learning system"""
    
    def learn_phrase(self, source_phrase, target_phrase, source_lang, target_lang):
        """Learn a phrase translation"""
        try:
            # Get language objects
            source_language = Language.objects.get(code=source_lang)
            target_language = Language.objects.get(code=target_lang)
            
            # Check if translation already exists
            existing = ComprehensiveTranslation.objects.filter(
                base_word=source_phrase,
                source_language=source_language,
                target_language=target_language
            ).first()
            
            if existing:
                # Update if confidence is low
                current_version = existing.versions.filter(is_active=True).first()
                if current_version and current_version.confidence_score < 0.85:
                    # Create new version
                    TranslationVersion.objects.create(
                        comprehensive_translation=existing,
                        translation=target_phrase,
                        created_by='learner',
                        confidence_score=0.85,
                        is_active=True,
                        notes='Learned from feedback'
                    )
                    return True, "Updated existing phrase"
            else:
                # Create new translation
                new_trans = ComprehensiveTranslation.objects.create(
                    base_word=source_phrase,
                    translation=target_phrase,
                    source_language=source_language,
                    target_language=target_language,
                    part_of_speech='phrase',
                    notes='Learned from feedback'
                )
                
                # Create initial version
                TranslationVersion.objects.create(
                    comprehensive_translation=new_trans,
                    translation=target_phrase,
                    created_by='learner',
                    confidence_score=0.85,
                    is_active=True,
                    notes='Initial version from learning'
                )
                
                # Create example
                TranslationExample.objects.create(
                    comprehensive_translation=new_trans,
                    source_text=source_phrase,
                    target_text=target_phrase
                )
                
                return True, "Added new phrase"
            
            return False, "No update needed"
            
        except Exception as e:
            print(f"Error learning phrase: {str(e)}")
            return False, str(e)

# Create singleton instance
phrase_learner = PhraseLearner()
