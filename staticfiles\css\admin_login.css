/* Custom CSS for Admin Login Page */

body.login-page {
    background: linear-gradient(135deg, #f5f0e1 0%, #e8e4d8 100%);
    background-size: cover;
    background-repeat: no-repeat;
    background-attachment: fixed;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-box {
    width: 400px;
    margin: 0 auto;
}

.login-logo {
    margin-bottom: 20px;
    text-align: center;
}

.login-logo h1 {
    font-size: 28px;
    color: #333;
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.login-card {
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: none;
    overflow: hidden;
}

.login-card-body {
    padding: 30px;
    border-radius: 10px;
    background-color: #fff;
}

.login-box-msg {
    margin-bottom: 15px;
    font-size: 16px;
    color: #555;
    text-align: center;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    transition: all 0.3s ease;
    padding: 10px;
    font-weight: 500;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
}

.form-control {
    height: 45px;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.alert-danger {
    border-radius: 5px;
}

.mt-3 {
    margin-top: 1rem !important;
}

.mb-1 {
    margin-bottom: 0.25rem !important;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.mb-3 {
    margin-bottom: 1rem !important;
}

a {
    color: #007bff;
    text-decoration: none;
    transition: color 0.2s ease;
}

a:hover {
    color: #0056b3;
    text-decoration: underline;
}

@media (max-width: 576px) {
    .login-box {
        width: 90%;
    }
}
