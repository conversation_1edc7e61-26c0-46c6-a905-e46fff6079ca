import os
import logging
from django.core.management.base import BaseCommand
from django.utils import timezone
from translation_app.management.commands.export_translations import Command as ExportCommand
from translation_app.utils.platform_detection import PLATFORM_SETTINGS

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Update the static translations file and set up a scheduled task to do this regularly'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reduced',
            action='store_true',
            help='Create a reduced version of the static file with only essential translations'
        )
        parser.add_argument(
            '--max-size',
            type=int,
            default=PLATFORM_SETTINGS['max_static_file_size'],
            help=f'Maximum size of the static file in bytes (default: {PLATFORM_SETTINGS["max_static_file_size"]})'
        )

    def handle(self, *args, **options):
        reduced = options.get('reduced', False)
        max_size = options.get('max_size', PLATFORM_SETTINGS['max_static_file_size'])

        if reduced:
            self.stdout.write('Creating reduced static translations file...')
        else:
            self.stdout.write('Updating static translations file...')

        try:
            # Run the export_translations command with appropriate options
            export_command = ExportCommand()

            if reduced:
                # Pass options to create a reduced version
                export_command.handle(
                    reduced=True,
                    max_size=max_size,
                    essential_only=True
                )
                self.stdout.write(self.style.SUCCESS('Successfully created reduced static translations file'))
            else:
                # Normal export
                export_command.handle()
                self.stdout.write(self.style.SUCCESS('Successfully updated static translations file'))

            # Create a log entry
            log_dir = os.path.join('logs')
            os.makedirs(log_dir, exist_ok=True)

            log_file = os.path.join(log_dir, 'translation_updates.log')
            with open(log_file, 'a') as f:
                if reduced:
                    f.write(f'{timezone.now().strftime("%Y-%m-%d %H:%M:%S")} - Reduced static translations file updated\n')
                else:
                    f.write(f'{timezone.now().strftime("%Y-%m-%d %H:%M:%S")} - Static translations file updated\n')

            self.stdout.write(f'Log entry added to {log_file}')

            # Check the file size
            static_file_path = os.path.join('static', 'translation_app', 'js', 'all_translations.js')
            if os.path.exists(static_file_path):
                file_size = os.path.getsize(static_file_path)
                self.stdout.write(f'Static file size: {file_size} bytes')

                if file_size > max_size and not reduced:
                    self.stdout.write(self.style.WARNING(
                        f'Static file is larger than the maximum allowed size ({max_size} bytes). '
                        f'Consider using the --reduced option to create a smaller file.'
                    ))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error updating static translations file: {str(e)}'))
            logger.error(f'Error updating static translations file: {str(e)}')
