/* Direct CSS overrides for admin interface */

/* Force light theme on all elements */
html, body, #container, #content, .module, .form-row, input, select, textarea {
    background-color: #F5F0E1 !important;
    color: #006837 !important;
}

/* Header styling */
#header {
    background-color: #006837 !important;
    color: #FFC72C !important;
}

#header a:link, #header a:visited {
    color: white !important;
}

#branding h1, #branding h1 a:link, #branding h1 a:visited {
    color: #FFC72C !important;
}

/* Module headers */
.module h2, .module caption, .inline-group h2 {
    background-color: #006837 !important;
    color: white !important;
}

/* Make sure all text in module headers is white */
.module h2 *, .module caption *, .inline-group h2 * {
    color: white !important;
}

/* Links */
a:link, a:visited {
    color: #006837 !important;
    font-weight: bold !important;
}

a:hover {
    color: #004020 !important;
}

/* Buttons */
.button, input[type=submit], input[type=button], .submit-row input, a.button {
    background-color: #006837 !important;
    color: white !important;
}

.button:hover, input[type=submit]:hover, input[type=button]:hover {
    background-color: #004020 !important;
}

/* Form elements */
input[type=text], input[type=password], input[type=email], input[type=url], input[type=number], input[type=tel], textarea, select, .vTextField {
    background-color: white !important;
    border: 1px solid #ccc !important;
    color: #006837 !important;
}

/* Breadcrumbs */
.breadcrumbs {
    background-color: #006837 !important;
    color: white !important;
}

.breadcrumbs a {
    color: #FFC72C !important;
}

/* Messages */
.messagelist .success {
    background-color: #006837 !important;
    color: white !important;
}

.messagelist .warning {
    background-color: #FFC72C !important;
    color: #006837 !important;
}

.messagelist .error {
    background-color: #ffefef !important;
    color: #ba2121 !important;
}

/* Dashboard */
.dashboard .module table th {
    background-color: #006837 !important;
    color: white !important;
}

.dashboard .module table td a {
    color: #006837 !important;
    font-weight: bold !important;
}

/* App list */
.app-translation_app .app-name {
    color: #006837 !important;
    font-weight: bold !important;
    font-size: 1.1em !important;
}

.app-translation_app .model-name {
    color: #006837 !important;
    font-weight: bold !important;
}

/* Selected rows */
.row1 {
    background-color: #F5F0E1 !important;
}

.row2 {
    background-color: #e5f0e9 !important;
}

tr.selected {
    background-color: #FFC72C !important;
    color: #006837 !important;
}

/* Table headers */
thead th {
    background-color: #006837 !important;
    color: white !important;
}

/* Table cells */
td, th {
    color: #006837 !important;
    font-weight: bold !important;
}

/* App index */
.app-translation_app .app-name, .model-name {
    color: #006837 !important;
    font-weight: bold !important;
}

/* Admin index */
#content h1 {
    color: #006837 !important;
    font-weight: bold !important;
}

/* Module captions */
caption {
    background-color: #006837 !important;
    color: white !important;
    font-weight: bold !important;
}

/* Recent actions */
#recent-actions-module h2 {
    background-color: #006837 !important;
    color: white !important;
}

#recent-actions-module h2 * {
    color: white !important;
}

#recent-actions-module h3 {
    color: #006837 !important;
    font-weight: bold !important;
    font-size: 1.1em !important;
    margin-top: 15px !important;
}

#recent-actions-module ul li {
    color: #006837 !important;
    font-weight: normal !important;
    margin: 5px 0 !important;
}

/* Fix for list items in modules */
.module ul li {
    color: #006837 !important;
    font-weight: normal !important;
}

/* Fix for text on green backgrounds */
.module ul li a {
    color: #006837 !important;
    font-weight: bold !important;
}

/* Fix for any green background with green text */
[style*="background-color: #006837"], [style*="background-color:#006837"],
[class*="background-color: #006837"], [class*="background-color:#006837"],
[style*="background: #006837"], [style*="background:#006837"],
[class*="background: #006837"], [class*="background:#006837"] {
    color: white !important;
}

[style*="background-color: #006837"] *, [style*="background-color:#006837"] *,
[class*="background-color: #006837"] *, [class*="background-color:#006837"] *,
[style*="background: #006837"] *, [style*="background:#006837"] *,
[class*="background: #006837"] *, [class*="background:#006837"] * {
    color: white !important;
}

/* Hide the theme toggle */
.theme-toggle, [data-theme], [class*="theme"] {
    display: none !important;
}

/* Hide the skip link */
.skip-to-content-link, a[href="#content-start"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    width: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    z-index: -9999 !important;
    pointer-events: none !important;
}
