"""
Simple script to add a translation to the database.
"""

import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'translation.settings')
django.setup()

# Import the models
from translation_app.models import ComprehensiveTranslation, Language

# Get language objects
try:
    tagalog = Language.objects.get(code='tgl')
    teduray = Language.objects.get(code='ted')
    
    # Add the translation
    translation, created = ComprehensiveTranslation.objects.get_or_create(
        base_word='babae',
        source_language=tagalog,
        target_language=teduray,
        defaults={
            'translation': 'oboen',
            'part_of_speech': 'noun',
            'notes': 'Added manually'
        }
    )
    
    if created:
        print(f"Added new translation: babae → oboen")
    else:
        print(f"Updated existing translation: babae → oboen")
    
except Exception as e:
    print(f"Error: {str(e)}")
