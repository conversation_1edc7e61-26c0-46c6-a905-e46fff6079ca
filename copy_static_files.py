#!/usr/bin/env python
"""
Script to copy static files from the staticfiles directory to the static directory.
This ensures that the static files are available in both locations.
"""

import os
import sys
import shutil
import logging
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

# Define the directories
STATICFILES_DIR = '/home/<USER>/teduray/staticfiles'
STATIC_DIR = '/home/<USER>/teduray/static'

def copy_static_files():
    """
    Copy static files from the staticfiles directory to the static directory.
    This ensures that the static files are available in both locations.
    """
    logger.info("Copying translation files from staticfiles to static...")

    # Check if the directories exist
    if not os.path.exists(STATICFILES_DIR):
        logger.error(f"Staticfiles directory not found: {STATICFILES_DIR}")
        return False

    if not os.path.exists(STATIC_DIR):
        logger.error(f"Static directory not found: {STATIC_DIR}")
        return False

    try:
        # Copy translation files
        source_js_dir = os.path.join(STATICFILES_DIR, 'translation_app', 'js')
        dest_js_dir = os.path.join(STATIC_DIR, 'translation_app', 'js')

        # Create the destination directory if it doesn't exist
        os.makedirs(dest_js_dir, exist_ok=True)

        # Copy all_translations.js
        all_translations_path = os.path.join(source_js_dir, 'all_translations.js')
        if os.path.exists(all_translations_path):
            shutil.copy2(all_translations_path, os.path.join(dest_js_dir, 'all_translations.js'))
            logger.info(f"Copied all_translations.js to {dest_js_dir}")
        else:
            logger.warning(f"all_translations.js not found in {source_js_dir}")

        # Copy word_mappings.js
        word_mappings_path = os.path.join(source_js_dir, 'word_mappings.js')
        if os.path.exists(word_mappings_path):
            shutil.copy2(word_mappings_path, os.path.join(dest_js_dir, 'word_mappings.js'))
            logger.info(f"Copied word_mappings.js to {dest_js_dir}")
        else:
            logger.warning(f"word_mappings.js not found in {source_js_dir}")

        logger.info("Static files copy complete.")
        return True
    except Exception as e:
        logger.error(f"Error copying static files: {str(e)}")
        return False

if __name__ == "__main__":
    success = copy_static_files()
    sys.exit(0 if success else 1)
