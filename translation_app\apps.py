from django.apps import AppConfig
import threading
import logging

logger = logging.getLogger(__name__)

class TranslationAppConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'translation_app'

    def ready(self):
        """
        Register signal handlers when the app is ready and pre-warm the translation cache.
        """
        # Only run in main thread to avoid running twice in development
        if threading.current_thread() == threading.main_thread():
            # Import and register signals
            import translation_app.signals
            logger.info("Registered translation signal handlers")

            # Import admin modules
            import translation_app.admin
            import translation_app.translation_management_admin
            logger.info("Loaded translation admin modules")

            # Create logs directory
            try:
                from translation_app.utils import create_logs_directory
                create_logs_directory()
                logger.info("Created logs directory")
            except Exception as e:
                logger.error(f"Error creating logs directory: {str(e)}")

            # Check if automatic processes should be disabled
            try:
                from django.conf import settings
                disable_auto_processes = getattr(settings, 'DISABLE_AUTO_PROCESSES', False)

                if disable_auto_processes:
                    logger.info("Automatic processes are disabled. Skipping cache pre-warming and static file updates.")
                    return
            except Exception as e:
                logger.error(f"Error checking auto processes setting: {str(e)}")
                # Continue with default behavior if there's an error

            # Pre-warm the translation cache in a background thread
            threading.Thread(target=self.prewarm_cache, daemon=True).start()
            logger.info("Started cache pre-warming in background thread")

            # Start the automatic static file update watcher in a background thread
            threading.Thread(target=self.start_auto_update_watcher, daemon=True).start()
            logger.info("Started automatic static file update watcher in background thread")

    def prewarm_cache(self):
        """Pre-warm the translation cache to avoid initial loading delay"""
        try:
            # Import here to avoid circular imports
            from translation_app.services import get_translation_service

            # Get the translation service (this will initialize it)
            logger.info("Pre-warming translation cache...")
            translation_service = get_translation_service()

            # Load common translations first for progressive loading
            translation_service.load_common_translations()
            logger.info("Common translations loaded")

            # Then load all translations
            translation_service.load_translations(clear_cache=False)
            logger.info("Translation cache pre-warmed successfully")
        except Exception as e:
            logger.error(f"Error pre-warming translation cache: {str(e)}")

    def start_auto_update_watcher(self):
        """Start the automatic static file update watcher"""
        try:
            import os
            import time
            from django.core.management import call_command
            from django.utils import timezone

            # Create logs directory if it doesn't exist
            logs_dir = os.path.join('logs')
            os.makedirs(logs_dir, exist_ok=True)

            # Create or update the translation updates log file
            log_file = os.path.join(logs_dir, 'translation_updates.log')
            if not os.path.exists(log_file):
                with open(log_file, 'w') as f:
                    f.write(f'{timezone.now().strftime("%Y-%m-%d %H:%M:%S")} - Auto-update watcher started\n')
            else:
                with open(log_file, 'a') as f:
                    f.write(f'{timezone.now().strftime("%Y-%m-%d %H:%M:%S")} - Auto-update watcher restarted\n')

            # Get the initial modification time
            try:
                last_mtime = os.path.getmtime(log_file)
            except OSError:
                last_mtime = 0

            logger.info(f"Starting auto-update watcher with initial modification time: {last_mtime}")

            # Update the static file on startup
            call_command('update_static_translations')
            logger.info("Static translations file updated on startup")

            # Main loop
            interval = 60  # Check every 60 seconds
            while True:
                try:
                    # Check if the file has been modified
                    current_mtime = os.path.getmtime(log_file)

                    if current_mtime > last_mtime:
                        logger.info(f'Change detected at {timezone.now().strftime("%Y-%m-%d %H:%M:%S")}')
                        logger.info('Updating static translations file...')

                        # Update the static file
                        call_command('update_static_translations')

                        logger.info('Static translations file updated successfully')

                        # Update the last modification time
                        last_mtime = current_mtime

                    # Sleep for the specified interval
                    time.sleep(interval)

                except Exception as e:
                    logger.error(f'Error in auto-update watcher: {str(e)}')
                    time.sleep(interval)

        except Exception as e:
            logger.error(f"Error starting auto-update watcher: {str(e)}")
