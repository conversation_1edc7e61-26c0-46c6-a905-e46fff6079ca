"""
Management command to generate realistic metrics data for the dashboard.
This command creates BleuScoreHistory and TranslationMetrics records
with realistic values based on actual translations in the database.
"""

import random
import datetime
import logging
from django.core.management.base import BaseCommand
from django.utils import timezone
from translation_app.models import (
    Language,
    ComprehensiveTranslation,
    TranslationMetrics,
    BleuScoreHistory
)

class Command(BaseCommand):
    help = 'Generates realistic metrics data for the dashboard'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='Number of days of data to generate'
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing metrics data before generating new data'
        )

    def handle(self, *args, **options):
        days = options['days']
        clear = options['clear']
        
        self.stdout.write(f'Generating metrics data for the last {days} days')
        
        # Clear existing data if requested
        if clear:
            self.stdout.write('Clearing existing metrics data')
            TranslationMetrics.objects.all().delete()
            BleuScoreHistory.objects.all().delete()
        
        try:
            # Get language objects
            try:
                tagalog = Language.objects.get(code='tgl')
                teduray = Language.objects.get(code='ted')
            except Language.DoesNotExist:
                self.stdout.write(self.style.ERROR('Required languages not found in database'))
                return
            
            # Get actual translation counts to use as a basis
            tgl_to_ted_count = ComprehensiveTranslation.objects.filter(
                source_language=tagalog,
                target_language=teduray
            ).count()
            
            ted_to_tgl_count = ComprehensiveTranslation.objects.filter(
                source_language=teduray,
                target_language=tagalog
            ).count()
            
            # Use actual counts or defaults if none exist
            base_words_tgl_to_ted = max(10, tgl_to_ted_count // 10)
            base_words_ted_to_tgl = max(8, ted_to_tgl_count // 10)
            
            base_sentences_tgl_to_ted = max(5, tgl_to_ted_count // 20)
            base_sentences_ted_to_tgl = max(4, ted_to_tgl_count // 20)
            
            # Generate data for each day
            end_date = timezone.now().date()
            
            for i in range(days):
                current_date = end_date - datetime.timedelta(days=days-i-1)
                
                # Generate metrics for Tagalog to Teduray
                self.generate_daily_metrics(
                    current_date,
                    tagalog,
                    teduray,
                    base_words_tgl_to_ted,
                    base_sentences_tgl_to_ted,
                    i,
                    days
                )
                
                # Generate metrics for Teduray to Tagalog
                self.generate_daily_metrics(
                    current_date,
                    teduray,
                    tagalog,
                    base_words_ted_to_tgl,
                    base_sentences_ted_to_tgl,
                    i,
                    days
                )
            
            self.stdout.write(self.style.SUCCESS(f'Successfully generated metrics data for {days} days'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error generating metrics data: {str(e)}'))
            logging.error(f'Error in generate_metrics_data command: {str(e)}')
    
    def generate_daily_metrics(self, date, source_language, target_language, base_words, base_sentences, day_index, total_days):
        """Generate metrics for a specific day and language pair."""
        # Calculate growth factor (more recent days have more activity)
        growth_factor = 1.0 + (day_index / total_days)
        
        # Add some randomness to the daily counts
        words_random_factor = random.uniform(0.8, 1.2)
        sentences_random_factor = random.uniform(0.8, 1.2)
        
        # Calculate daily counts with growth and randomness
        words_translated = int(base_words * growth_factor * words_random_factor)
        sentences_translated = int(base_sentences * growth_factor * sentences_random_factor)
        
        # Calculate characters translated (roughly 5 chars per word on average)
        characters_translated = words_translated * random.randint(4, 6)
        
        # Calculate BLEU score with slight improvement over time
        base_bleu = 0.65
        improvement_factor = day_index / (total_days * 2)  # Gradual improvement
        random_factor = random.uniform(-0.03, 0.03)  # Daily fluctuation
        bleu_score = min(0.95, base_bleu + improvement_factor + random_factor)
        
        # Calculate confidence score (similar to BLEU but slightly higher)
        confidence = min(0.98, bleu_score + random.uniform(0.02, 0.05))
        
        # Create or update TranslationMetrics
        metrics, created = TranslationMetrics.objects.update_or_create(
            date=date,
            source_language=source_language,
            target_language=target_language,
            defaults={
                'words_translated': words_translated,
                'sentences_translated': sentences_translated,
                'characters_translated': characters_translated,
                'bleu_score': bleu_score,
                'average_confidence': confidence
            }
        )
        
        # Create or update BleuScoreHistory with n-gram precision
        bleu_history, created = BleuScoreHistory.objects.update_or_create(
            date=date,
            source_language=source_language,
            target_language=target_language,
            defaults={
                'bleu_score': bleu_score,
                'bleu_1gram': min(0.95, bleu_score + random.uniform(0.03, 0.08)),
                'bleu_2gram': min(0.90, bleu_score - random.uniform(0.02, 0.05)),
                'bleu_3gram': min(0.85, bleu_score - random.uniform(0.08, 0.12)),
                'bleu_4gram': min(0.80, bleu_score - random.uniform(0.15, 0.20)),
                'reference_count': random.randint(5, 15),
                'test_set_size': random.randint(20, 50)
            }
        )
        
        action = 'Created' if created else 'Updated'
        self.stdout.write(f'{action} metrics for {source_language.code} → {target_language.code} on {date}: {words_translated} words, BLEU {bleu_score:.2f}')
