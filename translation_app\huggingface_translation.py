"""
Hugging Face Translation Module for Translation System

This module provides free translation using Hugging Face models
that run locally without requiring API keys or having usage limits.
"""

import os
import logging
import json
from typing import Dict, List, Optional, Any
import re

# Set up logging
logger = logging.getLogger(__name__)

# Check if transformers is available
try:
    from transformers import MarianMTModel, MarianTokenizer, pipeline
    HUGGINGFACE_AVAILABLE = True
except ImportError:
    HUGGINGFACE_AVAILABLE = False
    logger.warning("Hugging Face transformers package not installed. Install with: pip install transformers sentencepiece")

class HuggingFaceTranslator:
    """
    Class for providing free translation using Hugging Face models.
    """

    def __init__(self):
        """Initialize the Hugging Face translator"""
        self.is_available = HUGGINGFACE_AVAILABLE
        self.models = {}
        self.tokenizers = {}
        self.pipelines = {}

        # Create models directory if it doesn't exist
        os.makedirs('models', exist_ok=True)

        # Initialize models if available
        if self.is_available:
            try:
                # We'll use English as an intermediate language
                self._load_model('tgl-en', 'Helsinki-NLP/opus-mt-tl-en')  # Tagalog to English
                self._load_model('en-tgl', 'Helsinki-NLP/opus-mt-en-tl')  # English to Tagalog

                # For Teduray, we'll use your existing translations
                # since there's likely no pre-trained model

                logger.info("Hugging Face translation models initialized")
            except Exception as e:
                logger.error(f"Error initializing Hugging Face models: {str(e)}")
                self.is_available = False

    def _load_model(self, pair_name, model_name):
        """Load a translation model"""
        try:
            # First check if we have a fine-tuned model
            finetuned_path = os.path.join('models', f"{pair_name}-finetuned")
            custom_path = os.path.join('models', f"{pair_name}-custom")
            local_path = os.path.join('models', pair_name)

            # Priority: 1. Fine-tuned model, 2. Custom model, 3. Local model, 4. Download from HF
            if os.path.exists(finetuned_path):
                # Load fine-tuned model
                self.tokenizers[pair_name] = MarianTokenizer.from_pretrained(finetuned_path)
                self.models[pair_name] = MarianMTModel.from_pretrained(finetuned_path)
                logger.info(f"Loaded fine-tuned model {pair_name} from {finetuned_path}")
            elif os.path.exists(custom_path) and os.path.exists(os.path.join(custom_path, 'translation_memory.json')):
                # Load custom model or translation memory
                # For now, we'll just load the base model and use the translation memory in translate()
                self.tokenizers[pair_name] = MarianTokenizer.from_pretrained(model_name)
                self.models[pair_name] = MarianMTModel.from_pretrained(model_name)

                # Load translation memory
                with open(os.path.join(custom_path, 'translation_memory.json'), 'r', encoding='utf-8') as f:
                    self.translation_memory = json.load(f)

                logger.info(f"Loaded custom translation memory for {pair_name} with {len(self.translation_memory)} examples")
            elif os.path.exists(local_path):
                # Load from local path
                self.tokenizers[pair_name] = MarianTokenizer.from_pretrained(local_path)
                self.models[pair_name] = MarianMTModel.from_pretrained(local_path)
                logger.info(f"Loaded model {pair_name} from local path")
            else:
                # Download from Hugging Face
                logger.info(f"Downloading model {model_name}...")
                self.tokenizers[pair_name] = MarianTokenizer.from_pretrained(model_name)
                self.models[pair_name] = MarianMTModel.from_pretrained(model_name)

                # Save locally for future use
                os.makedirs(local_path, exist_ok=True)
                self.tokenizers[pair_name].save_pretrained(local_path)
                self.models[pair_name].save_pretrained(local_path)
                logger.info(f"Downloaded and saved model {pair_name}")

            # Create a pipeline for easier use
            self.pipelines[pair_name] = pipeline(
                "translation",
                model=self.models[pair_name],
                tokenizer=self.tokenizers[pair_name]
            )

        except Exception as e:
            logger.error(f"Error loading model {model_name}: {str(e)}")
            raise

    def translate(self, text: str, source_lang: str, target_lang: str,
                 context_examples: Optional[List[Dict[str, str]]] = None) -> Dict[str, Any]:
        """
        Translate text using Hugging Face models.

        Args:
            text: Text to translate
            source_lang: Source language code
            target_lang: Target language code
            context_examples: Optional list of example translations for context

        Returns:
            dict: Translation data including the translated text and metadata
        """
        if not self.is_available:
            return {
                'translation': text,
                'confidence': 0.0,
                'source': 'huggingface_unavailable',
                'error': 'Hugging Face translation is not available'
            }

        try:
            # First check if we have a direct translation path
            pair_key = f"{source_lang}-{target_lang}"

            if pair_key in self.pipelines:
                # Direct translation
                return self._translate_with_pipeline(text, pair_key)

            # If we have context examples, try to use them for translation
            if context_examples and len(context_examples) > 0:
                translation = self._translate_with_examples(text, source_lang, target_lang, context_examples)
                if translation:
                    return {
                        'translation': translation,
                        'confidence': 0.75,
                        'source': 'example_based',
                        'cultural_notes': 'Translation based on similar examples in the database.'
                    }

            # If no direct path, try to use English as an intermediate language
            if f"{source_lang}-en" in self.pipelines and f"en-{target_lang}" in self.pipelines:
                # Two-step translation through English
                english_result = self._translate_with_pipeline(text, f"{source_lang}-en")
                if english_result['confidence'] > 0:
                    target_result = self._translate_with_pipeline(
                        english_result['translation'], f"en-{target_lang}"
                    )

                    # Combine confidence scores
                    combined_confidence = english_result['confidence'] * target_result['confidence']

                    return {
                        'translation': target_result['translation'],
                        'confidence': combined_confidence,
                        'source': 'two_step',
                        'intermediate': english_result['translation'],
                        'cultural_notes': 'Translation performed through English as an intermediate language.'
                    }

            # If we can't translate with models, try pattern matching with examples
            if context_examples and len(context_examples) > 0:
                best_match, similarity = self._find_best_match(text, context_examples)
                if best_match and similarity > 0.6:
                    return {
                        'translation': best_match['target'],
                        'confidence': similarity,
                        'source': 'pattern_matching',
                        'match_source': best_match['source'],
                        'cultural_notes': 'Translation based on pattern matching with similar phrases.'
                    }

            # If all else fails, return the original text
            return {
                'translation': text,
                'confidence': 0.0,
                'source': 'no_translation_path',
                'error': 'No suitable translation path found'
            }

        except Exception as e:
            logger.error(f"Error in Hugging Face translation: {str(e)}")
            return {
                'translation': text,
                'confidence': 0.0,
                'source': 'huggingface_error',
                'error': str(e)
            }

    def _translate_with_pipeline(self, text: str, pair_key: str) -> Dict[str, Any]:
        """Translate text using a specific pipeline"""
        try:
            pipeline = self.pipelines[pair_key]

            # Translate
            result = pipeline(text, max_length=512)
            translation = result[0]['translation_text']

            return {
                'translation': translation,
                'confidence': 0.8,  # Base confidence for model translations
                'source': f'huggingface_{pair_key}'
            }
        except Exception as e:
            logger.error(f"Error translating with pipeline {pair_key}: {str(e)}")
            return {
                'translation': text,
                'confidence': 0.0,
                'error': str(e)
            }

    def _translate_with_examples(self, text: str, source_lang: str, target_lang: str,
                               context_examples: List[Dict[str, str]]) -> Optional[str]:
        """Translate text using example translations"""
        # This is a simple implementation that could be improved
        words = text.split()
        translated_words = []

        # Create a dictionary of word translations from examples
        word_translations = {}
        for example in context_examples:
            source = example.get('source', '')
            target = example.get('target', '')

            if source and target:
                source_words = source.split()
                target_words = target.split()

                # If word counts are similar, assume word-to-word mapping
                if 0.7 <= len(target_words) / len(source_words) <= 1.3:
                    for i, sword in enumerate(source_words):
                        if i < len(target_words):
                            word_translations[sword.lower()] = target_words[i]

        # Translate each word if possible
        for word in words:
            if word.lower() in word_translations:
                translated_words.append(word_translations[word.lower()])
            else:
                translated_words.append(word)  # Keep original if no translation

        # If we didn't translate anything, return None
        if words == translated_words:
            return None

        return ' '.join(translated_words)

    def _find_best_match(self, text: str, examples: List[Dict[str, str]]) -> tuple:
        """Find the best matching example for the text"""
        best_match = None
        best_similarity = 0

        for example in examples:
            source = example.get('source', '')
            similarity = self._calculate_similarity(text, source)

            if similarity > best_similarity:
                best_similarity = similarity
                best_match = example

        return best_match, best_similarity

    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two texts (0.0 to 1.0)"""
        # Simple word overlap similarity
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union)

# Initialize the Hugging Face translator
huggingface_translator = HuggingFaceTranslator()
