"""
New Attention Mechanism Module for Translation System

This module provides a simplified implementation of the attention mechanism
for translating sentences between Tagalog and Teduray.
"""

import logging
import re
from typing import Dict, Any, List

from django.core.cache import cache
from translation_app.models import ComprehensiveTranslation, Language

logger = logging.getLogger(__name__)

class NewAttentionMechanism:
    """
    Simplified implementation of attention mechanism for sentence translation.
    """
    
    def __init__(self):
        """Initialize the attention mechanism"""
        logger.info("Initializing new attention mechanism")
    
    def process_translation(self, source_text: str, result: Dict[str, Any], 
                           source_lang: str, target_lang: str) -> Dict[str, Any]:
        """
        Process a translation request using the attention mechanism.
        This method is called from services.py.
        
        Args:
            source_text: Source text
            result: Translation result data
            source_lang: Source language code
            target_lang: Target language code
            
        Returns:
            dict: Improved translation data
        """
        logger.info(f"Processing translation for: '{source_text}'")
        
        # Check if this is a sentence
        is_sentence = len(source_text.split()) > 1
        is_likely_sentence = any(p in source_text for p in '.?!,:;') or source_text.strip().endswith('?')
        
        # For sentences, generate a new translation
        if is_sentence or is_likely_sentence:
            logger.info(f"Generating sentence translation for: '{source_text}'")
            return self.generate_sentence_translation(source_text, result, source_lang, target_lang)
        else:
            # For single words, return the original result
            logger.info(f"Using original translation for word: '{source_text}'")
            return result
    
    def generate_sentence_translation(self, source_text: str, translation_data: Dict[str, Any],
                                    source_lang_code: str, target_lang_code: str) -> Dict[str, Any]:
        """
        Generate a complete sentence translation.
        This method is specifically designed for translating sentences.
        
        Args:
            source_text: Source text
            translation_data: Translation data from the translation service
            source_lang_code: Source language code
            target_lang_code: Target language code
            
        Returns:
            dict: Improved translation data
        """
        logger.info(f"Generating sentence translation for: '{source_text}'")
        
        # Clean the source text
        source_clean = re.sub(r'[^\w\s]', ' ', source_text.lower())
        source_tokens = source_clean.split()
        
        # Get language objects
        try:
            source_lang = Language.objects.get(code=source_lang_code)
            target_lang = Language.objects.get(code=target_lang_code)
        except Exception as e:
            logger.error(f"Error getting language objects: {str(e)}")
            return translation_data
        
        # Generate a new translation using word-by-word approach
        word_translations = []
        covered_indices = set()
        
        # First try to find translations for multi-word phrases
        for n in range(min(5, len(source_tokens)), 1, -1):
            for i in range(len(source_tokens) - n + 1):
                # Skip if any of these positions are already covered
                if any(j in covered_indices for j in range(i, i+n)):
                    continue
                
                # Extract n-gram
                ngram = ' '.join(source_tokens[i:i+n])
                
                # Look for matches in the database
                try:
                    phrase_match = ComprehensiveTranslation.objects.filter(
                        base_word__iexact=ngram,
                        source_language=source_lang,
                        target_language=target_lang
                    ).order_by('-confidence_score').first()
                    
                    if phrase_match:
                        # Add this phrase translation
                        word_translations.append({
                            'original': ngram,
                            'translation': phrase_match.translation,
                            'confidence': 0.9,
                            'position': i,
                            'length': n
                        })
                        # Mark these positions as covered
                        covered_indices.update(range(i, i+n))
                except Exception as e:
                    logger.error(f"Error finding phrase match: {str(e)}")
        
        # Now get individual word translations for any words not covered by phrases
        for i, word in enumerate(source_tokens):
            if i in covered_indices:
                continue  # Skip words that are part of phrases
            
            try:
                word_trans = ComprehensiveTranslation.objects.filter(
                    base_word__iexact=word,
                    source_language=source_lang,
                    target_language=target_lang
                ).order_by('-confidence_score').first()
                
                if word_trans:
                    word_translations.append({
                        'original': word,
                        'translation': word_trans.translation,
                        'confidence': 0.85,
                        'position': i,
                        'length': 1
                    })
                else:
                    # If no translation found, keep the original word
                    word_translations.append({
                        'original': word,
                        'translation': word,
                        'confidence': 0.5,
                        'position': i,
                        'length': 1
                    })
            except Exception as e:
                logger.error(f"Error finding word translation: {str(e)}")
                # If error, keep the original word
                word_translations.append({
                    'original': word,
                    'translation': word,
                    'confidence': 0.5,
                    'position': i,
                    'length': 1
                })
        
        # Sort translations by position
        word_translations.sort(key=lambda x: x.get('position', 0))
        
        # Construct the translation
        constructed_translation = ' '.join([wt['translation'] for wt in word_translations])
        
        # Calculate average confidence
        avg_confidence = sum(wt['confidence'] for wt in word_translations) / len(word_translations) if word_translations else 0.75
        
        # Create a simplified word_by_word without position information for API response
        simplified_word_by_word = []
        for wt in word_translations:
            simplified_word_by_word.append({
                'original': wt['original'],
                'translation': wt['translation'],
                'confidence': wt['confidence'],
                'source': 'attention_generated'
            })
        
        # Create the result
        source_word_count = len(source_tokens)
        target_word_count = len(constructed_translation.split())
        ratio = target_word_count / source_word_count if source_word_count > 0 else 0
        
        result = {
            'original': source_text,
            'translation': constructed_translation,
            'confidence': avg_confidence,
            'source': 'attention_generated',
            'notes': f'Generated using attention mechanism. Word ratio: {ratio:.2f} ({source_word_count} → {target_word_count})',
            'word_by_word': simplified_word_by_word
        }
        
        logger.info(f"Generated translation: {source_text} → {constructed_translation}")
        return result

# Create a singleton instance
new_attention_mechanism = NewAttentionMechanism()
