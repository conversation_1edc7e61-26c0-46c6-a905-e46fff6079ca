// Direct JavaScript overrides for admin interface

// Function to force light theme
function forceLightTheme() {
    // Remove any theme-related attributes
    document.documentElement.removeAttribute('data-theme');
    document.documentElement.removeAttribute('class');
    
    // Remove any theme toggle elements
    const themeToggles = document.querySelectorAll('.theme-toggle, [data-theme], [class*="theme-toggle"]');
    themeToggles.forEach(function(element) {
        element.style.display = 'none';
    });
    
    // Remove any skip links
    const skipLinks = document.querySelectorAll('.skip-to-content-link, a[href="#content-start"]');
    skipLinks.forEach(function(element) {
        element.style.display = 'none';
    });
    
    console.log('Light theme forced');
}

// Apply immediately
forceLightTheme();

// Apply when DOM is loaded
document.addEventListener('DOMContentLoaded', forceLightTheme);

// Apply periodically to ensure it takes effect
setInterval(forceLightTheme, 500);
