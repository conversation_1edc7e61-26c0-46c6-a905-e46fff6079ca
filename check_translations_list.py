"""
Script to check translations from a list.
This script checks if translations in the list match what's in the database.
"""

import os
import sys
import django
import logging

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Import Django models and services
from translation_app.services import get_translation_service
from translation_app.models import Language, ComprehensiveTranslation, TranslationVersion
from django.core.cache import cache

# List of translations to check (source, target)
TRANSLATIONS_TO_CHECK = [
    ("Umuwi ka", "Mënule go"),
    ("Um<PERSON>", "Mëntëkëdan"),
    ("<PERSON>an na siya", "Nayo nuwe de"),
    ("<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"),
    ("<PERSON><PERSON><PERSON><PERSON>", "Fëgitonën"),
    ("natalo sila", "tënabanan ro"),
    ("Sinubukan", "Kënalu-kalu"),
    ("nagtatrabaho", "Gëmalbëk"),
    ("Kunin", "sekung"),
    ("kumain", "mënama"),
    ("Magandang hapon", "Fiyo tëmëgën"),
    ("natalo", "Tënabanan"),
    ("sila", "bero"),
    ("Lalaki", "Lagëy"),
    ("Magandang lalaki", "Fiyo lagëy"),
    ("gumising", "Mëntek"),
    ("Lumabas", "Mënsut"),
    ("Kunin", "Dotën"),
    ("Itigil", "Ugatën"),
    ("subukan", "Kalu-kaluën"),
    ("Magandang araw", "Fiyo tërësang"),
    ("Umuwi", "Mënule"),
    ("Sumayaw kami", "Mënbaele gey."),
    ("Nahulog siya", "Mënlawu"),
    ("natalo kami", "Tënabanan gey."),
    ("Magandang umaga", "Fiyo gëfuwën"),
    ("bakit hindi siya pumunta", "sede kë ënda mënagëwën"),
    ("bakit", "sede"),
    ("sumayaw", "Mënbaele"),
    ("Nahulog", "Mënlawu"),
    ("ka", "go"),
    ("Tumakbo si Tom", "Lëmënëntuh i Tome."),
    ("Ngumiti siya sa akin", "Gëmënëmi dëb begëne"),
    ("Kami", "begey"),
    ("Maghintay", "Ongot-ongot"),
    ("Magandang gabi", "Fiyo këlungonon"),
    ("Nasaan si Tom", "nayo Tome de"),
    ("Magandang babae", "Fiyo libun"),
    ("ako", "Begën"),
    ("Babae", "Libun"),
    ("bakit", "Sede"),
    ("Tulungan", "Tobongan"),
    ("asawa ko ay masaya", "bawag =ku, =gu i ënda"),
    ("asawa", "bawag"),
    ("malungkot", "méruwe"),
    ("Ipakita mo sa akin", "Fëgito mo dini."),
    ("natalo kami", "Tënabanan gey."),
    ("Pumayag siya", "Mënagayun de"),
    ("tanungin mo sila", "Fënginsaëm bero."),
    ("kumusta", "fiyo")
]

def check_translations():
    """Check translations from the list."""
    try:
        # Get language objects
        tgl = Language.objects.get(code='tgl')
        ted = Language.objects.get(code='ted')
        
        # Get the translation service
        translation_service = get_translation_service()
        
        # Check each translation
        missing_translations = []
        incorrect_translations = []
        
        for source, expected_target in TRANSLATIONS_TO_CHECK:
            # Check if the translation exists in the database
            existing_translation = ComprehensiveTranslation.objects.filter(
                base_word__iexact=source,
                source_language=tgl,
                target_language=ted
            ).first()
            
            if existing_translation:
                # Check if the translation is correct
                if existing_translation.translation.lower() != expected_target.lower():
                    logger.warning(f"Incorrect translation in database: {source} → {existing_translation.translation} (expected: {expected_target})")
                    incorrect_translations.append((source, existing_translation.translation, expected_target))
            else:
                logger.warning(f"Missing translation in database: {source} → {expected_target}")
                missing_translations.append((source, expected_target))
            
            # Test the translation service
            actual_translation = translation_service.translate(source, tgl.code, ted.code)
            if actual_translation.lower() != expected_target.lower():
                logger.warning(f"Incorrect translation from service: {source} → {actual_translation} (expected: {expected_target})")
        
        # Print summary
        logger.info(f"Found {len(missing_translations)} missing translations and {len(incorrect_translations)} incorrect translations")
        
        if missing_translations:
            logger.info("Missing translations:")
            for source, target in missing_translations:
                logger.info(f"  - {source} → {target}")
        
        if incorrect_translations:
            logger.info("Incorrect translations:")
            for source, actual, expected in incorrect_translations:
                logger.info(f"  - {source} → {actual} (expected: {expected})")
        
        return missing_translations, incorrect_translations
    except Exception as e:
        logger.error(f"Error checking translations: {str(e)}")
        return [], []

def fix_translations(missing_translations, incorrect_translations):
    """Fix missing and incorrect translations."""
    try:
        # Get language objects
        tgl = Language.objects.get(code='tgl')
        ted = Language.objects.get(code='ted')
        
        # Fix missing translations
        for source, target in missing_translations:
            logger.info(f"Adding missing translation: {source} → {target}")
            
            # Create new translation
            new_translation = ComprehensiveTranslation.objects.create(
                base_word=source,
                translation=target,
                source_language=tgl,
                target_language=ted,
                part_of_speech='phrase' if ' ' in source else 'word',
                notes='Added manually to fix missing translation'
            )
            
            # Create a version
            TranslationVersion.objects.create(
                comprehensive_translation=new_translation,
                translation=target,
                created_by='system',
                confidence_score=1.0,
                is_active=True,
                notes='Initial version'
            )
        
        # Fix incorrect translations
        for source, actual, expected in incorrect_translations:
            logger.info(f"Fixing incorrect translation: {source} → {actual} to {expected}")
            
            # Get the translation
            existing_translation = ComprehensiveTranslation.objects.get(
                base_word__iexact=source,
                source_language=tgl,
                target_language=ted
            )
            
            # Create a new version with the old translation (inactive)
            TranslationVersion.objects.create(
                comprehensive_translation=existing_translation,
                translation=existing_translation.translation,
                created_by='system',
                confidence_score=0.8,
                is_active=False,
                notes='Archived before correction'
            )
            
            # Update the translation
            existing_translation.translation = expected
            existing_translation.save()
            
            # Create a new active version
            TranslationVersion.objects.create(
                comprehensive_translation=existing_translation,
                translation=expected,
                created_by='system',
                confidence_score=1.0,
                is_active=True,
                notes='Corrected translation'
            )
        
        # Clear cache
        cache.clear()
        logger.info("Cache cleared")
        
        # Force reload translations in the translation service
        translation_service = get_translation_service()
        translation_service.reload_translations()
        logger.info("Translations reloaded")
        
        return True
    except Exception as e:
        logger.error(f"Error fixing translations: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("Starting translation check script...")
    
    # Check translations
    missing, incorrect = check_translations()
    
    # Ask if user wants to fix translations
    if missing or incorrect:
        user_input = input("Do you want to fix these translations? (y/n): ")
        if user_input.lower() == 'y':
            if fix_translations(missing, incorrect):
                logger.info("Translations fixed successfully!")
            else:
                logger.error("Failed to fix translations.")
    else:
        logger.info("All translations are correct!")
