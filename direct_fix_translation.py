"""
<PERSON><PERSON><PERSON> to directly fix a specific translation in the database and static file.
This script bypasses the normal optimization process and directly updates both
the database and the static file with the correct translation.
"""

import os
import sys
import json
import logging
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Import Django models
from django.db import transaction
from translation_app.models import Language, ComprehensiveTranslation, TranslationVersion
from django.utils import timezone

def is_pythonanywhere():
    """
    Detect if the app is running on PythonAnywhere.
    
    Returns:
        bool: True if running on PythonAnywhere, False otherwise
    """
    # Check for PythonAnywhere-specific environment variables or paths
    if 'PYTHONANYWHERE_DOMAIN' in os.environ:
        return True
    
    # Check for PythonAnywhere in the hostname
    try:
        import socket
        if 'pythonanywhere' in socket.gethostname().lower():
            return True
    except:
        pass
    
    # Check for PythonAnywhere in the sys.path
    for path in sys.path:
        if 'pythonanywhere' in path.lower():
            return True
    
    return False

def get_static_file_path():
    """
    Get the static file path based on the environment.
    
    Returns:
        str: Path to the static file
    """
    if is_pythonanywhere():
        # PythonAnywhere paths
        static_root = '/home/<USER>/teduray/staticfiles'
        static_file_path = os.path.join(static_root, 'translation_app', 'js', 'all_translations.js')
        logger.info(f"PythonAnywhere detected. Using path: {static_file_path}")
    else:
        # Local paths
        static_file_path = os.path.join('static', 'translation_app', 'js', 'all_translations.js')
        logger.info(f"Local environment detected. Using path: {static_file_path}")
    
    return static_file_path

def update_database_translation():
    """
    Update the translation in the database.
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get language objects
        tgl = Language.objects.get(code='tgl')
        ted = Language.objects.get(code='ted')
        
        # Define the translation details
        source_text = "umuwi ka na"
        translation_text = "Mënule go"
        
        # Try to find existing translation
        with transaction.atomic():
            # Look for the exact match first
            translation = ComprehensiveTranslation.objects.filter(
                base_word__iexact=source_text,
                source_language=tgl,
                target_language=ted
            ).first()
            
            # Also look for the version with period
            if not translation:
                translation = ComprehensiveTranslation.objects.filter(
                    base_word__iexact=source_text + ".",
                    source_language=tgl,
                    target_language=ted
                ).first()
            
            if translation:
                # Update existing translation
                logger.info(f"Found existing translation: {translation.base_word} → {translation.translation}")
                
                # Create a new version
                version = TranslationVersion.objects.create(
                    comprehensive_translation=translation,
                    translation=translation_text,
                    confidence_score=0.95,
                    is_active=True
                )
                
                # Update the main translation
                translation.translation = translation_text
                translation.updated_at = timezone.now()
                translation.save()
                
                logger.info(f"Updated translation: {translation.base_word} → {translation_text}")
                logger.info(f"Created version: {version.id}")
            else:
                # Create new translation
                translation = ComprehensiveTranslation.objects.create(
                    base_word=source_text,
                    translation=translation_text,
                    source_language=tgl,
                    target_language=ted,
                    part_of_speech='',
                    notes='Created via direct fix script'
                )
                
                logger.info(f"Created new translation: {source_text} → {translation_text}")
                
                # Also create with period if needed
                period_translation = ComprehensiveTranslation.objects.filter(
                    base_word__iexact=source_text + ".",
                    source_language=tgl,
                    target_language=ted
                ).first()
                
                if not period_translation:
                    period_translation = ComprehensiveTranslation.objects.create(
                        base_word=source_text + ".",
                        translation=translation_text,
                        source_language=tgl,
                        target_language=ted,
                        part_of_speech='',
                        notes='Created via direct fix script (with period)'
                    )
                    
                    logger.info(f"Also created with period: {source_text}. → {translation_text}")
        
        return True
    except Exception as e:
        logger.error(f"Error updating database translation: {str(e)}")
        return False

def update_static_file_translation():
    """
    Update the translation in the static file.
    
    Returns:
        bool: True if successful, False otherwise
    """
    static_file_path = get_static_file_path()
    
    # Check if the file exists
    if not os.path.exists(static_file_path):
        logger.error(f"Static file not found: {static_file_path}")
        return False
    
    try:
        # Read the current content
        with open(static_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the start and end of the JSON object
        start_index = content.find('const ALL_TRANSLATIONS = ')
        if start_index == -1:
            logger.error("Could not find ALL_TRANSLATIONS variable in static file")
            return False
        
        # Extract the JSON part
        json_start = content.find('{', start_index)
        json_end = content.find(';\n\nconst WORD_BY_WORD_MAPPINGS')
        if json_start == -1 or json_end == -1 or json_end <= json_start:
            # Try alternative end pattern
            json_end = content.find(';\n')
            if json_end == -1 or json_end <= json_start:
                logger.error("Could not extract JSON from static file")
                return False
        
        json_str = content[json_start:json_end]
        
        try:
            # Parse the JSON
            translations_dict = json.loads(json_str)
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON from static file: {str(e)}")
            return False
        
        # Update the specific translation
        source_text = "umuwi ka na"
        translation_text = "Mënule go"
        direction = 'tgl_to_ted'
        
        # Check if the direction exists
        if direction not in translations_dict:
            translations_dict[direction] = {}
        
        # Update or add the translation
        translations_dict[direction][source_text.lower()] = {
            'translation': translation_text,
            'confidence': 0.95,
            'source': 'database',
            'part_of_speech': '',
            'notes': 'Updated via direct fix script'
        }
        
        # Also update with period
        translations_dict[direction][source_text.lower() + "."] = {
            'translation': translation_text,
            'confidence': 0.95,
            'source': 'database',
            'part_of_speech': '',
            'notes': 'Updated via direct fix script (with period)'
        }
        
        # Create the updated content
        updated_json = json.dumps(translations_dict, ensure_ascii=False, indent=2)
        updated_content = content[:json_start] + updated_json + content[json_end:]
        
        # Write the updated content back to the file
        with open(static_file_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        logger.info(f"Successfully updated static file with translation: {source_text} → {translation_text}")
        
        return True
    except Exception as e:
        logger.error(f"Error updating static file translation: {str(e)}")
        return False

def reload_translations():
    """
    Reload translations into memory.
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Import the translation service
        from django.core.cache import cache
        from translation_app.services import get_translation_service
        
        # Clear the cache
        cache.clear()
        logger.info("Cache cleared")
        
        # Get the translation service and force a reload
        translation_service = get_translation_service(skip_loading_for_admin=False)
        translation_service.reload_translations()
        
        logger.info("Translations reloaded successfully")
        return True
    except Exception as e:
        logger.error(f"Error reloading translations: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("Starting direct fix translation script...")
    
    # Update the database
    db_success = update_database_translation()
    if db_success:
        logger.info("Database update completed successfully!")
    else:
        logger.error("Failed to update database.")
    
    # Update the static file
    static_success = update_static_file_translation()
    if static_success:
        logger.info("Static file update completed successfully!")
    else:
        logger.error("Failed to update static file.")
    
    # Reload translations
    reload_success = reload_translations()
    if reload_success:
        logger.info("Translation reload completed successfully!")
    else:
        logger.error("Failed to reload translations.")
    
    # Overall result
    if db_success and static_success and reload_success:
        logger.info("All updates completed successfully!")
    else:
        logger.error("Some updates failed. Check the logs for details.")
