"""
<PERSON><PERSON><PERSON> to update a specific translation in the static file.
This script directly updates the static file with a specific translation.
"""

import os
import json
import logging
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def is_pythonanywhere():
    """
    Detect if the app is running on PythonAnywhere.
    
    Returns:
        bool: True if running on PythonAnywhere, False otherwise
    """
    # Check for PythonAnywhere-specific environment variables or paths
    if 'PYTHONANYWHERE_DOMAIN' in os.environ:
        return True
    
    # Check for PythonAnywhere in the hostname
    try:
        import socket
        if 'pythonanywhere' in socket.gethostname().lower():
            return True
    except:
        pass
    
    # Check for PythonAnywhere in the sys.path
    for path in sys.path:
        if 'pythonanywhere' in path.lower():
            return True
    
    return False

def get_static_file_path():
    """
    Get the static file path based on the environment.
    
    Returns:
        str: Path to the static file
    """
    if is_pythonanywhere():
        # PythonAnywhere paths
        static_root = '/home/<USER>/teduray/staticfiles'
        static_file_path = os.path.join(static_root, 'translation_app', 'js', 'all_translations.js')
        logger.info(f"PythonAnywhere detected. Using path: {static_file_path}")
    else:
        # Local paths
        static_file_path = os.path.join('static', 'translation_app', 'js', 'all_translations.js')
        logger.info(f"Local environment detected. Using path: {static_file_path}")
    
    return static_file_path

def update_specific_translation():
    """
    Update a specific translation in the static file.
    
    Returns:
        bool: True if successful, False otherwise
    """
    static_file_path = get_static_file_path()
    
    # Check if the file exists
    if not os.path.exists(static_file_path):
        logger.error(f"Static file not found: {static_file_path}")
        return False
    
    try:
        # Read the current content
        with open(static_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the start and end of the JSON object
        start_index = content.find('const ALL_TRANSLATIONS = ')
        if start_index == -1:
            logger.error("Could not find ALL_TRANSLATIONS variable in static file")
            return False
        
        # Extract the JSON part
        json_start = content.find('{', start_index)
        json_end = content.find(';\n\nconst WORD_BY_WORD_MAPPINGS')
        if json_start == -1 or json_end == -1 or json_end <= json_start:
            logger.error("Could not extract JSON from static file")
            return False
        
        json_str = content[json_start:json_end]
        
        try:
            # Parse the JSON
            translations_dict = json.loads(json_str)
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON from static file: {str(e)}")
            return False
        
        # Update the specific translation
        source_text = "umuwi ka na"
        translation_text = "Mënule go"
        source_lang = "tgl"
        target_lang = "ted"
        
        # Determine the translation direction
        if source_lang == 'tgl' and target_lang == 'ted':
            direction = 'tgl_to_ted'
        elif source_lang == 'ted' and target_lang == 'tgl':
            direction = 'ted_to_tgl'
        else:
            logger.warning(f"Unsupported language pair: {source_lang} to {target_lang}")
            return False
        
        # Check if the direction exists
        if direction not in translations_dict:
            translations_dict[direction] = {}
        
        # Normalize the source text
        source_text_lower = source_text.lower()
        
        # Update or add the translation
        translations_dict[direction][source_text_lower] = {
            'translation': translation_text,
            'confidence': 0.95,
            'source': 'database',
            'part_of_speech': '',
            'notes': 'Updated via direct script'
        }
        
        logger.info(f"Updated translation: {source_text} → {translation_text}")
        
        # Also update with period if needed
        if source_text_lower + "." not in translations_dict[direction]:
            translations_dict[direction][source_text_lower + "."] = {
                'translation': translation_text,
                'confidence': 0.95,
                'source': 'database',
                'part_of_speech': '',
                'notes': 'Updated via direct script (with period)'
            }
            logger.info(f"Also added with period: {source_text}. → {translation_text}")
        
        # Create the updated content
        updated_json = json.dumps(translations_dict, ensure_ascii=False, indent=2)
        updated_content = content[:json_start] + updated_json + content[json_end:]
        
        # Write the updated content back to the file
        with open(static_file_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        logger.info(f"Successfully updated static file with translation: {source_text} → {translation_text}")
        
        # Also update the word-by-word mappings
        # Find the start and end of the WORD_BY_WORD_MAPPINGS JSON object
        start_index = updated_content.find('const WORD_BY_WORD_MAPPINGS = ')
        if start_index == -1:
            logger.warning("Could not find WORD_BY_WORD_MAPPINGS variable in static file")
            return True  # Still return True as we updated the main translations
        
        # Extract the JSON part
        json_start = updated_content.find('{', start_index)
        json_end = updated_content.rfind(';\n')
        if json_start == -1 or json_end == -1 or json_end <= json_start:
            logger.warning("Could not extract WORD_BY_WORD_MAPPINGS JSON from static file")
            return True  # Still return True as we updated the main translations
        
        json_str = updated_content[json_start:json_end]
        
        try:
            # Parse the JSON
            word_mappings = json.loads(json_str)
            
            # Update the word mapping
            if direction in word_mappings:
                word_mappings[direction][source_text_lower] = {
                    'translation': translation_text,
                    'confidence': 0.95
                }
                
                # Create the updated content
                updated_json = json.dumps(word_mappings, ensure_ascii=False, indent=2)
                final_content = updated_content[:json_start] + updated_json + updated_content[json_end:]
                
                # Write the updated content back to the file
                with open(static_file_path, 'w', encoding='utf-8') as f:
                    f.write(final_content)
                
                logger.info(f"Also updated word-by-word mapping: {source_text} → {translation_text}")
            
        except json.JSONDecodeError as e:
            logger.warning(f"Error parsing WORD_BY_WORD_MAPPINGS JSON from static file: {str(e)}")
            return True  # Still return True as we updated the main translations
        
        return True
    except Exception as e:
        logger.error(f"Error updating specific translation: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("Starting specific translation update script...")
    
    if update_specific_translation():
        logger.info("Translation update completed successfully!")
    else:
        logger.error("Failed to update translation.")
