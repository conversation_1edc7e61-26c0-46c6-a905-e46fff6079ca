"""
Django management command to export translations to a static JavaScript file.
This allows the translations to be used on the client side without making API calls.
"""

import os
import json
import logging
from django.core.management.base import BaseCommand
from django.conf import settings
from translation_app.models import ComprehensiveTranslation, Language

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Export translations to a static JavaScript file'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reduced',
            action='store_true',
            help='Create a reduced version with only essential translations'
        )
        parser.add_argument(
            '--max-size',
            type=int,
            default=5 * 1024 * 1024,  # 5MB default
            help='Maximum file size in bytes'
        )
        parser.add_argument(
            '--output-dir',
            type=str,
            help='Output directory for the static file'
        )
        parser.add_argument(
            '--min-confidence',
            type=float,
            default=0.7,
            help='Minimum confidence score for translations to be included'
        )

    def handle(self, *args, **options):
        reduced = options.get('reduced', False)
        max_size = options.get('max_size', 5 * 1024 * 1024)  # 5MB default
        output_dir = options.get('output_dir')
        min_confidence = options.get('min_confidence', 0.7)

        # Detect PythonAnywhere environment
        is_pythonanywhere = 'PYTHONANYWHERE_DOMAIN' in os.environ
        if is_pythonanywhere:
            self.stdout.write("PythonAnywhere detected. Using configured paths:")
            self.stdout.write(f"STATIC_ROOT: {settings.STATIC_ROOT}")
            self.stdout.write(f"STATICFILES_DIRS: {settings.STATICFILES_DIRS}")

        # Determine output directory
        if output_dir:
            static_dir = output_dir
        elif hasattr(settings, 'STATIC_ROOT') and settings.STATIC_ROOT:
            # Use STATIC_ROOT if available
            static_dir = os.path.join(settings.STATIC_ROOT, 'translation_app', 'js')
        elif hasattr(settings, 'STATICFILES_DIRS') and settings.STATICFILES_DIRS:
            # Use the first STATICFILES_DIRS entry if available
            static_dir = os.path.join(settings.STATICFILES_DIRS[0], 'translation_app', 'js')
        else:
            # Fallback to a local static directory
            static_dir = os.path.join('static', 'translation_app', 'js')

        # Create the directory if it doesn't exist
        os.makedirs(static_dir, exist_ok=True)

        # Get all languages
        languages = Language.objects.all()

        # Create a dictionary to store all translations
        all_translations = {}

        # Initialize counters
        total_translations = 0
        included_translations = 0

        # Process each language pair
        for source_lang in languages:
            for target_lang in languages:
                if source_lang.id == target_lang.id:
                    continue  # Skip same language pairs

                # Create a key for this language pair
                direction_key = f"{source_lang.code}_to_{target_lang.code}"
                all_translations[direction_key] = {}

                # Get translations for this language pair
                query = ComprehensiveTranslation.objects.filter(
                    source_language=source_lang,
                    target_language=target_lang
                )

                # Apply confidence filter - skip this step as confidence_score is not a field in the model
                # query = query.filter(confidence_score__gte=min_confidence)

                # If reduced mode, limit to essential translations
                if reduced:
                    # Get only the most recently updated words (limit to 1000)
                    query = query.order_by('-updated_at')[:1000]

                # Process translations
                for translation in query:
                    total_translations += 1

                    # Get the active version if available
                    active_version = translation.versions.filter(is_active=True).first()

                    # Use the version's translation if available, otherwise use the base translation
                    translation_text = active_version.translation if active_version else translation.translation

                    # Set a default confidence score since it's not a field in the model
                    if active_version and hasattr(active_version, 'confidence_score'):
                        confidence_score = active_version.confidence_score
                    else:
                        # Use a default high confidence score for translations in the database
                        confidence_score = 0.95

                    # Skip empty translations
                    if not translation_text or translation_text.strip() == '':
                        continue

                    # Add to the translations dictionary
                    base_word_lower = translation.base_word.lower()
                    all_translations[direction_key][base_word_lower] = {
                        'translation': translation_text,
                        'confidence': confidence_score,
                        'part_of_speech': translation.part_of_speech,
                        'notes': translation.notes
                    }
                    included_translations += 1

        # Convert to JavaScript
        js_content = f"const ALL_TRANSLATIONS = {json.dumps(all_translations, ensure_ascii=False, indent=2)};"

        # Check file size
        if len(js_content.encode('utf-8')) > max_size:
            self.stdout.write(self.style.WARNING(
                f"Warning: Generated file exceeds maximum size ({len(js_content.encode('utf-8'))} > {max_size} bytes). "
                f"Consider using --reduced flag or increasing --max-size."
            ))

            # If in reduced mode, further reduce by removing notes and part_of_speech
            if reduced:
                self.stdout.write("Applying additional size reduction by removing notes and part_of_speech fields...")

                # Simplify the translations
                for direction in all_translations:
                    for word in all_translations[direction]:
                        # Convert to simple string if confidence is high
                        if all_translations[direction][word]['confidence'] > 0.8:
                            all_translations[direction][word] = all_translations[direction][word]['translation']
                        else:
                            # Keep only essential fields
                            all_translations[direction][word] = {
                                'translation': all_translations[direction][word]['translation'],
                                'confidence': all_translations[direction][word]['confidence']
                            }

                # Regenerate JS content
                js_content = f"const ALL_TRANSLATIONS = {json.dumps(all_translations, ensure_ascii=False, indent=2)};"

                # Check size again
                if len(js_content.encode('utf-8')) > max_size:
                    self.stdout.write(self.style.WARNING(
                        f"Warning: File still exceeds maximum size ({len(js_content.encode('utf-8'))} > {max_size} bytes). "
                        f"Only including high-confidence translations."
                    ))

                    # Create a new dictionary with only high-confidence translations
                    simplified_translations = {}
                    for direction in all_translations:
                        simplified_translations[direction] = {}
                        for word in all_translations[direction]:
                            if isinstance(all_translations[direction][word], dict):
                                if all_translations[direction][word]['confidence'] > 0.85:
                                    simplified_translations[direction][word] = all_translations[direction][word]['translation']
                            else:
                                simplified_translations[direction][word] = all_translations[direction][word]

                    # Regenerate JS content
                    js_content = f"const ALL_TRANSLATIONS = {json.dumps(simplified_translations, ensure_ascii=False, indent=2)};"

        # Write to file
        output_file = os.path.join(static_dir, 'all_translations.js')
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(js_content)

        # Create a word-by-word mappings file for more efficient lookups
        word_mappings = {}
        for direction in all_translations:
            word_mappings[direction] = {}
            for word in all_translations[direction]:
                if isinstance(all_translations[direction][word], dict):
                    word_mappings[direction][word] = {
                        'translation': all_translations[direction][word]['translation'],
                        'confidence': all_translations[direction][word]['confidence']
                    }
                else:
                    word_mappings[direction][word] = {
                        'translation': all_translations[direction][word],
                        'confidence': 0.9
                    }

        # Write word mappings to file
        word_mappings_js = f"const WORD_BY_WORD_MAPPINGS = {json.dumps(word_mappings, ensure_ascii=False, indent=2)};"
        word_mappings_file = os.path.join(static_dir, 'word_mappings.js')
        with open(word_mappings_file, 'w', encoding='utf-8') as f:
            f.write(word_mappings_js)

        # Output summary
        self.stdout.write(self.style.SUCCESS(
            f"Successfully exported {included_translations} of {total_translations} translations "
            f"to {output_file} ({len(js_content.encode('utf-8'))} bytes)"
        ))
        self.stdout.write(self.style.SUCCESS(
            f"Word mappings exported to {word_mappings_file} ({len(word_mappings_js.encode('utf-8'))} bytes)"
        ))
