import os
import django
import json
import requests

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Test the fast lookup function directly
from translation_app.fast_lookup import fast_lookup

print("Testing fast lookup function directly:")
result = fast_lookup('nagsisisi', 'tgl', 'ted')
print(f"Result: {result}")

# Test the API endpoint
print("\nTesting API endpoint:")
url = "http://127.0.0.1:8000/api/translate/"
data = {
    'text': 'nagsisisi',
    'source_lang': 'tgl',
    'target_lang': 'ted'
}

response = requests.post(url, json=data)
print(f"Status code: {response.status_code}")
print(f"Response: {response.json()}")
