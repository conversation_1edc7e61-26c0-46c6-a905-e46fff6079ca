/* Direct theme styles */

/* Dark mode */
html[data-theme="dark"] {
    --primary: #006837;
    --secondary: #8C5000;
    --accent: #FFC72C;
    --primary-fg: #fff;
    --body-fg: #e0e0e0;
    --body-bg: #121212;
    --header-color: #FFC72C;
    --header-branding-color: #FFC72C;
    --header-bg: #006837;
    --header-link-color: #fff;
    --breadcrumbs-fg: #e0e0e0;
    --breadcrumbs-link-fg: #e0e0e0;
    --breadcrumbs-bg: #333;
    --link-fg: #81d4fa;
    --link-hover-color: #4fc3f7;
    --link-selected-fg: #4fc3f7;
    --hairline-color: #333;
    --border-color: #333;
    --error-fg: #e57373;
    --message-success-bg: #43a047;
    --message-warning-bg: #ef6c00;
    --message-error-bg: #c62828;
    --card-bg: #1e1e1e;
    --selected-bg: #264f36;
    --selected-row: #264f36;
    --close-button-bg: #333;
    --close-button-hover-bg: #666;
}

/* Light mode */
html[data-theme="light"] {
    --primary: #006837;
    --secondary: #8C5000;
    --accent: #FFC72C;
    --primary-fg: #fff;
    --body-fg: #333;
    --body-bg: #F5F0E1;
    --header-color: #FFC72C;
    --header-branding-color: #FFC72C;
    --header-bg: #006837;
    --header-link-color: #fff;
}

/* Dark mode specific overrides */
html[data-theme="dark"] body {
    background-color: var(--body-bg);
    color: var(--body-fg);
}

html[data-theme="dark"] #header {
    background-color: var(--header-bg);
    color: var(--header-color);
}

html[data-theme="dark"] .module h2, 
html[data-theme="dark"] .module caption {
    background-color: var(--header-bg);
}

html[data-theme="dark"] a:link, 
html[data-theme="dark"] a:visited {
    color: var(--link-fg);
}

html[data-theme="dark"] a:hover {
    color: var(--link-hover-color);
}

html[data-theme="dark"] .button, 
html[data-theme="dark"] input[type=submit], 
html[data-theme="dark"] input[type=button], 
html[data-theme="dark"] .submit-row input, 
html[data-theme="dark"] a.button {
    background-color: var(--primary);
}

html[data-theme="dark"] .button:hover, 
html[data-theme="dark"] input[type=submit]:hover, 
html[data-theme="dark"] input[type=button]:hover {
    background-color: #004020;
}

html[data-theme="dark"] #changelist-filter li.selected a {
    color: var(--link-selected-fg);
}

html[data-theme="dark"] .paginator a:link, 
html[data-theme="dark"] .paginator a:visited {
    background-color: var(--primary);
}

/* Theme toggle button hover effect */
#theme-toggle-btn:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}
