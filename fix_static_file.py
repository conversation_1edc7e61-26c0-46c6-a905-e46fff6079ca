"""
<PERSON><PERSON><PERSON> to fix the static translations file in PythonAnywhere.
This script checks if the static file exists and has the correct format.
If not, it creates or fixes the file.

Run this script on PythonAnywhere to fix the "Could not find ALL_TRANSLATIONS variable in static file" error.
"""

import os
import json
import logging
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def is_pythonanywhere():
    """
    Detect if the app is running on PythonAnywhere.
    
    Returns:
        bool: True if running on PythonAnywhere, False otherwise
    """
    # Check for PythonAnywhere-specific environment variables or paths
    if 'PYTHONANYWHERE_DOMAIN' in os.environ:
        return True
    
    # Check for PythonAnywhere in the hostname
    try:
        import socket
        if 'pythonanywhere' in socket.gethostname().lower():
            return True
    except:
        pass
    
    # Check for PythonAnywhere in the sys.path
    for path in sys.path:
        if 'pythonanywhere' in path.lower():
            return True
    
    return False

def get_static_paths():
    """
    Get the static file paths based on the environment.
    
    Returns:
        tuple: (static_root, static_dirs)
    """
    if is_pythonanywhere():
        # PythonAnywhere paths
        static_root = '/home/<USER>/teduray/staticfiles'
        static_dirs = ['/home/<USER>/teduray/static']
        logger.info(f"PythonAnywhere detected. Using configured paths:")
        logger.info(f"STATIC_ROOT: {static_root}")
        logger.info(f"STATICFILES_DIRS: {static_dirs}")
    else:
        # Local paths
        static_root = 'static'
        static_dirs = ['translation_app/static']
        logger.info(f"Local environment detected. Using local paths:")
        logger.info(f"STATIC_ROOT: {static_root}")
        logger.info(f"STATICFILES_DIRS: {static_dirs}")
    
    return static_root, static_dirs

def fix_static_file():
    """
    Fix the static translations file.
    
    This function:
    1. Checks if the static file exists
    2. If not, creates it with the correct format
    3. If it exists but doesn't have the correct format, fixes it
    
    Returns:
        bool: True if successful, False otherwise
    """
    static_root, static_dirs = get_static_paths()
    
    # Define possible paths for the static file
    possible_paths = [
        os.path.join(static_root, 'translation_app', 'js', 'all_translations.js'),
    ]
    
    for static_dir in static_dirs:
        possible_paths.append(os.path.join(static_dir, 'translation_app', 'js', 'all_translations.js'))
    
    # Check if any of the paths exist
    existing_path = None
    for path in possible_paths:
        if os.path.exists(path):
            existing_path = path
            logger.info(f"Found existing static file at: {path}")
            break
    
    if existing_path:
        # Check if the file has the correct format
        with open(existing_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'const ALL_TRANSLATIONS = ' in content:
            logger.info("Static file has the correct format.")
            return True
        else:
            logger.warning("Static file exists but doesn't have the correct format. Fixing...")
            # Create a backup of the existing file
            backup_path = existing_path + '.bak'
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"Created backup of existing file at: {backup_path}")
            
            # Fix the file
            return create_static_file(existing_path)
    else:
        # File doesn't exist, create it
        logger.warning("Static file not found. Creating...")
        
        # Create the first path in the list
        path_to_create = possible_paths[0]
        return create_static_file(path_to_create)

def create_static_file(file_path):
    """
    Create a static translations file with the correct format.
    
    Args:
        file_path (str): Path to create the file
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Create the directory if it doesn't exist
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # Create a basic structure for the translations
        translations_dict = {
            'tgl_to_ted': {},
            'ted_to_tgl': {}
        }
        
        # Add some basic translations to ensure it's not empty
        translations_dict['tgl_to_ted']['hello'] = {
            'translation': 'Hello',
            'confidence': 0.9,
            'source': 'default',
            'part_of_speech': '',
            'notes': 'Default translation'
        }
        
        translations_dict['ted_to_tgl']['hello'] = {
            'translation': 'Hello',
            'confidence': 0.9,
            'source': 'default',
            'part_of_speech': '',
            'notes': 'Default translation'
        }
        
        # Create word-by-word mappings
        word_by_word_mappings = {
            'tgl_to_ted': {
                'hello': {
                    'translation': 'Hello',
                    'confidence': 0.9
                }
            },
            'ted_to_tgl': {
                'hello': {
                    'translation': 'Hello',
                    'confidence': 0.9
                }
            }
        }
        
        # Write the file with the correct format
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('const ALL_TRANSLATIONS = ')
            json.dump(translations_dict, f, ensure_ascii=False, indent=2)
            f.write(';\n\n')
            f.write('const WORD_BY_WORD_MAPPINGS = ')
            json.dump(word_by_word_mappings, f, ensure_ascii=False, indent=2)
            f.write(';\n')
        
        logger.info(f"Created static file at: {file_path}")
        return True
    except Exception as e:
        logger.error(f"Error creating static file: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("Starting static file fix script...")
    
    if fix_static_file():
        logger.info("Static file fix completed successfully!")
    else:
        logger.error("Failed to fix static file.")
