#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to directly fix the translation for "Yumuko ka" in the database.
This script ensures that the translation is properly stored and prioritized.
"""

import os
import sys
import logging
import django
from django.utils import timezone
from django.core.cache import cache

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fix_translation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('fix_translation')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Import Django models
from translation_app.models import (
    Language,
    ComprehensiveTranslation,
    TranslationVersion,
    TranslationExample
)
from translation_app.services import get_translation_service

def fix_yumuko_translation():
    """
    Fix the translation for "Yumuko ka" in the database.
    """
    logger.info("Starting to fix the translation for 'Yumuko ka'")
    
    try:
        # Get language objects
        tagalog = Language.objects.get(code='tgl')
        teduray = Language.objects.get(code='ted')
        
        # Check if the translation exists
        existing = ComprehensiveTranslation.objects.filter(
            base_word__iexact="Yumuko ka",
            source_language=tagalog,
            target_language=teduray
        ).first()
        
        if existing:
            logger.info(f"Found existing translation: {existing.base_word} → {existing.translation}")
            
            # Update the translation
            existing.translation = "Mëntëdungul go"
            existing.notes = (existing.notes or '') + f'\nFixed via direct script on {timezone.now().strftime("%Y-%m-%d")}'
            existing.save()
            
            # Create a new version with very high confidence
            TranslationVersion.objects.create(
                comprehensive_translation=existing,
                translation="Mëntëdungul go",
                confidence_score=0.99,  # Very high confidence
                is_active=True,
                notes='Fixed via direct script'
            )
            
            logger.info(f"Updated existing translation to: {existing.base_word} → Mëntëdungul go")
        else:
            # Create new translation
            new_trans = ComprehensiveTranslation.objects.create(
                base_word="Yumuko ka",
                translation="Mëntëdungul go",
                source_language=tagalog,
                target_language=teduray,
                part_of_speech='phrase',
                notes='Added via direct fix script'
            )
            
            # Create initial version with very high confidence
            TranslationVersion.objects.create(
                comprehensive_translation=new_trans,
                translation="Mëntëdungul go",
                confidence_score=0.99,  # Very high confidence
                is_active=True,
                notes='Created via direct fix script'
            )
            
            # Create example
            TranslationExample.objects.create(
                comprehensive_translation=new_trans,
                source_text="Yumuko ka",
                target_text="Mëntëdungul go"
            )
            
            logger.info(f"Created new translation: Yumuko ka → Mëntëdungul go")
        
        # Clear the cache
        logger.info("Clearing Django cache")
        cache.clear()
        
        # Update the translation service
        logger.info("Updating translation service")
        translation_service = get_translation_service(skip_loading_for_admin=False)
        translation_service.update_single_translation(
            "Yumuko ka",
            "Mëntëdungul go",
            'tgl',
            'ted'
        )
        
        # Force a reload of translations
        logger.info("Forcing a reload of translations")
        translation_service.reload_translations()
        
        # Check if the translation is now correct
        result = translation_service.translate_text("Yumuko ka", 'tgl', 'ted')
        logger.info(f"Current translation: {result.get('translation', 'Unknown')}")
        logger.info(f"Translation source: {result.get('source', 'Unknown')}")
        logger.info(f"Translation confidence: {result.get('confidence', 0) * 100:.0f}%")
        
        return True
    except Exception as e:
        logger.error(f"Error fixing translation: {str(e)}")
        return False

if __name__ == "__main__":
    success = fix_yumuko_translation()
    if success:
        logger.info("Successfully fixed the translation for 'Yumuko ka'")
    else:
        logger.error("Failed to fix the translation for 'Yumuko ka'")
