"""
Attention Mechanism Module for Translation System

This module provides utilities for implementing attention mechanisms
in the translation process, which helps improve translation quality
by focusing on relevant parts of the source text.
"""

import numpy as np
import json
from typing import List, Dict, Tuple, Any
from .models import AttentionData, Language

class AttentionMechanism:
    """
    Class for implementing attention mechanisms in the translation process.
    """

    def __init__(self):
        """Initialize the attention mechanism"""
        self.attention_weights = {}

        # Log initialization
        import logging
        logging.info("Initializing attention mechanism")

    def process_translation(self, source_text, result, source_lang, target_lang):
        """
        Process a translation request using the attention mechanism.
        This method is called from services.py.

        Args:
            source_text: Source text
            result: Translation result data
            source_lang: Source language code
            target_lang: Target language code

        Returns:
            dict: Improved translation data
        """
        import logging
        logging.info(f"process_translation called for: '{source_text}'")

        # Check if this is a sentence
        is_sentence = len(source_text.split()) > 1
        is_likely_sentence = any(p in source_text for p in '.?!,:;') or source_text.strip().endswith('?')

        # For sentences, use the special sentence translation method
        if is_sentence or is_likely_sentence:
            logging.info(f"Using generate_sentence_translation for sentence: '{source_text}'")
            return self.generate_sentence_translation(source_text, result, source_lang, target_lang)
        else:
            # For single words, use the regular apply_attention method
            logging.info(f"Using apply_attention for word: '{source_text}'")
            return self.apply_attention(source_text, result, source_lang, target_lang)

    def compute_attention(self, source_text: str, target_text: str,
                         source_lang_code: str, target_lang_code: str) -> Dict[str, Any]:
        """
        Compute attention weights between source and target text.
        Enhanced version for handling longer sentences and phrases.

        Args:
            source_text: Source text
            target_text: Target text
            source_lang_code: Source language code
            target_lang_code: Target language code

        Returns:
            dict: Attention data including weights
        """
        # Clean and normalize the texts
        import re
        source_clean = re.sub(r'[^\w\s]', ' ', source_text).lower()
        target_clean = re.sub(r'[^\w\s]', ' ', target_text).lower()

        # Tokenize the texts
        source_tokens = source_clean.split()
        target_tokens = target_clean.split()

        # Handle empty tokens
        if not source_tokens or not target_tokens:
            return {
                'source_text': source_text,
                'target_text': target_text,
                'source_lang': source_lang_code,
                'target_lang': target_lang_code,
                'attention_weights': []
            }

        # Initialize attention matrix
        attention_matrix = np.zeros((len(target_tokens), len(source_tokens)))

        # Compute attention weights using improved similarity measure
        for i, target_token in enumerate(target_tokens):
            for j, source_token in enumerate(source_tokens):
                # Use the improved similarity measure
                similarity = self._compute_similarity(target_token, source_token)
                attention_matrix[i, j] = similarity

        # Apply positional bias for longer sentences
        # Words in similar positions are more likely to be aligned
        if len(source_tokens) > 3 and len(target_tokens) > 3:
            for i in range(len(target_tokens)):
                for j in range(len(source_tokens)):
                    # Calculate relative position similarity
                    pos_sim = 1.0 - abs(i/len(target_tokens) - j/len(source_tokens))
                    # Apply positional bias (weighted by sentence length)
                    position_weight = 0.1 + 0.2 * min(1.0, max(len(source_tokens), len(target_tokens)) / 10)
                    attention_matrix[i, j] = (1 - position_weight) * attention_matrix[i, j] + position_weight * pos_sim

        # Apply bidirectional consistency check for more accurate alignments
        # This helps ensure that alignments are consistent in both directions
        forward_max_indices = np.argmax(attention_matrix, axis=1)
        backward_max_indices = np.argmax(attention_matrix.T, axis=1)

        # Create a consistency mask
        consistency_mask = np.zeros_like(attention_matrix)
        for i in range(len(target_tokens)):
            j = forward_max_indices[i]
            if backward_max_indices[j] == i:  # Consistent alignment
                consistency_mask[i, j] = 1.0

        # Boost consistent alignments
        attention_matrix = attention_matrix * (1.0 + 0.5 * consistency_mask)

        # Normalize weights for each target token
        for i in range(len(target_tokens)):
            if np.sum(attention_matrix[i]) > 0:
                attention_matrix[i] = attention_matrix[i] / np.sum(attention_matrix[i])

        # Convert to list of dictionaries for storage
        attention_weights = []
        for i, target_token in enumerate(target_tokens):
            for j, source_token in enumerate(source_tokens):
                if attention_matrix[i, j] > 0.1:  # Only store significant weights
                    attention_weights.append({
                        'target_idx': i,
                        'target_token': target_token,
                        'source_idx': j,
                        'source_token': source_token,
                        'weight': float(attention_matrix[i, j])
                    })

        # Store the attention data
        attention_data = {
            'source_text': source_text,
            'target_text': target_text,
            'source_lang': source_lang_code,
            'target_lang': target_lang_code,
            'attention_weights': attention_weights,
            'attention_matrix': attention_matrix.tolist()  # Include full matrix for advanced processing
        }

        # Save to database if languages exist
        try:
            source_language = Language.objects.get(code=source_lang_code)
            target_language = Language.objects.get(code=target_lang_code)

            AttentionData.objects.create(
                source_text=source_text,
                target_text=target_text,
                source_language=source_language,
                target_language=target_language,
                attention_weights=json.dumps(attention_weights)
            )
        except Language.DoesNotExist:
            pass  # Skip database storage if languages don't exist
        except Exception as e:
            print(f"Error storing attention data: {str(e)}")

        return attention_data

    def _compute_similarity(self, token1: str, token2: str) -> float:
        """
        Compute similarity between two tokens using a more sophisticated approach.

        Args:
            token1: First token
            token2: Second token

        Returns:
            float: Similarity score (0.0-1.0)
        """
        # Convert to lowercase
        token1 = token1.lower()
        token2 = token2.lower()

        if not token1 or not token2:
            return 0.0

        # 1. Character overlap similarity (Jaccard index)
        chars1 = set(token1)
        chars2 = set(token2)

        if not chars1 or not chars2:
            return 0.0

        intersection = len(chars1.intersection(chars2))
        union = len(chars1.union(chars2))

        jaccard_score = intersection / union if union > 0 else 0.0

        # 2. Longest common subsequence similarity
        lcs_length = self._longest_common_subsequence(token1, token2)
        lcs_score = lcs_length / max(len(token1), len(token2)) if max(len(token1), len(token2)) > 0 else 0.0

        # 3. Prefix similarity (important for morphologically related words)
        prefix_length = 0
        for i in range(min(len(token1), len(token2))):
            if token1[i] == token2[i]:
                prefix_length += 1
            else:
                break

        prefix_score = prefix_length / min(len(token1), len(token2)) if min(len(token1), len(token2)) > 0 else 0.0

        # 4. Suffix similarity (important for morphologically related words)
        suffix_length = 0
        for i in range(1, min(len(token1), len(token2)) + 1):
            if token1[-i] == token2[-i]:
                suffix_length += 1
            else:
                break

        suffix_score = suffix_length / min(len(token1), len(token2)) if min(len(token1), len(token2)) > 0 else 0.0

        # 5. Length similarity
        length_ratio = min(len(token1), len(token2)) / max(len(token1), len(token2)) if max(len(token1), len(token2)) > 0 else 0.0

        # Weighted combination of all similarity measures
        # Emphasize LCS and prefix/suffix for morphologically rich languages
        combined_score = (
            jaccard_score * 0.3 +
            lcs_score * 0.3 +
            prefix_score * 0.2 +
            suffix_score * 0.1 +
            length_ratio * 0.1
        )

        return combined_score

    def _longest_common_subsequence(self, str1: str, str2: str) -> int:
        """
        Calculate the length of the longest common subsequence between two strings.

        Args:
            str1: First string
            str2: Second string

        Returns:
            int: Length of the longest common subsequence
        """
        m, n = len(str1), len(str2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]

        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if str1[i-1] == str2[j-1]:
                    dp[i][j] = dp[i-1][j-1] + 1
                else:
                    dp[i][j] = max(dp[i-1][j], dp[i][j-1])

        return dp[m][n]

    def apply_attention(self, source_text: str, translation_data: Dict[str, Any],
                      source_lang_code: str, target_lang_code: str) -> Dict[str, Any]:
        """
        Apply attention mechanism to improve translation, especially for longer sentences.
        Enhanced version that always generates complete sentence translations.

        Args:
            source_text: Source text
            translation_data: Translation data from the translation service
            source_lang_code: Source language code
            target_lang_code: Target language code

        Returns:
            dict: Improved translation data
        """
        # Import necessary modules at the beginning to avoid reference errors
        import logging
        from django.core.cache import cache
        from translation_app.models import ComprehensiveTranslation, Language

        logger = logging.getLogger(__name__)

        # Always log what we're working with
        logger.info(f"Applying attention to: '{source_text}' with initial translation: '{translation_data.get('translation', 'None')}'")

        # For sentences, always try to improve the translation, even if confidence is high
        # For single words, only improve if confidence is low
        if translation_data and 'translation' in translation_data:
            # Check if this is a single word or a sentence
            is_sentence = len(source_text.split()) > 1
            is_likely_sentence = any(p in source_text for p in '.?!,:;') or source_text.strip().endswith('?')

            # Check the source of the translation
            translation_source = translation_data.get('source', '')

            # For sentences, always force a new translation
            if is_sentence or is_likely_sentence:
                logger.info(f"Detected sentence: '{source_text}'. Will use enhanced attention mechanism.")
                translation_data['force_new_translation'] = True

                # Log additional information
                source_word_count = len(source_text.split())
                translation_word_count = len(translation_data.get('translation', '').split())

                # Check if the translation is suspiciously short
                if translation_word_count < source_word_count / 2:
                    logger.warning(f"Existing translation is suspiciously short: {source_word_count} words -> {translation_word_count} words. Will generate new translation.")

                # Force new translation for sentences with database_combined source
                if translation_source == 'database_combined':
                    logger.warning(f"Translation source is database_combined for sentence: '{source_text}'. Will generate new translation.")

                # Force new translation for exact_match source
                if translation_source == 'exact_match':
                    logger.warning(f"Translation source is exact_match for sentence: '{source_text}'. Will generate new translation.")

                # Log that we're using the enhanced attention mechanism
                logger.info(f"Enabled attention mechanism for this request")

            # If it's a single word with high confidence, return it as is
            elif not is_sentence and not is_likely_sentence and translation_data.get('confidence', 0) > 0.8:
                return translation_data

            # For sentences, we'll continue with the improvement process regardless of confidence

        # Clean the source text
        import re
        source_clean = re.sub(r'[^\w\s]', ' ', source_text).lower()
        source_tokens = source_clean.split()

        # If it's a short text, no need for complex processing
        if len(source_tokens) <= 3:
            return translation_data

        # For longer texts, try to find similar translations in the database
        try:
            from django.db.models import Q

            # Get language objects - we already imported Language at the beginning
            source_lang = Language.objects.get(code=source_lang_code)
            target_lang = Language.objects.get(code=target_lang_code)

            # Find similar translations in the database
            # First try to find exact matches for subphrases
            matches = []

            # Try different n-gram sizes for matching
            for n in range(min(5, len(source_tokens)), 1, -1):
                for i in range(len(source_tokens) - n + 1):
                    # Extract n-gram
                    ngram = ' '.join(source_tokens[i:i+n])

                    # Look for matches in the database
                    exact_matches = ComprehensiveTranslation.objects.filter(
                        base_word__iexact=ngram,
                        source_language=source_lang,
                        target_language=target_lang
                    ).order_by('-confidence_score')[:3]

                    for match in exact_matches:
                        # Get the active version if available
                        active_version = match.versions.filter(is_active=True).first()
                        translation_text = active_version.translation if active_version else match.translation
                        confidence = active_version.confidence_score if active_version else match.confidence_score

                        matches.append({
                            'source': ngram,
                            'translation': translation_text,
                            'confidence': confidence,
                            'position': i,
                            'length': n
                        })

            # If we found matches, use them to construct a better translation
            if matches:
                # Sort by position
                matches.sort(key=lambda x: x['position'])

                # Construct a translation using the matches
                constructed_parts = []
                covered_positions = set()

                # First pass: add high-confidence matches that don't overlap
                for match in sorted(matches, key=lambda x: x['confidence'], reverse=True):
                    positions = set(range(match['position'], match['position'] + match['length']))

                    # Skip if this would overlap with already covered positions
                    if not positions.intersection(covered_positions):
                        constructed_parts.append({
                            'translation': match['translation'],
                            'position': match['position'],
                            'length': match['length'],
                            'confidence': match['confidence']
                        })
                        covered_positions.update(positions)

                # Sort by position for final assembly
                constructed_parts.sort(key=lambda x: x['position'])

                # Fill gaps with the original translation if available
                if 'word_by_word' in translation_data and translation_data['word_by_word']:
                    word_by_word = translation_data['word_by_word']

                    # Create a complete translation with gaps filled
                    final_parts = []
                    current_pos = 0

                    for part in constructed_parts:
                        # Add any words before this part
                        if part['position'] > current_pos:
                            gap_words = []
                            for i in range(current_pos, part['position']):
                                if i < len(word_by_word) and 'translation' in word_by_word[i]:
                                    gap_words.append(word_by_word[i]['translation'])

                            if gap_words:
                                final_parts.append(' '.join(gap_words))

                        # Add this part
                        final_parts.append(part['translation'])
                        current_pos = part['position'] + part['length']

                    # Add any remaining words
                    if current_pos < len(word_by_word):
                        gap_words = []
                        for i in range(current_pos, len(word_by_word)):
                            if 'translation' in word_by_word[i]:
                                gap_words.append(word_by_word[i]['translation'])

                        if gap_words:
                            final_parts.append(' '.join(gap_words))

                    # Join all parts
                    improved_translation = ' '.join(final_parts)

                    # Calculate average confidence
                    avg_confidence = sum(p['confidence'] for p in constructed_parts) / len(constructed_parts) if constructed_parts else 0.7

                    # Create improved translation data
                    improved_data = translation_data.copy()
                    improved_data['translation'] = improved_translation
                    improved_data['confidence'] = avg_confidence
                    improved_data['source'] = 'attention_improved'

                    return improved_data

            # If we couldn't construct a better translation from database matches,
            # generate a translation using word-by-word approach
            try:
                # If the original translation is missing, just returning the source text,
                # is an exact match, is suspiciously short, or we're forcing a new translation,
                # we need to generate a new translation
                if (not translation_data or
                    not translation_data.get('translation') or
                    translation_data.get('translation') == source_text or
                    translation_data.get('source') == 'exact_match' or
                    translation_data.get('force_new_translation', False) or
                    (translation_data.get('translation') and
                     len(translation_data.get('translation').split()) < len(source_tokens) / 2) or
                    # Always generate new translations for sentences
                    len(source_tokens) > 3):

                    import logging
                    logging.info(f"Generating new translation for: {source_text}")

                    # First try to find translations for multi-word phrases
                    word_translations = []
                    covered_indices = set()

                    # Check if we have an exact match reference from the cache
                    # We already imported cache at the beginning of the method
                    try:
                        # Use cache instead of session since we don't have direct access to the request
                        exact_match_ref = cache.get('exact_match_reference')
                        if exact_match_ref:
                            if (exact_match_ref.get('original') == source_text and
                                exact_match_ref.get('source_lang') == source_lang_code and
                                exact_match_ref.get('target_lang') == target_lang_code):

                                logging.info(f"Using exact match reference: {exact_match_ref.get('translation')}")

                                # Use this as a reference but still generate our own translation
                                # We'll add it as a special phrase covering the entire sentence
                                word_translations.append({
                                    'original': source_text,
                                    'translation': exact_match_ref.get('translation'),
                                    'confidence': 0.9,
                                    'position': -1,  # Special position to indicate it's a reference
                                    'length': len(source_tokens),
                                    'is_reference': True
                                })

                                # Clear the cache reference to avoid reusing it
                                cache.delete('exact_match_reference')
                    except Exception as e:
                        logging.error(f"Error checking for exact match reference: {str(e)}")

                    # Try different n-gram sizes for matching
                    for n in range(min(5, len(source_tokens)), 1, -1):
                        for i in range(len(source_tokens) - n + 1):
                            # Skip if any of these positions are already covered
                            if any(j in covered_indices for j in range(i, i+n)):
                                continue

                            # Extract n-gram
                            ngram = ' '.join(source_tokens[i:i+n])

                            # Look for matches in the database
                            phrase_match = ComprehensiveTranslation.objects.filter(
                                base_word__iexact=ngram,
                                source_language=source_lang,
                                target_language=target_lang
                            ).order_by('-confidence_score').first()

                            # Also try partial matches for better coverage
                            if not phrase_match and n > 2:
                                # Try partial match
                                phrase_match = ComprehensiveTranslation.objects.filter(
                                    base_word__icontains=ngram,
                                    source_language=source_lang,
                                    target_language=target_lang
                                ).order_by('-confidence_score').first()

                            if phrase_match:
                                # Add this phrase translation
                                word_translations.append({
                                    'original': ngram,
                                    'translation': phrase_match.translation,
                                    'confidence': 0.9,
                                    'position': i,
                                    'length': n
                                })
                                # Mark these positions as covered
                                covered_indices.update(range(i, i+n))

                    # Now get individual word translations for any words not covered by phrases
                    for i, word in enumerate(source_tokens):
                        if i in covered_indices:
                            continue  # Skip words that are part of phrases

                        word_trans = ComprehensiveTranslation.objects.filter(
                            base_word__iexact=word,
                            source_language=source_lang,
                            target_language=target_lang
                        ).order_by('-confidence_score').first()

                        if word_trans:
                            word_translations.append({
                                'original': word,
                                'translation': word_trans.translation,
                                'confidence': 0.85,
                                'position': i,
                                'length': 1
                            })
                        else:
                            # If no translation found, keep the original word
                            word_translations.append({
                                'original': word,
                                'translation': word,
                                'confidence': 0.5,
                                'position': i,
                                'length': 1
                            })

                    # Check if we have a reference translation
                    reference_translation = None
                    regular_translations = []

                    for wt in word_translations:
                        if wt.get('is_reference', False):
                            reference_translation = wt
                        else:
                            regular_translations.append(wt)

                    # Sort regular translations by position
                    regular_translations.sort(key=lambda x: x.get('position', 0))

                    # Construct a new translation
                    # For sentences, we need to ensure we generate a proper translation
                    # that's proportional to the input length
                    source_word_count = len(source_tokens)

                    # Check if this is a sentence that needs a proper translation
                    is_sentence = source_word_count > 3 or any(p in source_text for p in '.?!,:;') or source_text.strip().endswith('?')

                    if is_sentence:
                        # For sentences, we need to ensure the translation is proportional
                        # First, try to use the reference if available
                        if reference_translation:
                            reference_word_count = len(reference_translation['translation'].split())
                            reference_ratio = reference_word_count / source_word_count

                            # If the reference translation is proportional, use it
                            if reference_ratio >= 0.5 and reference_ratio <= 2.0:
                                logging.info(f"Using reference translation: {reference_translation['translation']}")
                                new_translation = reference_translation['translation']

                                # Add the reference to the regular translations for word-by-word output
                                # but mark it as a special type
                                for wt in regular_translations:
                                    wt['source'] = 'phrase_match'

                                # Use both for confidence calculation
                                all_translations = regular_translations + [reference_translation]
                            else:
                                # Reference translation is not proportional, construct a new one
                                logging.info(f"Reference translation not proportional ({reference_ratio:.2f}), constructing new one")

                                # Use the constructed translation from regular matches
                                constructed_translation = ' '.join([wt['translation'] for wt in regular_translations])
                                constructed_word_count = len(constructed_translation.split())

                                # If the constructed translation is too short, try to expand it
                                if constructed_word_count < source_word_count * 0.5:
                                    logging.info(f"Constructed translation too short, trying to expand it")

                                    # Try to find more translations for uncovered words
                                    for i, word in enumerate(source_tokens):
                                        if i not in covered_indices:
                                            word_trans = ComprehensiveTranslation.objects.filter(
                                                base_word__iexact=word,
                                                source_language=source_lang,
                                                target_language=target_lang
                                            ).order_by('-confidence_score').first()

                                            if word_trans:
                                                regular_translations.append({
                                                    'original': word,
                                                    'translation': word_trans.translation,
                                                    'confidence': 0.7,
                                                    'position': i,
                                                    'length': 1
                                                })

                                    # Sort by position and reconstruct
                                    regular_translations.sort(key=lambda x: x.get('position', 0))
                                    constructed_translation = ' '.join([wt['translation'] for wt in regular_translations])

                                new_translation = constructed_translation
                                all_translations = regular_translations
                        else:
                            # No reference, use the constructed translation
                            constructed_translation = ' '.join([wt['translation'] for wt in regular_translations])
                            constructed_word_count = len(constructed_translation.split())

                            # If the constructed translation is too short, try to expand it
                            if constructed_word_count < source_word_count * 0.5:
                                logging.info(f"Constructed translation too short, trying to expand it")

                                # Try to find more translations for uncovered words
                                for i, word in enumerate(source_tokens):
                                    if i not in covered_indices:
                                        word_trans = ComprehensiveTranslation.objects.filter(
                                            base_word__iexact=word,
                                            source_language=source_lang,
                                            target_language=target_lang
                                        ).order_by('-confidence_score').first()

                                        if word_trans:
                                            regular_translations.append({
                                                'original': word,
                                                'translation': word_trans.translation,
                                                'confidence': 0.7,
                                                'position': i,
                                                'length': 1
                                            })

                                # Sort by position and reconstruct
                                regular_translations.sort(key=lambda x: x.get('position', 0))
                                constructed_translation = ' '.join([wt['translation'] for wt in regular_translations])

                            new_translation = constructed_translation
                            all_translations = regular_translations
                    else:
                        # For shorter phrases, simpler logic
                        if reference_translation and len(regular_translations) < 3:
                            # If we have a reference and few regular translations, use the reference
                            constructed_translation = ' '.join([wt['translation'] for wt in regular_translations])
                            logging.info(f"Using reference translation instead of constructed: {constructed_translation}")
                            new_translation = reference_translation['translation']

                            # Add the reference to the regular translations for word-by-word output
                            for wt in regular_translations:
                                wt['source'] = 'phrase_match'

                            # Use both for confidence calculation
                            all_translations = regular_translations + [reference_translation]
                        else:
                            # Use the constructed translation from regular matches
                            new_translation = ' '.join([wt['translation'] for wt in regular_translations])
                            all_translations = regular_translations

                            # If we have a reference, compare and log the difference
                            if reference_translation:
                                logging.info(f"Reference translation: {reference_translation['translation']}")
                                logging.info(f"Constructed translation: {new_translation}")

                    # Calculate average confidence
                    avg_confidence = sum(wt['confidence'] for wt in all_translations) / len(all_translations) if all_translations else 0.75

                    # Create a simplified word_by_word without position information for API response
                    simplified_word_by_word = []

                    # If we used the reference translation, add it as a single entry
                    if reference_translation and len(regular_translations) < 3:
                        simplified_word_by_word.append({
                            'original': source_text,
                            'translation': new_translation,
                            'confidence': reference_translation['confidence'],
                            'source': 'attention_with_reference'
                        })
                    else:
                        # Add regular translations
                        for wt in regular_translations:
                            simplified_word_by_word.append({
                                'original': wt['original'],
                                'translation': wt['translation'],
                                'confidence': wt['confidence'],
                                'source': wt.get('source', 'attention_generated')
                            })

                    # Create a new translation data dictionary
                    # For sentences, always use attention_generated or attention_improved
                    if is_sentence:
                        if reference_translation and new_translation == reference_translation['translation']:
                            source_type = 'attention_improved'
                            notes = 'Improved using attention mechanism with database reference'
                        else:
                            source_type = 'attention_generated'
                            notes = 'Generated using attention mechanism'
                    else:
                        # For shorter phrases, use the original logic
                        source_type = 'attention_with_reference' if (reference_translation and len(regular_translations) < 3) else 'attention_generated'
                        notes = 'Generated using attention mechanism'

                    # Add more details to notes
                    if reference_translation and not notes.endswith('reference'):
                        notes += ' with database reference'
                    if len(regular_translations) > 0:
                        notes += f' and {len(regular_translations)} phrase matches'

                    # Add source and target word counts for validation
                    source_word_count = len(source_tokens)
                    target_word_count = len(new_translation.split())
                    ratio = target_word_count / source_word_count if source_word_count > 0 else 0
                    notes += f'. Word ratio: {ratio:.2f} ({source_word_count} → {target_word_count})'

                    new_translation_data = {
                        'original': source_text,
                        'translation': new_translation,
                        'confidence': avg_confidence,
                        'source': source_type,
                        'notes': notes,
                        'word_by_word': simplified_word_by_word,
                        'source_word_count': source_word_count,
                        'target_word_count': target_word_count,
                        'word_ratio': ratio
                    }

                    logging.info(f"Generated translation: {source_text} → {new_translation}")
                    return new_translation_data

            except Exception as e:
                import logging
                logging.error(f"Error generating translation: {str(e)}")

            # If all else fails, return the original
            return translation_data

        except Exception as e:
            import logging
            logging.error(f"Error in apply_attention: {str(e)}")
            return translation_data

    def calculate_attention(self, source_tokens: List[str], target_tokens: List[str],
                           word_by_word: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate attention weights between source and target tokens.
        This is an alias for compatibility with code that expects this method name.

        Args:
            source_tokens: List of source tokens
            target_tokens: List of target tokens
            word_by_word: Word-by-word translation data

        Returns:
            dict: Attention data including weights and matrix
        """
        # Join tokens to create text
        source_text = ' '.join(source_tokens)
        target_text = ' '.join(target_tokens)

        # Use compute_attention to get the attention data
        attention_data = self.compute_attention(
            source_text, target_text, 'tgl', 'ted'  # Default to Tagalog -> Teduray
        )

        # Add the attention matrix for compatibility
        if 'attention_matrix' not in attention_data and 'attention_weights' in attention_data:
            # Create a matrix from the weights
            matrix = np.zeros((len(target_tokens), len(source_tokens)))

            for weight in attention_data['attention_weights']:
                i = weight.get('target_idx', 0)
                j = weight.get('source_idx', 0)
                w = weight.get('weight', 0.0)

                if i < len(target_tokens) and j < len(source_tokens):
                    matrix[i, j] = w

            attention_data['attention_matrix'] = matrix.tolist()

        return attention_data

    def visualize_attention(self, attention_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate visualization data for attention weights.

        Args:
            attention_data: Attention data including weights

        Returns:
            dict: Visualization data
        """
        source_tokens = attention_data['source_text'].split()
        target_tokens = attention_data['target_text'].split()
        weights = attention_data['attention_weights']

        # Create a matrix representation for visualization
        matrix = np.zeros((len(target_tokens), len(source_tokens)))

        for weight_data in weights:
            i = weight_data['target_idx']
            j = weight_data['source_idx']
            w = weight_data['weight']

            if i < len(target_tokens) and j < len(source_tokens):
                matrix[i, j] = w

        # Generate visualization data
        visualization_data = {
            'source_tokens': source_tokens,
            'target_tokens': target_tokens,
            'attention_matrix': matrix.tolist()
        }

        return visualization_data

# Add a new method to the AttentionMechanism class
def generate_sentence_translation(self, source_text, translation_data, source_lang_code, target_lang_code):
    """
    Generate a complete sentence translation using the attention mechanism.
    This method is specifically designed for translating sentences.

    Args:
        source_text: Source text
        translation_data: Translation data from the translation service
        source_lang_code: Source language code
        target_lang_code: Target language code

    Returns:
        dict: Improved translation data
    """
    import logging
    import re
    from django.core.cache import cache
    from translation_app.models import ComprehensiveTranslation, Language

    logging.info(f"Generating sentence translation for: '{source_text}'")

    # Always force a new translation for sentences
    translation_data['force_new_translation'] = True

    # Clean the source text
    source_clean = re.sub(r'[^\w\s]', ' ', source_text.lower())
    source_tokens = source_clean.split()

    # Get language objects
    try:
        source_lang = Language.objects.get(code=source_lang_code)
        target_lang = Language.objects.get(code=target_lang_code)
    except Exception as e:
        logging.error(f"Error getting language objects: {str(e)}")
        return translation_data

    # Generate a new translation using word-by-word approach
    word_translations = []
    covered_indices = set()

    # First try to find translations for multi-word phrases
    for n in range(min(5, len(source_tokens)), 1, -1):
        for i in range(len(source_tokens) - n + 1):
            # Skip if any of these positions are already covered
            if any(j in covered_indices for j in range(i, i+n)):
                continue

            # Extract n-gram
            ngram = ' '.join(source_tokens[i:i+n])

            # Look for matches in the database
            try:
                phrase_match = ComprehensiveTranslation.objects.filter(
                    base_word__iexact=ngram,
                    source_language=source_lang,
                    target_language=target_lang
                ).order_by('-confidence_score').first()

                if phrase_match:
                    # Add this phrase translation
                    word_translations.append({
                        'original': ngram,
                        'translation': phrase_match.translation,
                        'confidence': 0.9,
                        'position': i,
                        'length': n
                    })
                    # Mark these positions as covered
                    covered_indices.update(range(i, i+n))
            except Exception as e:
                logging.error(f"Error finding phrase match: {str(e)}")

    # Now get individual word translations for any words not covered by phrases
    for i, word in enumerate(source_tokens):
        if i in covered_indices:
            continue  # Skip words that are part of phrases

        try:
            word_trans = ComprehensiveTranslation.objects.filter(
                base_word__iexact=word,
                source_language=source_lang,
                target_language=target_lang
            ).order_by('-confidence_score').first()

            if word_trans:
                word_translations.append({
                    'original': word,
                    'translation': word_trans.translation,
                    'confidence': 0.85,
                    'position': i,
                    'length': 1
                })
            else:
                # If no translation found, keep the original word
                word_translations.append({
                    'original': word,
                    'translation': word,
                    'confidence': 0.5,
                    'position': i,
                    'length': 1
                })
        except Exception as e:
            logging.error(f"Error finding word translation: {str(e)}")
            # If error, keep the original word
            word_translations.append({
                'original': word,
                'translation': word,
                'confidence': 0.5,
                'position': i,
                'length': 1
            })

    # Sort translations by position
    word_translations.sort(key=lambda x: x.get('position', 0))

    # Construct the translation
    constructed_translation = ' '.join([wt['translation'] for wt in word_translations])

    # Calculate average confidence
    avg_confidence = sum(wt['confidence'] for wt in word_translations) / len(word_translations) if word_translations else 0.75

    # Create a simplified word_by_word without position information for API response
    simplified_word_by_word = []
    for wt in word_translations:
        simplified_word_by_word.append({
            'original': wt['original'],
            'translation': wt['translation'],
            'confidence': wt['confidence'],
            'source': 'attention_generated'
        })

    # Create the result
    source_word_count = len(source_tokens)
    target_word_count = len(constructed_translation.split())
    ratio = target_word_count / source_word_count if source_word_count > 0 else 0

    result = {
        'original': source_text,
        'translation': constructed_translation,
        'confidence': avg_confidence,
        'source': 'attention_generated',
        'notes': f'Generated using attention mechanism. Word ratio: {ratio:.2f} ({source_word_count} → {target_word_count})',
        'word_by_word': simplified_word_by_word
    }

    logging.info(f"Generated translation: {source_text} → {constructed_translation}")
    return result

# Add the method to the AttentionMechanism class
AttentionMechanism.generate_sentence_translation = generate_sentence_translation

# Add a direct implementation of apply_attention that will be called from services.py
def direct_apply_attention(self, source_text, result, source_lang, target_lang):
    """
    Direct implementation of apply_attention that will be called from services.py.
    This method generates a complete sentence translation.

    Args:
        source_text: Source text
        result: Translation result data
        source_lang: Source language code
        target_lang: Target language code

    Returns:
        dict: Improved translation data
    """
    import logging
    import re
    from django.core.cache import cache
    from translation_app.models import ComprehensiveTranslation, Language

    logging.info(f"Direct apply_attention called for: '{source_text}'")

    # Check if this is a sentence
    is_sentence = len(source_text.split()) > 1
    is_likely_sentence = any(p in source_text for p in '.?!,:;') or source_text.strip().endswith('?')

    # For sentences, generate a new translation
    if is_sentence or is_likely_sentence:
        logging.info(f"SENTENCE DETECTED: '{source_text}'. Generating proper sentence translation...")

        # Clean the source text
        source_clean = re.sub(r'[^\w\s]', ' ', source_text.lower())
        source_tokens = source_clean.split()

        # Get language objects
        try:
            source_lang_obj = Language.objects.get(code=source_lang)
            target_lang_obj = Language.objects.get(code=target_lang)
        except Exception as e:
            logging.error(f"Error getting language objects: {str(e)}")
            return result

        # STEP 1: Try to find similar sentences in the database
        logging.info("Step 1: Looking for similar sentences in the database")
        similar_sentences = []

        try:
            # Look for sentences with similar words
            for word in source_tokens:
                similar = ComprehensiveTranslation.objects.filter(
                    base_word__icontains=word,
                    source_language=source_lang_obj,
                    target_language=target_lang_obj,
                    part_of_speech__in=['sentence', 'phrase']
                ).order_by('-confidence_score')[:5]

                for match in similar:
                    # Calculate similarity score based on word overlap
                    match_words = match.base_word.lower().split()
                    common_words = set(source_tokens).intersection(set(match_words))
                    similarity = len(common_words) / max(len(source_tokens), len(match_words))

                    if similarity > 0.3:  # Only consider if at least 30% similar
                        similar_sentences.append({
                            'base_word': match.base_word,
                            'translation': match.translation,
                            'similarity': similarity
                        })

            # Remove duplicates and sort by similarity
            unique_similar = {}
            for s in similar_sentences:
                if s['base_word'] not in unique_similar or s['similarity'] > unique_similar[s['base_word']]['similarity']:
                    unique_similar[s['base_word']] = s

            similar_sentences = list(unique_similar.values())
            similar_sentences.sort(key=lambda x: x['similarity'], reverse=True)

            if similar_sentences:
                logging.info(f"Found {len(similar_sentences)} similar sentences")
                for s in similar_sentences[:3]:  # Log top 3
                    logging.info(f"Similar: '{s['base_word']}' → '{s['translation']}' (similarity: {s['similarity']:.2f})")
        except Exception as e:
            logging.error(f"Error finding similar sentences: {str(e)}")

        # STEP 2: Generate a new translation using word-by-word approach
        logging.info("Step 2: Generating word-by-word translation")
        word_translations = []
        covered_indices = set()

        # First try to find translations for multi-word phrases
        for n in range(min(5, len(source_tokens)), 1, -1):
            for i in range(len(source_tokens) - n + 1):
                # Skip if any of these positions are already covered
                if any(j in covered_indices for j in range(i, i+n)):
                    continue

                # Extract n-gram
                ngram = ' '.join(source_tokens[i:i+n])

                # Look for matches in the database
                try:
                    phrase_matches = ComprehensiveTranslation.objects.filter(
                        base_word__iexact=ngram,
                        source_language=source_lang_obj,
                        target_language=target_lang_obj
                    ).order_by('-confidence_score')[:3]

                    if phrase_matches:
                        phrase_match = phrase_matches[0]  # Use the highest confidence match
                        logging.info(f"Found phrase match: '{ngram}' → '{phrase_match.translation}'")

                        # Add this phrase translation
                        word_translations.append({
                            'original': ngram,
                            'translation': phrase_match.translation,
                            'confidence': 0.9,
                            'position': i,
                            'length': n
                        })
                        # Mark these positions as covered
                        covered_indices.update(range(i, i+n))
                except Exception as e:
                    logging.error(f"Error finding phrase match: {str(e)}")

        # Now get individual word translations for any words not covered by phrases
        for i, word in enumerate(source_tokens):
            if i in covered_indices:
                continue  # Skip words that are part of phrases

            try:
                word_matches = ComprehensiveTranslation.objects.filter(
                    base_word__iexact=word,
                    source_language=source_lang_obj,
                    target_language=target_lang_obj
                ).order_by('-confidence_score')[:3]

                if word_matches:
                    word_trans = word_matches[0]  # Use the highest confidence match
                    logging.info(f"Found word match: '{word}' → '{word_trans.translation}'")

                    word_translations.append({
                        'original': word,
                        'translation': word_trans.translation,
                        'confidence': 0.85,
                        'position': i,
                        'length': 1
                    })
                else:
                    # If no translation found, keep the original word
                    logging.info(f"No translation found for word: '{word}', keeping original")
                    word_translations.append({
                        'original': word,
                        'translation': word,
                        'confidence': 0.5,
                        'position': i,
                        'length': 1
                    })
            except Exception as e:
                logging.error(f"Error finding word translation: {str(e)}")
                # If error, keep the original word
                word_translations.append({
                    'original': word,
                    'translation': word,
                    'confidence': 0.5,
                    'position': i,
                    'length': 1
                })

        # Sort translations by position
        word_translations.sort(key=lambda x: x.get('position', 0))

        # STEP 3: Construct the translation
        logging.info("Step 3: Constructing final translation")

        # If we have similar sentences, use them to improve the translation
        final_translation = ""
        if similar_sentences and similar_sentences[0]['similarity'] > 0.7:
            # If we have a very similar sentence, use its translation as a base
            best_match = similar_sentences[0]
            logging.info(f"Using similar sentence as base: '{best_match['base_word']}' → '{best_match['translation']}'")
            final_translation = best_match['translation']
        else:
            # Otherwise, construct from word-by-word translations
            constructed_translation = ' '.join([wt['translation'] for wt in word_translations])
            logging.info(f"Constructed translation: '{constructed_translation}'")

            # Check if the constructed translation is too short
            source_word_count = len(source_tokens)
            constructed_word_count = len(constructed_translation.split())

            if constructed_word_count < source_word_count * 0.5:
                logging.warning(f"Constructed translation too short: {constructed_word_count} words vs {source_word_count} words in source")

                # Try to use similar sentences to improve
                if similar_sentences:
                    best_match = similar_sentences[0]
                    logging.info(f"Using similar sentence to improve: '{best_match['base_word']}' → '{best_match['translation']}'")
                    final_translation = best_match['translation']
                else:
                    final_translation = constructed_translation
            else:
                final_translation = constructed_translation

        # STEP 4: Create the result
        logging.info(f"Step 4: Creating final result with translation: '{final_translation}'")

        # Calculate average confidence
        avg_confidence = sum(wt['confidence'] for wt in word_translations) / len(word_translations) if word_translations else 0.75

        # Create a simplified word_by_word without position information for API response
        simplified_word_by_word = []
        for wt in word_translations:
            simplified_word_by_word.append({
                'original': wt['original'],
                'translation': wt['translation'],
                'confidence': wt['confidence'],
                'source': 'attention_generated'
            })

        # Create the result
        source_word_count = len(source_tokens)
        target_word_count = len(final_translation.split())
        ratio = target_word_count / source_word_count if source_word_count > 0 else 0

        new_result = {
            'original': source_text,
            'translation': final_translation,
            'confidence': avg_confidence,
            'source': 'attention_generated',
            'notes': f'Generated using enhanced attention mechanism. Word ratio: {ratio:.2f} ({source_word_count} → {target_word_count})',
            'word_by_word': simplified_word_by_word
        }

        logging.info(f"FINAL TRANSLATION: {source_text} → {final_translation}")
        return new_result
    else:
        # For single words, return the original result
        return result

# Add the method to the AttentionMechanism class
AttentionMechanism.apply_attention = direct_apply_attention

# Singleton instance
attention_mechanism = AttentionMechanism()
