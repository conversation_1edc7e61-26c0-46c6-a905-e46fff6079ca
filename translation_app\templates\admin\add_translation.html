<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Translation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    {% load i18n static %}
    <link rel="stylesheet" href="{% static 'css/teduray-theme.css' %}">
</head>
<body>

<div class="philippines-colors">
    <div class="color-blue"></div>
    <div class="color-red"></div>
    <div class="color-yellow"></div>
</div>

<header class="header">
    <div class="container">
        <div class="row">
            <div class="col-md-12 position-relative">
                {% if user.is_staff %}
                <div class="position-absolute top-0 end-0">
                    <div class="d-flex align-items-center text-white">
                        <span class="me-2">Welcome Admin</span>
                        <a href="/admin/logout/" class="btn btn-sm btn-outline-light">Logout</a>
                    </div>
                </div>
                {% endif %}
                <h1 class="text-center">Tagalog-Teduray Translator</h1>
                <p class="text-center mb-0">Preserving Mindanao's Cultural Heritage</p>
            </div>
        </div>
    </div>
</header>

<div class="container mb-4">
    <div class="row">
        <div class="col-md-12">
            <ul class="nav nav-pills nav-fill">
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'translation:index' %}">Translator</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'translation:dictionary' %}">Dictionary</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'translation:phrases' %}">Phrases</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'translation:metrics_dashboard' %}">Metrics Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="{% url 'translation:add_translation' %}">Add Translation</a>
                </li>
            </ul>
        </div>
    </div>
</div>
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <h1 class="mb-4"><i class="bi bi-plus-circle me-2"></i>{% trans 'Add Translation' %}</h1>

            {% if messages %}
            <div class="messages mb-4">
                {% for message in messages %}
                <div class="alert {% if message.tags %}{% if message.tags == 'success' %}alert-success{% elif message.tags == 'error' %}alert-danger{% else %}alert-info{% endif %}{% else %}alert-info{% endif %}">
                    {{ message }}
                </div>
                {% endfor %}
            </div>
            {% endif %}

    <div class="translation-form">
        <form method="post" action="">
            {% csrf_token %}

            <div class="form-row">
                <label for="source_lang">{% trans 'Source Language:' %}</label>
                <select name="source_lang" id="source_lang">
                    {% for language in languages %}
                    <option value="{{ language.code }}" {% if language.code == 'tgl' %}selected{% endif %}>{{ language.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="form-row">
                <label for="source_text">{% trans 'Source Text:' %}</label>
                <textarea name="source_text" id="source_text" required></textarea>
                <p class="help-text">{% trans 'Enter the text to be translated.' %}</p>
            </div>

            <div class="form-row">
                <label for="target_lang">{% trans 'Target Language:' %}</label>
                <select name="target_lang" id="target_lang">
                    {% for language in languages %}
                    <option value="{{ language.code }}" {% if language.code == 'ted' %}selected{% endif %}>{{ language.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="form-row">
                <label for="target_text">{% trans 'Target Text:' %}</label>
                <textarea name="target_text" id="target_text" required></textarea>
                <p class="help-text">{% trans 'Enter the translation.' %}</p>
            </div>

            <div class="form-row">
                <label for="context">{% trans 'Context:' %}</label>
                <input type="text" name="context" id="context">
                <p class="help-text">{% trans 'Optional context information (e.g., Bible verse, conversation).' %}</p>
            </div>

            <div class="form-row">
                <label for="part_of_speech">{% trans 'Part of Speech:' %}</label>
                <select name="part_of_speech" id="part_of_speech">
                    <option value="word">{% trans 'Word' %}</option>
                    <option value="phrase">{% trans 'Phrase' %}</option>
                    <option value="noun">{% trans 'Noun' %}</option>
                    <option value="verb">{% trans 'Verb' %}</option>
                    <option value="adjective">{% trans 'Adjective' %}</option>
                    <option value="adverb">{% trans 'Adverb' %}</option>
                    <option value="pronoun">{% trans 'Pronoun' %}</option>
                    <option value="preposition">{% trans 'Preposition' %}</option>
                    <option value="conjunction">{% trans 'Conjunction' %}</option>
                    <option value="interjection">{% trans 'Interjection' %}</option>
                </select>
            </div>

            <!-- Confidence is set automatically -->
            <input type="hidden" name="confidence" id="confidence" value="0.9">

            <div class="form-row">
                <input type="checkbox" name="extract_words" id="extract_words" checked>
                <label for="extract_words" style="display: inline;">{% trans 'Extract individual words and phrases' %}</label>
                <p class="help-text">{% trans 'Attempt to extract individual word translations from phrases.' %}</p>
            </div>

            <div class="form-row">
                <input type="checkbox" name="add_reverse" id="add_reverse" checked>
                <label for="add_reverse" style="display: inline;">{% trans 'Add reverse translation' %}</label>
                <p class="help-text">{% trans 'Also add the translation in the reverse direction.' %}</p>
            </div>

            <div class="submit-row">
                <input type="submit" value="{% trans 'Add Translation' %}" class="btn btn-primary rounded-pill">
            </div>
        </form>
    </div>
        </div>
    </div>
</div>

<!-- Confidence is now handled automatically -->

<footer class="footer">
    <div class="container">
        <!-- About and Contact sections hidden as requested -->

        {% if user.is_staff %}
        <div class="row mt-3 mb-3">
            <div class="col-md-12">
                <h5 class="mb-3"><i class="bi bi-gear-fill me-2"></i>Admin Tools</h5>
                <div class="d-flex flex-wrap gap-2">
                    <a href="{% url 'translation:metrics_dashboard' %}" class="btn btn-sm btn-light rounded-pill">
                        <i class="bi bi-graph-up me-1"></i> Metrics Dashboard
                    </a>
                    <a href="{% url 'translation:add_translation' %}" class="btn btn-sm btn-light rounded-pill">
                        <i class="bi bi-plus-circle me-1"></i> Add Translation
                    </a>
                    <a href="/admin/" class="btn btn-sm btn-light rounded-pill">
                        <i class="bi bi-shield-lock me-1"></i> Django Admin
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="text-center mt-3">
            <div class="tribal-pattern mb-3" style="opacity: 0.5;"></div>
            <p class="mb-0">&copy; 2025 Tagalog-Teduray Translator. All rights reserved.</p>
        </div>
    </div>
</footer>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
