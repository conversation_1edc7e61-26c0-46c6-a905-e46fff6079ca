// Force light theme
(function() {
    // Function to set light theme
    function setLightTheme() {
        // Set the theme attribute on html element
        document.documentElement.setAttribute('data-theme', 'light');
        
        // Force light theme in localStorage
        localStorage.setItem('theme', 'light');
        
        // Apply light theme styles directly
        document.documentElement.style.setProperty('--body-bg', '#F5F0E1');
        document.documentElement.style.setProperty('--body-fg', '#333');
        document.documentElement.style.setProperty('--link-fg', '#006837');
        document.documentElement.style.setProperty('--link-hover-color', '#004020');
        
        console.log('Light theme forced');
    }

    // Apply light theme immediately
    setLightTheme();
    
    // Also apply when DOM is loaded to ensure it takes effect
    document.addEventListener('DOMContentLoaded', setLightTheme);
    
    // Apply again after a short delay to override any other scripts
    setTimeout(setLightTheme, 100);
    setTimeout(setLightTheme, 500);
    setTimeout(setLightTheme, 1000);
})();
