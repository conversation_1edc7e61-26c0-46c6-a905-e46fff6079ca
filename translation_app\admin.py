from django.contrib import admin
from django.utils import timezone
from django.utils.html import format_html
from django.urls import reverse
import json
import threading
import logging

# Import directly from models.py
from translation_app.models import Language, Word, Translation, ComprehensiveTranslation
from translation_app.models import Related<PERSON>ordForm, TranslationExample, TranslationFeedback
from translation_app.models import TranslationVersion, TranslationSuggestion

# Try to import AITranslationReview
try:
    from translation_app.models import AITranslationReview
except ImportError:
    AITranslationReview = None

# Import AI settings and logging
try:
    # Try direct import first
    from translation_app.ai_settings import AITranslationSettings
    from translation_app.ai_logging import AITranslationLog
    AI_FEATURES_AVAILABLE = True
except ImportError:
    AI_FEATURES_AVAILABLE = False

@admin.register(Language)
class LanguageAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'description')
    search_fields = ('name', 'code')

@admin.register(Word)
class WordAdmin(admin.ModelAdmin):
    list_display = ('text', 'language', 'part_of_speech', 'created_at')
    list_filter = ('language', 'part_of_speech')
    search_fields = ('text', 'pronunciation', 'etymology')

@admin.register(Translation)
class TranslationAdmin(admin.ModelAdmin):
    list_display = ('source_word', 'target_word', 'created_at')
    list_filter = ('source_word__language', 'target_word__language')
    search_fields = ('source_word__text', 'target_word__text', 'notes')

class RelatedWordFormInline(admin.TabularInline):
    model = RelatedWordForm
    extra = 1

class TranslationExampleInline(admin.TabularInline):
    model = TranslationExample
    extra = 1

class TranslationVersionInline(admin.TabularInline):
    model = TranslationVersion
    extra = 0
    readonly_fields = ('created_at', 'created_by', 'is_active')
    fields = ('translation', 'confidence_score', 'created_by', 'created_at', 'is_active', 'notes')
    max_num = 5
    can_delete = False

@admin.register(ComprehensiveTranslation)
class ComprehensiveTranslationAdmin(admin.ModelAdmin):
    list_display = ('base_word', 'translation', 'source_language', 'target_language', 'part_of_speech', 'updated_at')
    list_filter = ('source_language', 'target_language', 'part_of_speech')
    search_fields = ('base_word', 'translation', 'notes', 'cultural_notes')
    inlines = [RelatedWordFormInline, TranslationExampleInline, TranslationVersionInline]
    list_per_page = 50  # Limit number of items per page for faster loading
    actions = ['update_attention_for_selected']

    # Add select_related to improve query performance
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'source_language', 'target_language'
        )

    fieldsets = (
        (None, {
            'fields': ('base_word', 'translation', 'source_language', 'target_language', 'part_of_speech')
        }),
        ('Additional Information', {
            'fields': ('notes', 'cultural_notes'),
            'classes': ('collapse',),
        }),
    )

    # Optimize save to avoid triggering unnecessary signals
    def save_model(self, request, obj, form, change):
        # Set a flag to indicate this is an admin save
        obj._skip_signal = True
        super().save_model(request, obj, form, change)

    def update_attention_for_selected(self, request, queryset):
        """Update attention mechanism for selected translations"""
        try:
            # Run the script in a background thread to avoid blocking the admin interface
            def run_update_attention_for_selected(translation_ids):
                try:
                    from django.core.management import call_command
                    from translation_app.services import get_translation_service
                    from translation_app.attention_mechanism import AttentionMechanism

                    # Get the attention mechanism
                    attention = AttentionMechanism()

                    # Process each translation
                    for translation_id in translation_ids:
                        try:
                            # Get the translation
                            translation = ComprehensiveTranslation.objects.get(id=translation_id)

                            # Compute attention weights
                            attention_data = attention.compute_attention(
                                translation.base_word,
                                translation.translation,
                                translation.source_language.code,
                                translation.target_language.code
                            )

                            # Store attention data in the database
                            from translation_app.models import AttentionData

                            # Find or create attention data
                            attention_obj, created = AttentionData.objects.get_or_create(
                                comprehensive_translation=translation,
                                defaults={
                                    'attention_weights': json.dumps(attention_data['attention_weights']),
                                    'confidence': 0.9,
                                    'bleu_score': 0.85,
                                    'notes': 'Created from admin action'
                                }
                            )

                            # If not created, update the attention data
                            if not created:
                                attention_obj.attention_weights = json.dumps(attention_data['attention_weights'])
                                attention_obj.updated_at = timezone.now()
                                attention_obj.save()

                            logger.info(f"Updated attention data for translation {translation_id}")
                        except Exception as e:
                            logger.error(f"Error updating attention data for translation {translation_id}: {str(e)}")

                    # Reload the translation service
                    translation_service = get_translation_service()
                    translation_service.load_translations(clear_cache=True)

                    logger.info(f"Attention mechanism update completed for {len(translation_ids)} translations")
                except Exception as e:
                    logger.error(f"Error in attention mechanism update: {str(e)}")

            # Get the IDs of the selected translations
            translation_ids = [obj.id for obj in queryset]

            # Start the thread
            thread = threading.Thread(target=run_update_attention_for_selected, args=(translation_ids,))
            thread.daemon = True
            thread.start()

            self.message_user(request, f"Attention mechanism update started for {len(translation_ids)} translations. This may take a few minutes.")
        except Exception as e:
            self.message_user(request, f"Error starting attention update: {str(e)}", level='error')

    update_attention_for_selected.short_description = "Update attention for selected translations"

@admin.register(TranslationSuggestion)
class TranslationSuggestionAdmin(admin.ModelAdmin):
    list_display = ('original_text_preview', 'suggested_translation_preview', 'status_with_actions')
    list_filter = ('status', 'source_language', 'target_language', 'created_at')
    search_fields = ('original_text', 'system_translation', 'suggested_translation', 'notes')
    readonly_fields = ('created_at',)
    actions = ['approve_suggestions', 'reject_suggestions']

    def original_text_preview(self, obj):
        return obj.original_text[:50] + '...' if len(obj.original_text) > 50 else obj.original_text
    original_text_preview.short_description = 'Original Text'

    def suggested_translation_preview(self, obj):
        return obj.suggested_translation[:50] + '...' if len(obj.suggested_translation) > 50 else obj.suggested_translation
    suggested_translation_preview.short_description = 'Suggested Translation'

    def status_with_actions(self, obj):
        if obj.status == 'pending':
            approve_url = reverse('admin:approve_suggestion', args=[obj.id])
            reject_url = reverse('admin:reject_suggestion', args=[obj.id])
            return format_html(
                '<span style="color: orange;">Pending</span> &nbsp; '
                '<a class="button" href="{}">Approve</a> &nbsp; '
                '<a class="button" href="{}">Reject</a>',
                approve_url, reject_url
            )
        elif obj.status == 'approved':
            return format_html('<span style="color: green;">Approved</span>')
        else:
            return format_html('<span style="color: red;">Rejected</span>')
    status_with_actions.short_description = 'Status & Actions'

    def get_urls(self):
        from django.urls import path
        urls = super().get_urls()
        custom_urls = [
            path(
                '<int:suggestion_id>/approve/',
                self.admin_site.admin_view(self.approve_suggestion_view),
                name='approve_suggestion',
            ),
            path(
                '<int:suggestion_id>/reject/',
                self.admin_site.admin_view(self.reject_suggestion_view),
                name='reject_suggestion',
            ),
        ]
        return custom_urls + urls

    def approve_suggestion_view(self, request, suggestion_id):
        from django.shortcuts import get_object_or_404, redirect
        from django.contrib import messages
        from translation_app.services import get_translation_service

        suggestion = get_object_or_404(TranslationSuggestion, id=suggestion_id)

        if suggestion.status == 'pending':
            try:
                # Find the existing translation if it exists
                translation = ComprehensiveTranslation.objects.filter(
                    base_word=suggestion.original_text,
                    source_language=suggestion.source_language,
                    target_language=suggestion.target_language
                ).first()

                if translation:
                    # Create a version record of the current translation
                    TranslationVersion.objects.create(
                        comprehensive_translation=translation,
                        translation=translation.translation,
                        created_by='system',
                        confidence_score=0.95,
                        is_active=False,
                        notes='Archived before applying suggestion'
                    )

                    # Update with the suggestion
                    translation.translation = suggestion.suggested_translation
                    translation.notes = (translation.notes or '') + f'\nUpdated via suggestion on {timezone.now().strftime("%Y-%m-%d")}'
                    translation.save()

                    # Create a new version for the updated translation
                    TranslationVersion.objects.create(
                        comprehensive_translation=translation,
                        translation=suggestion.suggested_translation,
                        created_by='admin',
                        confidence_score=suggestion.confidence_score,
                        is_active=True,
                        notes=f'Applied from suggestion ID: {suggestion.id}'
                    )
                else:
                    # Create a new translation
                    new_trans = ComprehensiveTranslation.objects.create(
                        base_word=suggestion.original_text,
                        translation=suggestion.suggested_translation,
                        source_language=suggestion.source_language,
                        target_language=suggestion.target_language,
                        part_of_speech='phrase' if ' ' in suggestion.original_text else 'word',
                        notes=f'Created from suggestion on {timezone.now().strftime("%Y-%m-%d")}'
                    )

                    # Create initial version
                    TranslationVersion.objects.create(
                        comprehensive_translation=new_trans,
                        translation=suggestion.suggested_translation,
                        created_by='admin',
                        confidence_score=suggestion.confidence_score,
                        is_active=True,
                        notes=f'Initial version from suggestion ID: {suggestion.id}'
                    )

                    # Create example
                    TranslationExample.objects.create(
                        comprehensive_translation=new_trans,
                        source_text=suggestion.original_text,
                        target_text=suggestion.suggested_translation
                    )

                # Mark suggestion as approved
                suggestion.status = 'approved'
                suggestion.reviewed_at = timezone.now()
                suggestion.reviewed_by = request.user
                suggestion.save()

                # Reload translation service
                try:
                    translation_service = get_translation_service()
                    translation_service.load_translations(clear_cache=True)
                except Exception as e:
                    print(f"Error reloading translation service: {str(e)}")

                messages.success(request, f"Suggestion approved and added to translations: {suggestion.original_text[:30]}")
            except Exception as e:
                messages.error(request, f"Error approving suggestion: {str(e)}")
        else:
            messages.error(request, "Suggestion is not in pending status")

        return redirect('admin:translation_app_translationsuggestion_changelist')

    def reject_suggestion_view(self, request, suggestion_id):
        from django.shortcuts import get_object_or_404, redirect
        from django.contrib import messages

        suggestion = get_object_or_404(TranslationSuggestion, id=suggestion_id)

        if suggestion.status == 'pending':
            suggestion.status = 'rejected'
            suggestion.reviewed_at = timezone.now()
            suggestion.reviewed_by = request.user
            suggestion.save()

            messages.success(request, f"Suggestion rejected: {suggestion.original_text[:30]}")
        else:
            messages.error(request, "Suggestion is not in pending status")

        return redirect('admin:translation_app_translationsuggestion_changelist')

    def approve_suggestions(self, request, queryset):
        from translation_app.services import get_translation_service

        count = 0
        for suggestion in queryset.filter(status='pending'):
            # Find the existing translation if it exists
            translation = ComprehensiveTranslation.objects.filter(
                base_word=suggestion.original_text,
                source_language=suggestion.source_language,
                target_language=suggestion.target_language
            ).first()

            if translation:
                # Create a version record of the current translation
                TranslationVersion.objects.create(
                    comprehensive_translation=translation,
                    translation=translation.translation,
                    created_by='system',
                    confidence_score=0.95,
                    is_active=False,
                    notes='Archived before applying suggestion'
                )

                # Update with the suggestion
                translation.translation = suggestion.suggested_translation
                translation.notes = (translation.notes or '') + f'\nUpdated via suggestion on {timezone.now().strftime("%Y-%m-%d")}'
                translation.save()

                # Create a new version for the updated translation
                TranslationVersion.objects.create(
                    comprehensive_translation=translation,
                    translation=suggestion.suggested_translation,
                    created_by='admin',
                    confidence_score=suggestion.confidence_score,
                    is_active=True,
                    notes=f'Applied from suggestion ID: {suggestion.id}'
                )
            else:
                # Create a new translation
                new_trans = ComprehensiveTranslation.objects.create(
                    base_word=suggestion.original_text,
                    translation=suggestion.suggested_translation,
                    source_language=suggestion.source_language,
                    target_language=suggestion.target_language,
                    part_of_speech='phrase' if ' ' in suggestion.original_text else 'word',
                    notes=f'Created from suggestion on {timezone.now().strftime("%Y-%m-%d")}'
                )

                # Create initial version
                TranslationVersion.objects.create(
                    comprehensive_translation=new_trans,
                    translation=suggestion.suggested_translation,
                    created_by='admin',
                    confidence_score=suggestion.confidence_score,
                    is_active=True,
                    notes=f'Initial version from suggestion ID: {suggestion.id}'
                )

                # Create example
                TranslationExample.objects.create(
                    comprehensive_translation=new_trans,
                    source_text=suggestion.original_text,
                    target_text=suggestion.suggested_translation
                )

            # Mark suggestion as approved
            suggestion.status = 'approved'
            suggestion.reviewed_at = timezone.now()
            suggestion.reviewed_by = request.user
            suggestion.save()
            count += 1

        # Schedule a delayed reload of the translation service and update static file
        try:
            from threading import Timer
            from translation_app.utils.static_file_updater import update_static_translations_file

            def delayed_reload():
                try:
                    # First reload the translation service
                    translation_service = get_translation_service()
                    translation_service.load_translations(clear_cache=True)
                    print("Translation service reloaded successfully in background")

                    # Then update the static file
                    update_static_translations_file(background=False)
                    print("Static translations file updated successfully")
                except Exception as e:
                    print(f"Error in delayed reload: {str(e)}")

            # Reload after 1 second
            Timer(1.0, delayed_reload).start()
            print("Scheduled translation service reload and static file update")
        except Exception as e:
            # If threading fails, do a normal reload but with minimal work
            print(f"Threading failed, doing minimal reload: {str(e)}")
            translation_service = get_translation_service()
            translation_service.load_translations(clear_cache=False)

            # Try to update the static file directly
            try:
                from translation_app.utils.static_file_updater import update_static_translations_file
                update_static_translations_file(background=False)
                print("Static translations file updated successfully (direct)")
            except Exception as e2:
                print(f"Error updating static file: {str(e2)}")

        self.message_user(request, f"{count} suggestions were approved and applied. The translation system has been updated.")
    approve_suggestions.short_description = "Approve selected suggestions"

    def reject_suggestions(self, request, queryset):
        count = queryset.filter(status='pending').update(
            status='rejected',
            reviewed_at=timezone.now(),
            reviewed_by=request.user
        )
        self.message_user(request, f"{count} suggestions were rejected.")
    reject_suggestions.short_description = "Reject selected suggestions"

@admin.register(TranslationFeedback)
class TranslationFeedbackAdmin(admin.ModelAdmin):
    list_display = ('original_text_preview', 'suggested_translation_preview', 'rating', 'action_buttons')
    list_filter = ('rating', 'source_language', 'target_language', 'processed', 'created_at')
    search_fields = ('original_text', 'translated_text', 'suggested_translation', 'comments')
    readonly_fields = ('created_at',)
    actions = ['mark_as_processed', 'create_suggestions_from_feedback', 'approve_all_feedback']

    # Hide certain fields from the list display
    exclude = ('processed',)

    def original_text_preview(self, obj):
        return obj.original_text[:50] + '...' if len(obj.original_text) > 50 else obj.original_text
    original_text_preview.short_description = 'Original Text'

    def suggested_translation_preview(self, obj):
        if not obj.suggested_translation:
            return '(No suggestion)'
        return obj.suggested_translation[:50] + '...' if len(obj.suggested_translation) > 50 else obj.suggested_translation
    suggested_translation_preview.short_description = 'Suggested Translation'

    def action_buttons(self, obj):
        if not obj.processed:
            approve_url = reverse('admin:approve_feedback', args=[obj.id])
            reject_url = reverse('admin:reject_feedback', args=[obj.id])
            return format_html(
                '<span style="color: orange;">Pending</span> &nbsp; '
                '<a class="button" href="{}">Approve</a> &nbsp; '
                '<a class="button" href="{}">Reject</a>',
                approve_url, reject_url
            )
        return format_html('<span style="color: green;">✓ Processed</span>')
    action_buttons.short_description = 'Status & Actions'

    def get_urls(self):
        from django.urls import path
        urls = super().get_urls()
        custom_urls = [
            path(
                '<int:feedback_id>/approve/',
                self.admin_site.admin_view(self.approve_feedback_view),
                name='approve_feedback',
            ),
            path(
                '<int:feedback_id>/reject/',
                self.admin_site.admin_view(self.reject_feedback_view),
                name='reject_feedback',
            ),
        ]
        return custom_urls + urls

    def approve_feedback_view(self, request, feedback_id):
        from django.shortcuts import get_object_or_404, redirect
        from django.contrib import messages

        feedback = get_object_or_404(TranslationFeedback, id=feedback_id)

        if not feedback.processed and feedback.suggested_translation:
            # Create a suggestion from the feedback
            TranslationSuggestion.objects.create(
                original_text=feedback.original_text,
                system_translation=feedback.translated_text,
                suggested_translation=feedback.suggested_translation,
                source_language=feedback.source_language,
                target_language=feedback.target_language,
                confidence_score=0.8,  # Default confidence for user feedback
                notes=f'Created from feedback ID: {feedback.id}. User comments: {feedback.comments or "None"}',
                user=feedback.user
            )

            # Mark feedback as processed
            feedback.processed = True
            feedback.save()

            messages.success(request, f"Feedback approved and suggestion created for: {feedback.original_text[:30]}")
        else:
            messages.error(request, "Feedback already processed or has no suggestion")

        return redirect('admin:translation_app_translationfeedback_changelist')

    def reject_feedback_view(self, request, feedback_id):
        from django.shortcuts import get_object_or_404, redirect
        from django.contrib import messages

        feedback = get_object_or_404(TranslationFeedback, id=feedback_id)

        if not feedback.processed:
            # Mark feedback as processed but don't create a suggestion
            feedback.processed = True
            feedback.save()

            messages.success(request, f"Feedback rejected for: {feedback.original_text[:30]}")
        else:
            messages.error(request, "Feedback already processed")

        return redirect('admin:translation_app_translationfeedback_changelist')

    def mark_as_processed(self, request, queryset):
        count = queryset.update(processed=True)
        self.message_user(request, f"{count} feedback items marked as processed.")
    mark_as_processed.short_description = "Mark selected feedback as processed"

    def create_suggestions_from_feedback(self, request, queryset):
        count = 0
        for feedback in queryset.filter(processed=False, suggested_translation__isnull=False):
            # Create a suggestion from the feedback
            TranslationSuggestion.objects.create(
                original_text=feedback.original_text,
                system_translation=feedback.translated_text,
                suggested_translation=feedback.suggested_translation,
                source_language=feedback.source_language,
                target_language=feedback.target_language,
                confidence_score=0.8,  # Default confidence for user feedback
                notes=f'Created from feedback ID: {feedback.id}. User comments: {feedback.comments or "None"}',
                user=feedback.user
            )

            # Mark feedback as processed
            feedback.processed = True
            feedback.save()
            count += 1

        self.message_user(request, f"{count} suggestions created from feedback.")
    create_suggestions_from_feedback.short_description = "Create suggestions from selected feedback"

    def approve_all_feedback(self, request, queryset):
        """Approve all selected feedback items at once"""
        count = 0
        for feedback in queryset.filter(processed=False, suggested_translation__isnull=False):
            # Create a suggestion from the feedback
            TranslationSuggestion.objects.create(
                original_text=feedback.original_text,
                system_translation=feedback.translated_text,
                suggested_translation=feedback.suggested_translation,
                source_language=feedback.source_language,
                target_language=feedback.target_language,
                status='approved',  # Automatically approve
                confidence_score=0.8,  # Default confidence for user feedback
                notes=f'Auto-approved from feedback ID: {feedback.id}. User comments: {feedback.comments or "None"}',
                user=feedback.user,
                reviewed_at=timezone.now()
            )

            # Mark feedback as processed
            feedback.processed = True
            feedback.save()
            count += 1

        self.message_user(request, f"{count} feedback items approved and added to translations.")
    approve_all_feedback.short_description = "Approve all selected feedback"

# Completely hide AI-related models from the admin
# We won't register them at all to ensure they don't appear in the admin dashboard
# If you need to access these models, you'll need to use the Django shell or direct database access

# The following models are hidden:
# - AITranslationReview
# - AITranslationSettings
# - AITranslationLog

# Add a direct link to the translation management page
from django.utils.html import format_html
from django.urls import reverse

class TranslationManagementLink(admin.ModelAdmin):
    def get_urls(self):
        from django.urls import path
        from django.shortcuts import redirect

        urls = super().get_urls()
        custom_urls = [
            path('translation-management/',
                 self.admin_site.admin_view(lambda request: redirect('translation:translation_management')),
                 name='translation_management'),
        ]
        return custom_urls + urls

# Register a dummy model to add the link to the admin sidebar
admin.site.register_model = lambda model, admin_class=None, **options: None  # Dummy function
admin.site.register_model(TranslationManagementLink)

# Create a custom admin dashboard with translation update buttons
from django.contrib.admin.sites import AdminSite
from django.http import HttpResponseRedirect
from django.contrib import messages
from django.urls import path
import time

logger = logging.getLogger(__name__)

# Add register_view method to AdminSite
def register_view(self, route, view_func=None, name=None):
    """
    Register a view with the site.

    route: the path to the view
    view_func: the view function
    name: the name of the view (for reverse lookup)
    """
    if view_func and not name:
        name = view_func.__name__

    def wrapper(view_func):
        self._registry_views.append((route, view_func, name))
        return view_func

    if view_func:
        return wrapper(view_func)
    return wrapper

# Add _registry_views attribute and register_view method to AdminSite
if not hasattr(admin.site, '_registry_views'):
    admin.site._registry_views = []
    admin.site.register_view = register_view.__get__(admin.site, admin.site.__class__)

# Monkey patch get_urls to include custom views
original_get_urls = admin.site.__class__.get_urls
def custom_get_urls(self):
    urls = original_get_urls(self)
    from django.urls import path
    custom_urls = [
        path(route, self.admin_view(view_func), name=name)
        for route, view_func, name in getattr(self, '_registry_views', [])
    ]
    return custom_urls + urls

admin.site.__class__.get_urls = custom_get_urls

# Create a translation dashboard view
@admin.site.register_view('translation-dashboard/')
def translation_dashboard(request):
    """Admin view for the translation dashboard"""
    from django.shortcuts import render

    context = {
        'title': 'Translation Dashboard',
        'site_title': admin.site.site_title,
        'site_header': admin.site.site_header,
        'has_permission': request.user.is_staff,
    }

    return render(request, 'admin/translation_dashboard.html', context)

# Add custom admin views for translation updates
def update_attention(request):
    """Run a script to enhance translations using the attention mechanism"""
    if request.method == 'POST':
        try:
            # Run the script in a background thread to avoid blocking the admin interface
            def run_update_attention():
                try:
                    from django.core.management import call_command
                    from translation_app.services import get_translation_service

                    # First, run the optimize_system command with attention focus
                    logger.info("Starting attention mechanism update...")
                    call_command('optimize_system', attention_focus=True)

                    # Then reload the translation service
                    translation_service = get_translation_service()
                    translation_service.load_translations(clear_cache=True)

                    logger.info("Attention mechanism update completed successfully")
                except Exception as e:
                    logger.error(f"Error in attention mechanism update: {str(e)}")

            # Start the thread
            thread = threading.Thread(target=run_update_attention)
            thread.daemon = True
            thread.start()

            messages.success(request, "Attention mechanism update started in the background. This may take a few minutes.")
        except Exception as e:
            messages.error(request, f"Error starting attention update: {str(e)}")

    return HttpResponseRedirect('/admin/')

def update_static(request):
    """Update static translation files"""
    if request.method == 'POST':
        try:
            # Run the script in a background thread to avoid blocking the admin interface
            def run_update_static():
                try:
                    from django.core.management import call_command

                    # Run the update_static_translations command
                    logger.info("Starting static translations update...")
                    call_command('update_static_translations')

                    # Also create a reduced version for mobile/low-bandwidth
                    call_command('update_static_translations', reduced=True)

                    logger.info("Static translations update completed successfully")
                except Exception as e:
                    logger.error(f"Error in static translations update: {str(e)}")

            # Start the thread
            thread = threading.Thread(target=run_update_static)
            thread.daemon = True
            thread.start()

            messages.success(request, "Static translations update started in the background. This may take a few minutes.")
        except Exception as e:
            messages.error(request, f"Error starting static update: {str(e)}")

    return HttpResponseRedirect('/admin/')

def update_feedback(request):
    """Process all pending translation feedback"""
    if request.method == 'POST':
        try:
            # Run the script in a background thread to avoid blocking the admin interface
            def run_update_feedback():
                try:
                    from django.core.management import call_command
                    from translation_app.models import TranslationFeedback

                    # Get count of pending feedback before processing
                    pending_count = TranslationFeedback.objects.filter(processed=False).count()

                    # Run the process_feedback command
                    logger.info(f"Starting feedback processing for {pending_count} pending items...")
                    call_command('process_feedback')

                    # Get count after processing
                    processed_count = pending_count - TranslationFeedback.objects.filter(processed=False).count()

                    logger.info(f"Feedback processing completed. Processed {processed_count} items.")
                except Exception as e:
                    logger.error(f"Error in feedback processing: {str(e)}")

            # Start the thread
            thread = threading.Thread(target=run_update_feedback)
            thread.daemon = True
            thread.start()

            messages.success(request, "Translation feedback processing started in the background. This may take a few minutes.")
        except Exception as e:
            messages.error(request, f"Error starting feedback processing: {str(e)}")

    return HttpResponseRedirect('/admin/')

def update_translations(request):
    """Update the translation system with all processed translations"""
    if request.method == 'POST':
        try:
            # Run the script in a background thread to avoid blocking the admin interface
            def run_update_translations():
                try:
                    from django.core.management import call_command
                    from translation_app.services import get_translation_service

                    # First, run the optimize_system command
                    logger.info("Starting translation system update...")
                    call_command('optimize_system', update_metrics=True, load_translations=True)

                    # Then reload the translation service
                    translation_service = get_translation_service()
                    translation_service.load_translations(clear_cache=True)

                    # Finally, update the static files
                    time.sleep(2)  # Small delay to ensure database operations complete
                    call_command('update_static_translations')

                    logger.info("Translation system update completed successfully")
                except Exception as e:
                    logger.error(f"Error in translation system update: {str(e)}")

            # Start the thread
            thread = threading.Thread(target=run_update_translations)
            thread.daemon = True
            thread.start()

            messages.success(request, "Translation system update started in the background. This may take a few minutes.")
        except Exception as e:
            messages.error(request, f"Error starting translation system update: {str(e)}")

    return HttpResponseRedirect('/admin/')
