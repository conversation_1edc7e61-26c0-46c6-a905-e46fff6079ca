"""
Management command to update only the latest translations in static files.
This avoids database locks by only processing recent changes.
"""

import os
import json
import logging
from datetime import timedelta
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.conf import settings

from translation_app.models import (
    ComprehensiveTranslation,
    TranslationFeedback,
    Language
)

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Update only the latest translations in static files'

    def add_arguments(self, parser):
        parser.add_argument(
            '--hours',
            type=int,
            default=24,
            help='Number of hours to look back for recent translations',
        )
        parser.add_argument(
            '--limit',
            type=int,
            default=100,
            help='Maximum number of translations to process',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force update even if no recent translations are found',
        )

    def handle(self, *args, **options):
        hours = options.get('hours', 24)
        limit = options.get('limit', 100)
        force = options.get('force', False)

        self.stdout.write(self.style.SUCCESS(f'Updating translations from the last {hours} hours...'))

        # Get recent translations
        recent_time = timezone.now() - timedelta(hours=hours)

        # Get recently updated translations
        recent_translations = ComprehensiveTranslation.objects.filter(
            updated_at__gte=recent_time
        ).order_by('-updated_at')[:limit]

        # Get recent feedback that has been processed
        recent_feedback = TranslationFeedback.objects.filter(
            created_at__gte=recent_time,
            processed=True
        ).order_by('-created_at')[:limit]

        # Count the translations
        translation_count = recent_translations.count()
        feedback_count = recent_feedback.count()

        if translation_count == 0 and feedback_count == 0 and not force:
            self.stdout.write(self.style.WARNING('No recent translations found. Use --force to update anyway.'))
            return

        self.stdout.write(f'Found {translation_count} recent translations and {feedback_count} processed feedback items')

        # Update static files with only the recent translations
        self.update_static_files(recent_translations, recent_feedback)

        self.stdout.write(self.style.SUCCESS('Latest translations updated successfully!'))

    def update_static_files(self, recent_translations, recent_feedback):
        """Update static files with only the recent translations"""
        # Get all language pairs
        language_pairs = []

        # Add language pairs from recent translations
        for translation in recent_translations:
            pair = (translation.source_language.code, translation.target_language.code)
            if pair not in language_pairs:
                language_pairs.append(pair)

        # Add language pairs from recent feedback
        for feedback in recent_feedback:
            pair = (feedback.source_language.code, feedback.target_language.code)
            if pair not in language_pairs:
                language_pairs.append(pair)

        self.stdout.write(f'Updating {len(language_pairs)} language pairs')

        # Process each language pair
        for source_code, target_code in language_pairs:
            self.update_language_pair(source_code, target_code, recent_translations, recent_feedback)

    def update_language_pair(self, source_code, target_code, recent_translations, recent_feedback):
        """Update static files for a specific language pair"""
        self.stdout.write(f'Updating {source_code} to {target_code}')

        # Create the direction key
        direction = f"{source_code}_to_{target_code}"

        # Get the static file path
        static_dir = os.path.join(settings.BASE_DIR, 'static', 'translations')
        os.makedirs(static_dir, exist_ok=True)

        file_path = os.path.join(static_dir, f"{direction}.json")

        # Load existing translations if the file exists
        existing_translations = {}
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    existing_translations = json.load(f)
            except Exception as e:
                self.stderr.write(f'Error loading existing translations: {str(e)}')

        # Filter recent translations for this language pair
        pair_translations = recent_translations.filter(
            source_language__code=source_code,
            target_language__code=target_code
        )

        # Filter recent feedback for this language pair
        pair_feedback = recent_feedback.filter(
            source_language__code=source_code,
            target_language__code=target_code
        )

        # Update translations from recent translations
        updated_count = 0
        for translation in pair_translations:
            # Determine if this is a phrase or word
            is_phrase = ' ' in translation.base_word

            # Create the key
            key = f"phrase:{translation.base_word.lower()}" if is_phrase else translation.base_word.lower()

            # Update or add the translation
            existing_translations[key] = {
                'translation': translation.translation,
                'part_of_speech': translation.part_of_speech or ('phrase' if is_phrase else 'word'),
                'notes': translation.notes or '',
                'cultural_notes': translation.cultural_notes or '',
                'examples': [],
                'priority': True,
                'confidence': 0.99,
                'source': 'database',
                'version_id': None
            }
            updated_count += 1

        # Update translations from recent feedback
        for feedback in pair_feedback:
            if not feedback.suggested_translation:
                continue

            # Determine if this is a phrase or word
            is_phrase = ' ' in feedback.original_text

            # Create the key
            key = f"phrase:{feedback.original_text.lower()}" if is_phrase else feedback.original_text.lower()

            # Update or add the translation
            existing_translations[key] = {
                'translation': feedback.suggested_translation,
                'part_of_speech': 'phrase' if is_phrase else 'word',
                'notes': 'Added from user feedback',
                'cultural_notes': '',
                'examples': [],
                'priority': True,
                'confidence': 0.95,
                'source': 'feedback',
                'version_id': None
            }
            updated_count += 1

        # Save the updated translations
        if updated_count > 0:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(existing_translations, f, ensure_ascii=False, indent=2)
                self.stdout.write(f'Updated {updated_count} translations for {source_code} to {target_code}')
            except Exception as e:
                self.stderr.write(f'Error saving translations: {str(e)}')
        else:
            self.stdout.write(f'No updates needed for {source_code} to {target_code}')
