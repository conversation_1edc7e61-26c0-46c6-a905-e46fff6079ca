{% extends "admin/base.html" %}
{% load i18n static %}

{% block extrahead %}
<style>
/* Hide the skip to content link completely */
.skip-to-content-link {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    width: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    z-index: -9999 !important;
    pointer-events: none !important;
}
</style>
<script src="{% static 'js/force_light.js' %}"></script>
{% endblock %}

{% block title %}{% if subtitle %}{{ subtitle }} | {% endif %}{{ title }} | Teduray-Tagalog Translator Admin{% endblock %}

{% block extrastyle %}
{{ block.super }}
<link rel="stylesheet" href="{% static 'css/force_light.css' %}">
{% endblock %}

{% block branding %}
<h1 id="site-name"><a href="{% url 'admin:index' %}">Teduray-Tagalog Translator</a></h1>
{% endblock %}

{% block userlinks %}
  {% if site_url %}
    <a href="{{ site_url }}">View site</a> /
  {% endif %}
  {% if user.is_active and user.is_staff %}
    {% url 'django-admindocs-docroot' as docsroot %}
    {% if docsroot %}
      <a href="{{ docsroot }}">Documentation</a> /
    {% endif %}
  {% endif %}
  {% if user.has_usable_password %}
    <a href="{% url 'admin:password_change' %}">Change password</a> /
  {% endif %}
  <a href="{% url 'admin:logout' %}">Log out</a>
{% endblock %}

{% block nav-global %}{% endblock %}
