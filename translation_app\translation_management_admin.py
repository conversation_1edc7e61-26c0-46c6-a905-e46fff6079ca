"""
Admin integration for translation management.
"""

from django.contrib import admin
from django.urls import path
from django.shortcuts import redirect

# Add a custom admin site action
def add_translation_management_link():
    """
    Add a translation management link to the admin site.
    """
    # Add a custom URL to the admin site
    admin.site.get_urls = admin.site.get_urls

    # Add a menu item to the admin menu
    admin.site.each_context = lambda request: {
        **admin.site.__class__.each_context(admin.site, request),
        'translation_management_url': '/translation-management/',
    }

# Add the translation management link to the admin site
add_translation_management_link()
