"""
<PERSON><PERSON><PERSON> to update the static translations file with all translations from the database.
This script should be run manually when you want to update the static files.

Usage:
    python update_static_translations.py [--reduced] [--max-size SIZE]

Options:
    --reduced       Create a reduced version with only essential translations
    --max-size SIZE Maximum size of the static file in bytes (default: 5MB)
"""

import os
import sys
import subprocess
import time
import logging
import argparse
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('update_static_translations.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('update_static_script')

def update_static_file(reduced=False, max_size=5*1024*1024):
    """
    Update the static translations file.
    
    Args:
        reduced (bool): Whether to create a reduced version with only essential translations
        max_size (int): Maximum size of the static file in bytes
    
    Returns:
        bool: True if successful, False otherwise
    """
    start_time = time.time()
    logger.info(f"Starting static file update at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Build the command
        cmd = [sys.executable, 'manage.py', 'update_static_translations']
        
        # Add flags
        if reduced:
            cmd.append('--reduced')
            logger.info("Creating reduced version with only essential translations")
        
        cmd.extend(['--max-size', str(max_size)])
        logger.info(f"Maximum static file size: {max_size} bytes")
        
        # Run the Django management command
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True
        )
        
        # Log the output
        if result.stdout:
            logger.info(f"Command output:\n{result.stdout}")
        
        # Log any errors
        if result.stderr:
            logger.error(f"Command errors:\n{result.stderr}")
        
        # Check return code
        if result.returncode == 0:
            elapsed_time = time.time() - start_time
            logger.info(f"Static file update completed successfully in {elapsed_time:.2f} seconds")
            
            # Check the file size
            static_file_path = os.path.join('static', 'translation_app', 'js', 'all_translations.js')
            if os.path.exists(static_file_path):
                file_size = os.path.getsize(static_file_path)
                logger.info(f"Static file size: {file_size} bytes")
                
                if file_size > max_size and not reduced:
                    logger.warning(
                        f"Static file is larger than the maximum allowed size ({max_size} bytes). "
                        f"Consider using the --reduced option to create a smaller file."
                    )
            
            return True
        else:
            logger.error(f"Static file update failed with return code {result.returncode}")
            return False
    
    except Exception as e:
        logger.error(f"Error updating static file: {str(e)}")
        return False

if __name__ == '__main__':
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Update the static translations file')
    parser.add_argument('--reduced', action='store_true', help='Create a reduced version with only essential translations')
    parser.add_argument('--max-size', type=int, default=5*1024*1024, help='Maximum size of the static file in bytes')
    
    args = parser.parse_args()
    
    # Run the update
    success = update_static_file(reduced=args.reduced, max_size=args.max_size)
    
    if success:
        logger.info("Static file update completed successfully")
        sys.exit(0)
    else:
        logger.error("Static file update failed")
        sys.exit(1)
