#!/usr/bin/env python
"""
Script to enable AI translation features (Hugging Face and attention mechanism)
and ensure database translations are properly used.

This script:
1. Enables Hugging Face integration
2. Enables attention mechanism
3. Clears all caches to ensure fresh data
4. Forces a complete reload of translations from the database
"""

import os
import sys
import logging
import django
from django.utils import timezone
from django.core.cache import cache

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enable_ai_translation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('enable_ai_translation')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Import Django models and services
from django.conf import settings
from translation_app.models import (
    Language,
    ComprehensiveTranslation,
    TranslationVersion
)
from translation_app.services import get_translation_service

def is_pythonanywhere():
    """Check if running on PythonAnywhere"""
    return 'PYTHONANYWHERE_DOMAIN' in os.environ or os.path.exists('/home/<USER>')

def enable_ai_translation():
    """
    Enable AI translation features and ensure database translations are properly used.
    """
    logger.info("Starting to enable AI translation features")
    
    try:
        # 1. Enable Hugging Face and attention mechanism in settings
        logger.info("Enabling Hugging Face integration and attention mechanism")
        setattr(settings, 'HUGGINGFACE_ENABLED', True)
        setattr(settings, 'ATTENTION_MECHANISM_ENABLED', True)
        
        # 2. Clear all caches
        logger.info("Clearing all caches")
        cache.clear()
        
        # 3. Get the translation service
        logger.info("Getting translation service")
        translation_service = get_translation_service(skip_loading_for_admin=False)
        
        # 4. Force a reload of translations
        logger.info("Forcing a reload of translations")
        translation_service.reload_translations()
        
        # 5. Test a few translations
        logger.info("Testing translations")
        test_translations = [
            ("Yumuko ka", "tgl", "ted"),
            ("kumusta", "tgl", "ted"),
            ("Tumakbo si Tom", "tgl", "ted")
        ]
        
        for text, source_lang, target_lang in test_translations:
            result = translation_service.translate_text(text, source_lang, target_lang)
            logger.info(f"Translation of '{text}': {result.get('translation', 'Unknown')}")
            logger.info(f"Source: {result.get('source', 'Unknown')}")
            logger.info(f"Confidence: {result.get('confidence', 0) * 100:.0f}%")
        
        # 6. Test AI translation
        logger.info("Testing AI translation")
        for text, source_lang, target_lang in test_translations:
            result = translation_service.translate_with_ai(text, source_lang, target_lang)
            logger.info(f"AI Translation of '{text}': {result.get('translation', 'Unknown')}")
            logger.info(f"Source: {result.get('source', 'Unknown')}")
            logger.info(f"Confidence: {result.get('confidence', 0) * 100:.0f}%")
        
        return True
    except Exception as e:
        logger.error(f"Error enabling AI translation: {str(e)}")
        return False

if __name__ == "__main__":
    success = enable_ai_translation()
    if success:
        logger.info("Successfully enabled AI translation features")
    else:
        logger.error("Failed to enable AI translation features")
