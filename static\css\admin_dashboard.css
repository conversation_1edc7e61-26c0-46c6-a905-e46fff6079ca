/* Custom CSS for the translation admin dashboard */

/* Card styling */
.card {
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    padding: 15px 20px;
}

.card-body {
    padding: 20px;
    min-height: 150px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.card-title {
    margin-bottom: 0;
    font-weight: 600;
}

/* Button styling */
.btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.2s;
    margin-top: 15px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.btn-info {
    background-color: #17a2b8;
    border-color: #17a2b8;
}

/* Background colors */
.bg-success {
    background-color: #28a745 !important;
}

.bg-primary {
    background-color: #007bff !important;
}

.bg-warning {
    background-color: #ffc107 !important;
}

.bg-info {
    background-color: #17a2b8 !important;
}

/* Text colors */
.text-white {
    color: #fff !important;
}

.text-dark {
    color: #343a40 !important;
}

/* Spacing */
.mt-4 {
    margin-top: 1.5rem !important;
}

/* Grid system */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.col-12 {
    flex: 0 0 100%;
    max-width: 100%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding-right: 15px;
    padding-left: 15px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}
