2025-05-23 15:42:44,155 - optimize_script - INFO - Starting feedback processing
2025-05-23 15:42:44,155 - optimize_script - INFO - Processing pending feedback files
2025-05-23 15:42:44,668 - optimize_script - INFO - Successfully processed pending feedback
2025-05-23 15:42:44,668 - optimize_script - INFO - Running full system optimization
2025-05-23 15:42:44,668 - optimize_script - INFO - Starting optimization at 2025-05-23 15:42:44
2025-05-23 15:42:45,111 - optimize_script - INFO - Cleared Django cache to ensure fresh data
2025-05-23 15:42:45,111 - optimize_script - INFO - Skipping static file updates to prioritize database operations
2025-05-23 15:42:45,111 - optimize_script - INFO - Enabling Hugging Face integration for translations
2025-05-23 15:42:45,111 - optimize_script - INFO - Enabling attention mechanism for translations
2025-05-23 15:42:45,125 - optimize_script - INFO - Enabling BLEU score for translation quality evaluation
2025-05-23 15:42:58,183 - optimize_script - INFO - Command output:
Starting translation system optimization...
Optimizing database...
Optimizing SQLite database...
SQLite database optimized successfully
Updating metrics...
Starting metrics update...
Error updating metrics: 'charmap' codec can't encode character '\u2192' in position 25: character maps to <undefined>
Metrics updated successfully
Skipping static file update to prioritize database operations
Loading translations...
Using BLEU score for translation quality evaluation
Cache cleared
Enabling Hugging Face integration for translations
Enabling attention mechanism for translations
Enabling BLEU score for translation quality evaluation
Temporarily enabling automatic processes for translation loading
Restored automatic processes setting to: True
Translations loaded successfully
Translation system optimization completed in 12.09 seconds!

2025-05-23 15:42:58,183 - optimize_script - ERROR - Command errors:
ERROR:root:Error in update_metrics command: 'charmap' codec can't encode character '\u2192' in position 25: character maps to <undefined>

2025-05-23 15:42:58,183 - optimize_script - INFO - Forcing a complete reload of translations from the database
2025-05-23 15:43:13,431 - optimize_script - INFO - Reload output:
Cache cleared
Translations reloaded successfully

2025-05-23 15:43:13,431 - optimize_script - INFO - Successfully reloaded translations from the database
2025-05-23 15:43:13,431 - optimize_script - INFO - Optimization completed successfully in 28.76 seconds
2025-05-23 15:43:13,431 - optimize_script - INFO - System optimization completed successfully
2025-05-23 15:43:13,431 - optimize_script - INFO - All tasks completed
2025-05-23 15:43:32,811 - optimize_script - INFO - Processing all translations, feedback, and improvements
2025-05-23 15:43:32,811 - optimize_script - INFO - Starting feedback processing
2025-05-23 15:43:32,811 - optimize_script - INFO - Processing pending feedback files
2025-05-23 15:43:33,334 - optimize_script - INFO - Successfully processed pending feedback
2025-05-23 15:43:33,334 - optimize_script - INFO - Optimizing translations using attention mechanism
2025-05-23 15:43:33,334 - optimize_script - INFO - Optimizing translations using attention mechanism
2025-05-23 15:43:33,337 - optimize_script - ERROR - Error optimizing translations with attention: 'charmap' codec can't encode character '\u2192' in position 1814: character maps to <undefined>
2025-05-23 15:43:33,337 - optimize_script - WARNING - Failed to optimize translations using attention mechanism
2025-05-23 15:43:33,337 - optimize_script - INFO - Running full system optimization
2025-05-23 15:43:33,337 - optimize_script - INFO - Starting optimization at 2025-05-23 15:43:33
2025-05-23 15:43:33,792 - optimize_script - INFO - Cleared Django cache to ensure fresh data
2025-05-23 15:43:33,792 - optimize_script - INFO - Will update static files with latest translations
2025-05-23 15:43:33,792 - optimize_script - INFO - Enabling Hugging Face integration for translations
2025-05-23 15:43:33,792 - optimize_script - INFO - Enabling attention mechanism for translations
2025-05-23 15:43:33,792 - optimize_script - INFO - Enabling BLEU score for translation quality evaluation
2025-05-23 15:44:04,135 - optimize_script - INFO - Command output:
Starting translation system optimization...
Optimizing database...
Optimizing SQLite database...
SQLite database optimized successfully
Updating metrics...
Starting metrics update...
Error updating metrics: 'charmap' codec can't encode character '\u2192' in position 25: character maps to <undefined>
Metrics updated successfully
Updating static translations file...
Updating static translations file...
Successfully exported 18622 of 18622 translations to C:\Users\<USER>\translation\staticfiles\translation_app\js\all_translations.js (3886868 bytes)
Word mappings exported to C:\Users\<USER>\translation\staticfiles\translation_app\js\word_mappings.js (2332407 bytes)
Successfully updated static translations file
Log entry added to logs\translation_updates.log
Static file size: 7047204 bytes
Static translations file updated successfully
Loading translations...
Using BLEU score for translation quality evaluation
Cache cleared
Enabling Hugging Face integration for translations
Enabling attention mechanism for translations
Enabling BLEU score for translation quality evaluation
Temporarily enabling automatic processes for translation loading
Restored automatic processes setting to: True
Translations loaded successfully
Translation system optimization completed in 29.28 seconds!

2025-05-23 15:44:04,135 - optimize_script - ERROR - Command errors:
ERROR:root:Error in update_metrics command: 'charmap' codec can't encode character '\u2192' in position 25: character maps to <undefined>

2025-05-23 15:44:04,135 - optimize_script - INFO - Forcing a complete reload of translations from the database
2025-05-23 15:44:18,332 - optimize_script - INFO - Reload output:
Cache cleared
Translations reloaded successfully

2025-05-23 15:44:18,332 - optimize_script - INFO - Successfully reloaded translations from the database
2025-05-23 15:44:18,333 - optimize_script - INFO - Optimization completed successfully in 45.00 seconds
2025-05-23 15:44:18,333 - optimize_script - INFO - System optimization completed successfully
2025-05-23 15:44:18,333 - optimize_script - INFO - Updating static translations file
2025-05-23 15:44:18,334 - optimize_script - INFO - Updating static translations file
2025-05-23 15:44:29,684 - optimize_script - ERROR - Static file update errors:
2025-05-23 15:44:18,396 - update_static_script - INFO - Starting static file update at 2025-05-23 15:44:18
2025-05-23 15:44:18,396 - update_static_script - INFO - Maximum static file size: 5242880 bytes
2025-05-23 15:44:29,681 - update_static_script - INFO - Command output:
Updating static translations file...
Successfully exported 18622 of 18622 translations to C:\Users\<USER>\translation\staticfiles\translation_app\js\all_translations.js (3886868 bytes)
Word mappings exported to C:\Users\<USER>\translation\staticfiles\translation_app\js\word_mappings.js (2332407 bytes)
Successfully updated static translations file
Log entry added to logs\translation_updates.log
Static file size: 7047204 bytes
Static file is larger than the maximum allowed size (5242880 bytes). Consider using the --reduced option to create a smaller file.

2025-05-23 15:44:29,681 - update_static_script - INFO - Static file update completed successfully in 11.29 seconds
2025-05-23 15:44:29,681 - update_static_script - INFO - Static file size: 7047204 bytes
2025-05-23 15:44:29,681 - update_static_script - WARNING - Static file is larger than the maximum allowed size (5242880 bytes). Consider using the --reduced option to create a smaller file.
2025-05-23 15:44:29,681 - update_static_script - INFO - Static file update completed successfully

2025-05-23 15:44:29,684 - optimize_script - INFO - Static translations file updated successfully
2025-05-23 15:44:29,684 - optimize_script - INFO - Static translations file updated successfully
2025-05-23 15:44:29,684 - optimize_script - INFO - All processing tasks completed
2025-05-23 15:49:54,519 - optimize_script - INFO - Updating static translations file only
2025-05-23 15:49:54,519 - optimize_script - INFO - Updating static translations file
2025-05-23 15:50:04,213 - optimize_script - INFO - Static file update additional output:
2025-05-23 15:49:54,581 - update_static_script - INFO - Starting static file update at 2025-05-23 15:49:54
2025-05-23 15:49:54,581 - update_static_script - INFO - Maximum static file size: 5242880 bytes
2025-05-23 15:50:04,199 - update_static_script - INFO - Command output:
Updating static translations file...
Successfully exported 18622 of 18622 translations to C:\Users\<USER>\translation\staticfiles\translation_app\js\all_translations.js (3886868 bytes)
Word mappings exported to C:\Users\<USER>\translation\staticfiles\translation_app\js\word_mappings.js (2332407 bytes)
Successfully updated static translations file
Log entry added to logs\translation_updates.log
Static file size: 7047204 bytes
Static file is larger than the maximum allowed size (5242880 bytes). Consider using the --reduced option to create a smaller file.

2025-05-23 15:50:04,199 - update_static_script - INFO - Static file update completed successfully in 9.62 seconds
2025-05-23 15:50:04,199 - update_static_script - INFO - Static file size: 7047204 bytes
2025-05-23 15:50:04,199 - update_static_script - WARNING - Static file is larger than the maximum allowed size (5242880 bytes). Consider using the --reduced option to create a smaller file.
2025-05-23 15:50:04,199 - update_static_script - INFO - Static file update completed successfully

2025-05-23 15:50:04,213 - optimize_script - INFO - Static translations file updated successfully
2025-05-23 15:50:04,213 - optimize_script - INFO - Static translations file updated successfully
2025-05-23 15:50:21,786 - optimize_script - INFO - Starting feedback processing
2025-05-23 15:50:21,786 - optimize_script - INFO - Processing pending feedback files
2025-05-23 15:50:22,386 - optimize_script - INFO - Successfully processed pending feedback
2025-05-23 15:50:22,386 - optimize_script - INFO - Running full system optimization
2025-05-23 15:50:22,386 - optimize_script - INFO - Starting optimization at 2025-05-23 15:50:22
2025-05-23 15:50:22,852 - optimize_script - INFO - Cleared Django cache to ensure fresh data
2025-05-23 15:50:22,852 - optimize_script - INFO - Skipping static file updates to prioritize database operations
2025-05-23 15:50:22,852 - optimize_script - INFO - Enabling Hugging Face integration for translations
2025-05-23 15:50:22,852 - optimize_script - INFO - Enabling attention mechanism for translations
2025-05-23 15:50:22,852 - optimize_script - INFO - Enabling BLEU score for translation quality evaluation
2025-05-23 15:50:37,138 - optimize_script - INFO - Command output:
Starting translation system optimization...
Optimizing database...
Optimizing SQLite database...
SQLite database optimized successfully
Updating metrics...
Starting metrics update...
Error updating metrics: 'charmap' codec can't encode character '\u2192' in position 25: character maps to <undefined>
Metrics updated successfully
Skipping static file update to prioritize database operations
Loading translations...
Using BLEU score for translation quality evaluation
Cache cleared
Enabling Hugging Face integration for translations
Enabling attention mechanism for translations
Enabling BLEU score for translation quality evaluation
Temporarily enabling automatic processes for translation loading
Restored automatic processes setting to: True
Translations loaded successfully
Translation system optimization completed in 13.26 seconds!

2025-05-23 15:50:37,138 - optimize_script - INFO - Command additional output:
ERROR:root:Error in update_metrics command: 'charmap' codec can't encode character '\u2192' in position 25: character maps to <undefined>

2025-05-23 15:50:37,144 - optimize_script - INFO - Forcing a complete reload of translations from the database
2025-05-23 15:50:51,216 - optimize_script - INFO - Reload output:
Cache cleared
Translations reloaded successfully

2025-05-23 15:50:51,216 - optimize_script - INFO - Successfully reloaded translations from the database
2025-05-23 15:50:51,216 - optimize_script - INFO - Optimization completed successfully in 28.83 seconds
2025-05-23 15:50:51,216 - optimize_script - INFO - System optimization completed successfully
2025-05-23 15:50:51,220 - optimize_script - INFO - All tasks completed
2025-05-23 15:57:11,217 - optimize_script - INFO - Starting feedback processing
2025-05-23 15:57:11,217 - optimize_script - INFO - Processing pending feedback files
2025-05-23 15:57:11,870 - optimize_script - INFO - Successfully processed pending feedback
2025-05-23 15:57:11,870 - optimize_script - INFO - Running full system optimization
2025-05-23 15:57:11,870 - optimize_script - INFO - Starting optimization at 2025-05-23 15:57:11
2025-05-23 15:57:12,425 - optimize_script - INFO - Cleared Django cache to ensure fresh data
2025-05-23 15:57:12,425 - optimize_script - INFO - Skipping static file updates to prioritize database operations
2025-05-23 15:57:12,425 - optimize_script - INFO - Enabling Hugging Face integration for translations
2025-05-23 15:57:12,425 - optimize_script - INFO - Enabling attention mechanism for translations
2025-05-23 15:57:12,425 - optimize_script - INFO - Enabling BLEU score for translation quality evaluation
2025-05-23 15:57:28,242 - optimize_script - INFO - Command output:
Starting translation system optimization...
Optimizing database...
Optimizing SQLite database...
SQLite database optimized successfully
Updating metrics...
Starting metrics update...
Error updating metrics: 'charmap' codec can't encode character '\u2192' in position 25: character maps to <undefined>
Metrics updated successfully
Skipping static file update to prioritize database operations
Loading translations...
Using BLEU score for translation quality evaluation
Cache cleared
Enabling Hugging Face integration for translations
Enabling attention mechanism for translations
Enabling BLEU score for translation quality evaluation
Temporarily enabling automatic processes for translation loading
Restored automatic processes setting to: True
Translations loaded successfully
Translation system optimization completed in 14.79 seconds!

2025-05-23 15:57:28,243 - optimize_script - INFO - Command additional output:
ERROR:root:Error in update_metrics command: 'charmap' codec can't encode character '\u2192' in position 25: character maps to <undefined>

2025-05-23 15:57:28,243 - optimize_script - INFO - Forcing a complete reload of translations from the database
2025-05-23 15:57:43,009 - optimize_script - INFO - Reload output:
Cache cleared
Translations reloaded successfully

2025-05-23 15:57:43,009 - optimize_script - INFO - Successfully reloaded translations from the database
2025-05-23 15:57:43,009 - optimize_script - INFO - Optimization completed successfully in 31.14 seconds
2025-05-23 15:57:43,009 - optimize_script - INFO - System optimization completed successfully
2025-05-23 15:57:43,009 - optimize_script - INFO - All tasks completed
2025-05-23 15:58:03,693 - optimize_script - INFO - Processing all translations, feedback, and improvements
2025-05-23 15:58:03,693 - optimize_script - INFO - Starting feedback processing
2025-05-23 15:58:03,693 - optimize_script - INFO - Processing pending feedback files
2025-05-23 15:58:04,230 - optimize_script - INFO - Successfully processed pending feedback
2025-05-23 15:58:04,230 - optimize_script - INFO - Optimizing translations using attention mechanism
2025-05-23 15:58:04,230 - optimize_script - INFO - Optimizing translations using attention mechanism
2025-05-23 15:58:04,230 - optimize_script - ERROR - Error optimizing translations with attention: 'charmap' codec can't encode character '\u2192' in position 1814: character maps to <undefined>
2025-05-23 15:58:04,230 - optimize_script - WARNING - Failed to optimize translations using attention mechanism
2025-05-23 15:58:04,230 - optimize_script - INFO - Running full system optimization
2025-05-23 15:58:04,230 - optimize_script - INFO - Starting optimization at 2025-05-23 15:58:04
2025-05-23 15:58:04,680 - optimize_script - INFO - Cleared Django cache to ensure fresh data
2025-05-23 15:58:04,680 - optimize_script - INFO - Will update static files with latest translations
2025-05-23 15:58:04,680 - optimize_script - INFO - Enabling Hugging Face integration for translations
2025-05-23 15:58:04,680 - optimize_script - INFO - Enabling attention mechanism for translations
2025-05-23 15:58:04,680 - optimize_script - INFO - Enabling BLEU score for translation quality evaluation
2025-05-23 15:58:27,698 - optimize_script - INFO - Command output:
Starting translation system optimization...
Optimizing database...
Optimizing SQLite database...
SQLite database optimized successfully
Updating metrics...
Starting metrics update...
Error updating metrics: 'charmap' codec can't encode character '\u2192' in position 25: character maps to <undefined>
Metrics updated successfully
Updating static translations file...
Updating static translations file...
Successfully exported 18622 of 18622 translations to C:\Users\<USER>\translation\staticfiles\translation_app\js\all_translations.js (3886868 bytes)
Word mappings exported to C:\Users\<USER>\translation\staticfiles\translation_app\js\word_mappings.js (2332407 bytes)
Successfully updated static translations file
Log entry added to logs\translation_updates.log
Static file size: 7047204 bytes
Static translations file updated successfully
Loading translations...
Using BLEU score for translation quality evaluation
Cache cleared
Enabling Hugging Face integration for translations
Enabling attention mechanism for translations
Enabling BLEU score for translation quality evaluation
Temporarily enabling automatic processes for translation loading
Restored automatic processes setting to: True
Translations loaded successfully
Translation system optimization completed in 22.09 seconds!

2025-05-23 15:58:27,698 - optimize_script - INFO - Command additional output:
ERROR:root:Error in update_metrics command: 'charmap' codec can't encode character '\u2192' in position 25: character maps to <undefined>

2025-05-23 15:58:27,698 - optimize_script - INFO - Forcing a complete reload of translations from the database
2025-05-23 15:58:40,261 - optimize_script - INFO - Reload output:
Cache cleared
Translations reloaded successfully

2025-05-23 15:58:40,261 - optimize_script - INFO - Successfully reloaded translations from the database
2025-05-23 15:58:40,262 - optimize_script - INFO - Optimization completed successfully in 36.03 seconds
2025-05-23 15:58:40,262 - optimize_script - INFO - System optimization completed successfully
2025-05-23 15:58:40,262 - optimize_script - INFO - Updating static translations file
2025-05-23 15:58:40,262 - optimize_script - INFO - Updating static translations file
2025-05-23 15:58:50,967 - optimize_script - INFO - Static file update additional output:
2025-05-23 15:58:40,326 - update_static_script - INFO - Starting static file update at 2025-05-23 15:58:40
2025-05-23 15:58:40,328 - update_static_script - INFO - Maximum static file size: 5242880 bytes
2025-05-23 15:58:50,956 - update_static_script - INFO - Command output:
Updating static translations file...
Successfully exported 18622 of 18622 translations to C:\Users\<USER>\translation\staticfiles\translation_app\js\all_translations.js (3886868 bytes)
Word mappings exported to C:\Users\<USER>\translation\staticfiles\translation_app\js\word_mappings.js (2332407 bytes)
Successfully updated static translations file
Log entry added to logs\translation_updates.log
Static file size: 7047204 bytes
Static file is larger than the maximum allowed size (5242880 bytes). Consider using the --reduced option to create a smaller file.

2025-05-23 15:58:50,956 - update_static_script - INFO - Static file update completed successfully in 10.63 seconds
2025-05-23 15:58:50,956 - update_static_script - INFO - Static file size: 7047204 bytes
2025-05-23 15:58:50,960 - update_static_script - WARNING - Static file is larger than the maximum allowed size (5242880 bytes). Consider using the --reduced option to create a smaller file.
2025-05-23 15:58:50,960 - update_static_script - INFO - Static file update completed successfully

2025-05-23 15:58:50,967 - optimize_script - INFO - Static translations file updated successfully
2025-05-23 15:58:50,967 - optimize_script - INFO - Static translations file updated successfully
2025-05-23 15:58:50,967 - optimize_script - INFO - All processing tasks completed
2025-05-23 16:00:33,092 - optimize_script - INFO - Processing all translations, feedback, and improvements
2025-05-23 16:00:33,093 - optimize_script - INFO - Starting feedback processing
2025-05-23 16:00:33,093 - optimize_script - INFO - Processing pending feedback files
2025-05-23 16:00:33,627 - optimize_script - INFO - Successfully processed pending feedback
2025-05-23 16:00:33,627 - optimize_script - INFO - Optimizing translations using attention mechanism
2025-05-23 16:00:33,627 - optimize_script - INFO - Optimizing translations using attention mechanism
2025-05-23 16:00:33,627 - optimize_script - ERROR - Error optimizing translations with attention: 'charmap' codec can't encode character '\u2192' in position 2090: character maps to <undefined>
2025-05-23 16:00:33,627 - optimize_script - WARNING - Failed to optimize translations using attention mechanism
2025-05-23 16:00:33,627 - optimize_script - INFO - Running full system optimization
2025-05-23 16:00:33,627 - optimize_script - INFO - Starting optimization at 2025-05-23 16:00:33
2025-05-23 16:00:34,092 - optimize_script - INFO - Cleared Django cache to ensure fresh data
2025-05-23 16:00:34,092 - optimize_script - INFO - Will update static files with latest translations
2025-05-23 16:00:34,092 - optimize_script - INFO - Enabling Hugging Face integration for translations
2025-05-23 16:00:34,092 - optimize_script - INFO - Enabling attention mechanism for translations
2025-05-23 16:00:34,092 - optimize_script - INFO - Enabling BLEU score for translation quality evaluation
2025-05-23 16:00:55,762 - optimize_script - INFO - Command output:
Starting translation system optimization...
Optimizing database...
Optimizing SQLite database...
SQLite database optimized successfully
Updating metrics...
Starting metrics update...
Error updating metrics: 'charmap' codec can't encode character '\u2192' in position 25: character maps to <undefined>
Metrics updated successfully
Updating static translations file...
Updating static translations file...
Successfully exported 18622 of 18622 translations to C:\Users\<USER>\translation\staticfiles\translation_app\js\all_translations.js (3886868 bytes)
Word mappings exported to C:\Users\<USER>\translation\staticfiles\translation_app\js\word_mappings.js (2332407 bytes)
Successfully updated static translations file
Log entry added to logs\translation_updates.log
Static file size: 7047204 bytes
Static translations file updated successfully
Loading translations...
Using BLEU score for translation quality evaluation
Cache cleared
Enabling Hugging Face integration for translations
Enabling attention mechanism for translations
Enabling BLEU score for translation quality evaluation
Temporarily enabling automatic processes for translation loading
Restored automatic processes setting to: True
Translations loaded successfully
Translation system optimization completed in 20.74 seconds!

2025-05-23 16:00:55,762 - optimize_script - INFO - Command additional output:
ERROR:root:Error in update_metrics command: 'charmap' codec can't encode character '\u2192' in position 25: character maps to <undefined>

2025-05-23 16:00:55,762 - optimize_script - INFO - Forcing a complete reload of translations from the database
2025-05-23 16:01:08,735 - optimize_script - INFO - Reload output:
Cache cleared
Translations reloaded successfully

2025-05-23 16:01:08,735 - optimize_script - INFO - Successfully reloaded translations from the database
2025-05-23 16:01:08,736 - optimize_script - INFO - Optimization completed successfully in 35.11 seconds
2025-05-23 16:01:08,737 - optimize_script - INFO - System optimization completed successfully
2025-05-23 16:01:08,737 - optimize_script - INFO - Updating static translations file
2025-05-23 16:01:08,737 - optimize_script - INFO - Updating static translations file
2025-05-23 16:01:19,268 - optimize_script - INFO - Static file update additional output:
2025-05-23 16:01:08,809 - update_static_script - INFO - Starting static file update at 2025-05-23 16:01:08
2025-05-23 16:01:08,809 - update_static_script - INFO - Maximum static file size: 5242880 bytes
2025-05-23 16:01:19,259 - update_static_script - INFO - Command output:
Updating static translations file...
Successfully exported 18622 of 18622 translations to C:\Users\<USER>\translation\staticfiles\translation_app\js\all_translations.js (3886868 bytes)
Word mappings exported to C:\Users\<USER>\translation\staticfiles\translation_app\js\word_mappings.js (2332407 bytes)
Successfully updated static translations file
Log entry added to logs\translation_updates.log
Static file size: 7047204 bytes
Static file is larger than the maximum allowed size (5242880 bytes). Consider using the --reduced option to create a smaller file.

2025-05-23 16:01:19,259 - update_static_script - INFO - Static file update completed successfully in 10.45 seconds
2025-05-23 16:01:19,259 - update_static_script - INFO - Static file size: 7047204 bytes
2025-05-23 16:01:19,259 - update_static_script - WARNING - Static file is larger than the maximum allowed size (5242880 bytes). Consider using the --reduced option to create a smaller file.
2025-05-23 16:01:19,259 - update_static_script - INFO - Static file update completed successfully

2025-05-23 16:01:19,268 - optimize_script - INFO - Static translations file updated successfully
2025-05-23 16:01:19,268 - optimize_script - INFO - Static translations file updated successfully
2025-05-23 16:01:19,268 - optimize_script - INFO - All processing tasks completed
2025-05-23 16:03:59,624 - optimize_script - INFO - Starting feedback processing
2025-05-23 16:03:59,624 - optimize_script - INFO - Processing pending feedback files
2025-05-23 16:04:00,188 - optimize_script - INFO - Successfully processed pending feedback
2025-05-23 16:04:00,188 - optimize_script - INFO - Running full system optimization
2025-05-23 16:04:00,188 - optimize_script - INFO - Starting optimization at 2025-05-23 16:04:00
2025-05-23 16:04:00,690 - optimize_script - INFO - Cleared Django cache to ensure fresh data
2025-05-23 16:04:00,690 - optimize_script - INFO - Skipping static file updates to prioritize database operations
2025-05-23 16:04:00,690 - optimize_script - INFO - Enabling Hugging Face integration for translations
2025-05-23 16:04:00,690 - optimize_script - INFO - Enabling attention mechanism for translations
2025-05-23 16:04:00,692 - optimize_script - INFO - Enabling BLEU score for translation quality evaluation
2025-05-23 16:04:12,763 - optimize_script - INFO - Command output:
Starting translation system optimization...
Optimizing database...
Optimizing SQLite database...
SQLite database optimized successfully
Updating metrics...
Starting metrics update...
Error updating metrics: 'charmap' codec can't encode character '\u2192' in position 25: character maps to <undefined>
Metrics updated successfully
Skipping static file update to prioritize database operations
Loading translations...
Using BLEU score for translation quality evaluation
Cache cleared
Enabling Hugging Face integration for translations
Enabling attention mechanism for translations
Enabling BLEU score for translation quality evaluation
Temporarily enabling automatic processes for translation loading
Restored automatic processes setting to: True
Translations loaded successfully
Translation system optimization completed in 11.12 seconds!

2025-05-23 16:04:12,763 - optimize_script - INFO - Command additional output:
ERROR:root:Error in update_metrics command: 'charmap' codec can't encode character '\u2192' in position 25: character maps to <undefined>

2025-05-23 16:04:12,763 - optimize_script - INFO - Forcing a complete reload of translations from the database
2025-05-23 16:04:24,585 - optimize_script - INFO - Reload output:
Cache cleared
Translations reloaded successfully

2025-05-23 16:04:24,585 - optimize_script - INFO - Successfully reloaded translations from the database
2025-05-23 16:04:24,585 - optimize_script - INFO - Optimization completed successfully in 24.40 seconds
2025-05-23 16:04:24,585 - optimize_script - INFO - System optimization completed successfully
2025-05-23 16:04:24,585 - optimize_script - INFO - All tasks completed
2025-05-23 16:04:36,462 - optimize_script - INFO - Processing all translations, feedback, and improvements
2025-05-23 16:04:36,462 - optimize_script - INFO - Starting feedback processing
2025-05-23 16:04:36,462 - optimize_script - INFO - Processing pending feedback files
2025-05-23 16:04:37,012 - optimize_script - INFO - Successfully processed pending feedback
2025-05-23 16:04:37,012 - optimize_script - INFO - Optimizing translations using attention mechanism
2025-05-23 16:04:37,012 - optimize_script - INFO - Optimizing translations using attention mechanism
2025-05-23 16:04:37,060 - optimize_script - ERROR - Optimization errors:
  File "C:\Users\<USER>\translation\temp_optimize_translations.py", line 95
    translation=best_translation,
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
SyntaxError: keyword argument repeated: translation

2025-05-23 16:04:37,060 - optimize_script - WARNING - Failed to optimize translations using attention mechanism
2025-05-23 16:04:37,060 - optimize_script - INFO - Running full system optimization
2025-05-23 16:04:37,061 - optimize_script - INFO - Starting optimization at 2025-05-23 16:04:37
2025-05-23 16:04:37,579 - optimize_script - INFO - Cleared Django cache to ensure fresh data
2025-05-23 16:04:37,579 - optimize_script - INFO - Will update static files with latest translations
2025-05-23 16:04:37,579 - optimize_script - INFO - Enabling Hugging Face integration for translations
2025-05-23 16:04:37,579 - optimize_script - INFO - Enabling attention mechanism for translations
2025-05-23 16:04:37,579 - optimize_script - INFO - Enabling BLEU score for translation quality evaluation
2025-05-23 16:04:59,105 - optimize_script - INFO - Command output:
Starting translation system optimization...
Optimizing database...
Optimizing SQLite database...
SQLite database optimized successfully
Updating metrics...
Starting metrics update...
Error updating metrics: 'charmap' codec can't encode character '\u2192' in position 25: character maps to <undefined>
Metrics updated successfully
Updating static translations file...
Updating static translations file...
Successfully exported 18622 of 18622 translations to C:\Users\<USER>\translation\staticfiles\translation_app\js\all_translations.js (3886868 bytes)
Word mappings exported to C:\Users\<USER>\translation\staticfiles\translation_app\js\word_mappings.js (2332407 bytes)
Successfully updated static translations file
Log entry added to logs\translation_updates.log
Static file size: 7047204 bytes
Static translations file updated successfully
Loading translations...
Using BLEU score for translation quality evaluation
Cache cleared
Enabling Hugging Face integration for translations
Enabling attention mechanism for translations
Enabling BLEU score for translation quality evaluation
Temporarily enabling automatic processes for translation loading
Restored automatic processes setting to: True
Translations loaded successfully
Translation system optimization completed in 20.43 seconds!

2025-05-23 16:04:59,105 - optimize_script - INFO - Command additional output:
ERROR:root:Error in update_metrics command: 'charmap' codec can't encode character '\u2192' in position 25: character maps to <undefined>

2025-05-23 16:04:59,105 - optimize_script - INFO - Forcing a complete reload of translations from the database
2025-05-23 16:05:11,113 - optimize_script - INFO - Reload output:
Cache cleared
Translations reloaded successfully

2025-05-23 16:05:11,113 - optimize_script - INFO - Successfully reloaded translations from the database
2025-05-23 16:05:11,113 - optimize_script - INFO - Optimization completed successfully in 34.05 seconds
2025-05-23 16:05:11,113 - optimize_script - INFO - System optimization completed successfully
2025-05-23 16:05:11,113 - optimize_script - INFO - Updating static translations file
2025-05-23 16:05:11,113 - optimize_script - INFO - Updating static translations file
2025-05-23 16:05:21,007 - optimize_script - INFO - Static file update additional output:
2025-05-23 16:05:11,178 - update_static_script - INFO - Starting static file update at 2025-05-23 16:05:11
2025-05-23 16:05:11,178 - update_static_script - INFO - Maximum static file size: 5242880 bytes
2025-05-23 16:05:20,998 - update_static_script - INFO - Command output:
Updating static translations file...
Successfully exported 18622 of 18622 translations to C:\Users\<USER>\translation\staticfiles\translation_app\js\all_translations.js (3886868 bytes)
Word mappings exported to C:\Users\<USER>\translation\staticfiles\translation_app\js\word_mappings.js (2332407 bytes)
Successfully updated static translations file
Log entry added to logs\translation_updates.log
Static file size: 7047204 bytes
Static file is larger than the maximum allowed size (5242880 bytes). Consider using the --reduced option to create a smaller file.

2025-05-23 16:05:20,998 - update_static_script - INFO - Static file update completed successfully in 9.82 seconds
2025-05-23 16:05:20,998 - update_static_script - INFO - Static file size: 7047204 bytes
2025-05-23 16:05:20,998 - update_static_script - WARNING - Static file is larger than the maximum allowed size (5242880 bytes). Consider using the --reduced option to create a smaller file.
2025-05-23 16:05:20,998 - update_static_script - INFO - Static file update completed successfully

2025-05-23 16:05:21,007 - optimize_script - INFO - Static translations file updated successfully
2025-05-23 16:05:21,007 - optimize_script - INFO - Static translations file updated successfully
2025-05-23 16:05:21,007 - optimize_script - INFO - All processing tasks completed
2025-05-23 16:06:31,831 - optimize_script - INFO - Processing all translations, feedback, and improvements
2025-05-23 16:06:31,831 - optimize_script - INFO - Starting feedback processing
2025-05-23 16:06:31,831 - optimize_script - INFO - Processing pending feedback files
2025-05-23 16:06:32,387 - optimize_script - INFO - Successfully processed pending feedback
2025-05-23 16:06:32,387 - optimize_script - INFO - Optimizing translations using attention mechanism
2025-05-23 16:06:32,387 - optimize_script - INFO - Optimizing translations using attention mechanism
2025-05-23 16:06:33,052 - optimize_script - ERROR - Optimization errors:
2025-05-23 16:06:32,848 - translation_app.pattern_learning - INFO - Loaded patterns from data\learned_patterns.json
2025-05-23 16:06:32,848 - translation_app.pattern_learning - INFO - TGL patterns:
2025-05-23 16:06:32,848 - translation_app.pattern_learning - INFO -   Word patterns: 53
2025-05-23 16:06:32,848 - translation_app.pattern_learning - INFO -   Phrase patterns: 4
2025-05-23 16:06:32,848 - translation_app.pattern_learning - INFO -   Grammar patterns: 8
2025-05-23 16:06:32,848 - translation_app.pattern_learning - INFO -   Substitution patterns: 0
2025-05-23 16:06:32,848 - translation_app.pattern_learning - INFO - TED patterns:
2025-05-23 16:06:32,848 - translation_app.pattern_learning - INFO -   Word patterns: 0
2025-05-23 16:06:32,848 - translation_app.pattern_learning - INFO -   Phrase patterns: 0
2025-05-23 16:06:32,848 - translation_app.pattern_learning - INFO -   Grammar patterns: 0
2025-05-23 16:06:32,848 - translation_app.pattern_learning - INFO -   Substitution patterns: 0
2025-05-23 16:06:32,917 - root - INFO - Initializing attention mechanism
2025-05-23 16:06:32,917 - optimize_translations - INFO - Attention mechanism enabled
2025-05-23 16:06:32,917 - root - INFO - Initializing attention mechanism
2025-05-23 16:06:32,917 - optimize_translations - INFO - Attention mechanism imported successfully
2025-05-23 16:06:32,917 - translation_app.services - INFO - Automatic loading disabled - only loading minimal translations
2025-05-23 16:06:32,917 - translation_app.services - INFO - Loading minimal translations (absolute minimum)...
2025-05-23 16:06:32,919 - translation_app.services - INFO - Loaded 5 minimal Tagalog to Teduray translations
2025-05-23 16:06:32,919 - translation_app.services - INFO - Loaded 5 minimal Teduray to Tagalog translations
2025-05-23 16:06:32,919 - translation_app.services - INFO - Translations will be loaded on demand or by scheduled task
Traceback (most recent call last):
  File "C:\Users\<USER>\translation\temp_optimize_translations.py", line 41, in <module>
    low_quality_translations = ComprehensiveTranslation.objects.filter(confidence_score__lt=0.7).order_by('confidence_score')[:100]
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\site-packages\django\db\models\query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\site-packages\django\db\models\query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\site-packages\django\db\models\sql\query.py", line 1545, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\site-packages\django\db\models\sql\query.py", line 1576, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\site-packages\django\db\models\sql\query.py", line 1426, in build_filter
    lookups, parts, reffed_expression = self.solve_lookup_type(arg, summarize)
                                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\site-packages\django\db\models\sql\query.py", line 1236, in solve_lookup_type
    _, field, _, lookup_parts = self.names_to_path(lookup_splitted, self.get_meta())
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\site-packages\django\db\models\sql\query.py", line 1724, in names_to_path
    raise FieldError(
django.core.exceptions.FieldError: Cannot resolve keyword 'confidence_score' into field. Choices are: attention_data, base_word, created_at, created_by, created_by_id, cultural_notes, examples, id, notes, part_of_speech, ratings, related_forms, source_language, source_language_id, target_language, target_language_id, translation, updated_at, versions

2025-05-23 16:06:33,052 - optimize_script - WARNING - Failed to optimize translations using attention mechanism
2025-05-23 16:06:33,056 - optimize_script - INFO - Running full system optimization
2025-05-23 16:06:33,056 - optimize_script - INFO - Starting optimization at 2025-05-23 16:06:33
2025-05-23 16:06:33,643 - optimize_script - INFO - Cleared Django cache to ensure fresh data
2025-05-23 16:06:33,643 - optimize_script - INFO - Will update static files with latest translations
2025-05-23 16:06:33,643 - optimize_script - INFO - Enabling Hugging Face integration for translations
2025-05-23 16:06:33,643 - optimize_script - INFO - Enabling attention mechanism for translations
2025-05-23 16:06:33,643 - optimize_script - INFO - Enabling BLEU score for translation quality evaluation
2025-05-23 16:06:54,950 - optimize_script - INFO - Command output:
Starting translation system optimization...
Optimizing database...
Optimizing SQLite database...
SQLite database optimized successfully
Updating metrics...
Starting metrics update...
Error updating metrics: 'charmap' codec can't encode character '\u2192' in position 25: character maps to <undefined>
Metrics updated successfully
Updating static translations file...
Updating static translations file...
Successfully exported 18622 of 18622 translations to C:\Users\<USER>\translation\staticfiles\translation_app\js\all_translations.js (3886868 bytes)
Word mappings exported to C:\Users\<USER>\translation\staticfiles\translation_app\js\word_mappings.js (2332407 bytes)
Successfully updated static translations file
Log entry added to logs\translation_updates.log
Static file size: 7047204 bytes
Static translations file updated successfully
Loading translations...
Using BLEU score for translation quality evaluation
Cache cleared
Enabling Hugging Face integration for translations
Enabling attention mechanism for translations
Enabling BLEU score for translation quality evaluation
Temporarily enabling automatic processes for translation loading
Restored automatic processes setting to: True
Translations loaded successfully
Translation system optimization completed in 20.29 seconds!

2025-05-23 16:06:54,950 - optimize_script - INFO - Command additional output:
ERROR:root:Error in update_metrics command: 'charmap' codec can't encode character '\u2192' in position 25: character maps to <undefined>

2025-05-23 16:06:54,950 - optimize_script - INFO - Forcing a complete reload of translations from the database
2025-05-23 16:07:07,434 - optimize_script - INFO - Reload output:
Cache cleared
Translations reloaded successfully

2025-05-23 16:07:07,434 - optimize_script - INFO - Successfully reloaded translations from the database
2025-05-23 16:07:07,434 - optimize_script - INFO - Optimization completed successfully in 34.38 seconds
2025-05-23 16:07:07,434 - optimize_script - INFO - System optimization completed successfully
2025-05-23 16:07:07,434 - optimize_script - INFO - Updating static translations file
2025-05-23 16:07:07,434 - optimize_script - INFO - Updating static translations file
2025-05-23 16:07:17,559 - optimize_script - INFO - Static file update additional output:
2025-05-23 16:07:07,497 - update_static_script - INFO - Starting static file update at 2025-05-23 16:07:07
2025-05-23 16:07:07,497 - update_static_script - INFO - Maximum static file size: 5242880 bytes
2025-05-23 16:07:17,559 - update_static_script - INFO - Command output:
Updating static translations file...
Successfully exported 18622 of 18622 translations to C:\Users\<USER>\translation\staticfiles\translation_app\js\all_translations.js (3886868 bytes)
Word mappings exported to C:\Users\<USER>\translation\staticfiles\translation_app\js\word_mappings.js (2332407 bytes)
Successfully updated static translations file
Log entry added to logs\translation_updates.log
Static file size: 7047204 bytes
Static file is larger than the maximum allowed size (5242880 bytes). Consider using the --reduced option to create a smaller file.

2025-05-23 16:07:17,559 - update_static_script - INFO - Static file update completed successfully in 10.06 seconds
2025-05-23 16:07:17,559 - update_static_script - INFO - Static file size: 7047204 bytes
2025-05-23 16:07:17,559 - update_static_script - WARNING - Static file is larger than the maximum allowed size (5242880 bytes). Consider using the --reduced option to create a smaller file.
2025-05-23 16:07:17,559 - update_static_script - INFO - Static file update completed successfully

2025-05-23 16:07:17,572 - optimize_script - INFO - Static translations file updated successfully
2025-05-23 16:07:17,572 - optimize_script - INFO - Static translations file updated successfully
2025-05-23 16:07:17,572 - optimize_script - INFO - All processing tasks completed
